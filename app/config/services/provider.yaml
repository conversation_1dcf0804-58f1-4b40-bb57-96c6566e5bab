services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: true

    AppBundle\Provider\:
        resource: '../../../src/AppBundle/Provider/*'

    AppBundle\Provider\Matcher\:
        resource: '../../../src/AppBundle/Provider/Matcher/*'
        tags:
            - { name: "uppler.provider_matcher"}
        bind:
            $promotionStateWorkflow: '@state_machine.promotion_state'

    AppBundle\Provider\MessageQueueProvider:
        arguments:
            $receivers: !tagged_locator { tag: messenger.receiver }

    # The following services has to be public since they are used by almost all the controllers
    AppBundle\Provider\Security\AuthenticatedProvider:
        public: true

    AppBundle\Provider\Security\AccessProvider:
        public: true
        bind:
            $accesses: '%uppler_security.access%'

    AppBundle\Provider\ConfigurationProvider:
        public: true
        bind:
            $locale: '%locale%'
            $allowedLanguages: '%allowed_languages%'
        tags:
            - { name: uppler.cache_decorator, class: AppBundle\Cache\Decorator\Provider\ConfigurationProviderDecorator }


    # All the following should not be public
    AppBundle\Provider\Contract\ContractProvider:
        lazy: true
        public: false
        tags:
            - { name: uppler.cache_decorator, class: AppBundle\Cache\Decorator\Provider\Contract\ContractProviderDecorator }

    AppBundle\Provider\Contract\ConditionProvider:
        lazy: true
        public: false
        tags:
            - { name: uppler.cache_decorator, class: AppBundle\Cache\Decorator\Provider\Contract\ConditionProviderDecorator }

    AppBundle\Provider\Promotion\PromotionProvider:
        public: false
        arguments:
            $promotionModels: '%uppler.promotion_models%'
        tags:
            - { name: uppler.cache_decorator, class: AppBundle\Cache\Decorator\Provider\Promotion\PromotionProviderDecorator }

    AppBundle\Provider\Promotion\ConfigurationProvider:
        public: false
        bind:
            $promotionTypeConfiguration: '%uppler.promotion_types%'
            $promotionModelConfiguration: '%uppler.promotion_models%'

    AppBundle\Provider\Shop\TaxonProvider:
        public: false
        bind:
            $localeParameter: '%locale%'

    AppBundle\Provider\TranslationProvider:
        public: false
        bind:
            $localeParameter: '%locale%'

    AppBundle\Provider\Cms\DynamicFormProvider:
        public: false
        bind:
            $entityDynamicForms: '%uppler_cms.entity_dynamic_form%'

    AppBundle\Provider\DataExporter\DataExporterProvider:
        public: false

    AppBundle\Provider\DataExporter\DataExporterConfigurationProvider:
        public: false
        bind:
            $exporterConfigurations: "%uppler.data_exporter.definitions%"

    AppBundle\Provider\Shop\DisputeProvider:
        public: false

    AppBundle\Provider\DataBuilder\DataBuilderProvider:
        public: false
        bind:
            $configurations: "%uppler.data_builder.configurations%"

    AppBundle\Provider\QueueProvider:
        public: false
        bind:
            $queueables: '%uppler_core.queueables%'

    AppBundle\Provider\Cms\PageProvider:
        public: false
        tags:
            - { name: uppler.cache_decorator, class: AppBundle\Cache\Decorator\Provider\Cms\PageProviderDecorator }

    AppBundle\Provider\Security\ScopeProvider:
        public: false
        bind:
            $scopes: '%uppler_core.scopes%'

    app.provider.available.owner:
        class: AppBundle\Provider\Available\OwnerProvider
        arguments: ['@security.token_storage']


    AppBundle\Provider\Bank\Provider:
        arguments:
            - '@doctrine.orm.entity_manager'
            - '@session'
            - '@security.token_storage'
            - '@AppBundle\Security\UrlToken\Factory'
            - '@translator'
            - '@AppBundle\FileHandler\FileUploader'
            - '@AppBundle\Provider\ConfigurationProvider'
            - '@event_dispatcher'
            - '@app.resource_repository.user'
            - '@app.resource_repository.company'
            - '@app.resource_repository.payment'
            - '@app.resource_repository.bank_configuration'
            - '@app.resource_repository.currency'
            - '@AppBundle\Context\CurrencyContext'
            - '@AppBundle\Resolver\Shop\TaxRateResolver'
            - '@AppBundle\Twig\Extension\MoneyExtension'
            - '@monolog.logger.payment'
            - '%uppler_bank.owner_id%'
            - '%uppler_bank.wallet_owner_id%'
            - '%uppler_bank.referer%'
            - '%uppler_bank.payer%'

    AppBundle\Provider\Security\UserProvider:
        parent: fos_user.user_provider.username_email
        public: true
        autowire: true
        autoconfigure: false
        bind:
            $allowAdmin: false
            $fosUserResettingTtl: '%fos_user.resetting.retry_ttl%'

    uppler_core.security.admin_user_provider:
        parent: AppBundle\Provider\Security\UserProvider
        public: true
        autowire: true
        autoconfigure: false
        bind:
            $allowAdmin: true

    # --------------------------------------------------------
    #                           SEARCH
    # --------------------------------------------------------
    AppBundle\Provider\Search\AbstractSearchProvider:
        abstract: true
        public: false
        arguments:
            - '@AppBundle\Parser\SpreadsheetParser'

    AppBundle\Provider\Search\ProductProvider:
        parent: AppBundle\Provider\Search\AbstractSearchProvider
        public: true
        autowire: true
        autoconfigure: false
        bind:
            $searchContextFactory: '@uppler_search.context.factory.product'
            $searchFormFactory: '@app.form.factory.product_search'
        tags:
            - { name: uppler.cache_decorator, class: AppBundle\Cache\Decorator\Provider\Search\ProductProviderDecorator }

    AppBundle\Provider\Search\OfferProvider:
        parent: AppBundle\Provider\Search\ProductProvider
        public: true
        autowire: true
        autoconfigure: false
        bind:
            $searchContextFactory: '@uppler_search.context.factory.offer'
            $searchFormFactory: '@app.form.factory.offer_search'

    AppBundle\Provider\Search\CompanyProvider:
        parent: AppBundle\Provider\Search\AbstractSearchProvider
        public: true
        autowire: false
        autoconfigure: false
        arguments:
            - '@event_dispatcher'
            - '@security.token_storage'
            - '@AppBundle\Elastica\Repository\CompanyRepository'
            - '@liip_imagine.cache.manager'
            - '@translator'
            - '@router'
            - '@app.resource_repository.relationship'
            - '@app.resource_repository.category'
            - '@app.resource_repository.country'
            - '@app.resource_repository.province'
            - '@app.resource_repository.type'
            - '@uppler_search.context.factory.company'
            - '@app.form.factory.company_search'
            - '%liip_imagine.default_image%'
            - '@AppBundle\Provider\Company\CompanyRestrictionProvider'
            - '@AppBundle\Provider\Company\CompanyProvider'
            - '@AppBundle\Provider\Contract\ContractProvider'

    AppBundle\Provider\Company\VideoProvider:
        arguments:
            - '@app.resource_repository.video'
            - '@doctrine.orm.entity_manager'
            - '%uppler_core.video_referables%'

    AppBundle\Provider\FileHistoryProvider:
        arguments:
            - '@doctrine.orm.entity_manager'
            - '@=container.hasParameter(''amazon.s3.backup.base_url'') ? parameter(''amazon.s3.backup.base_url'') : null'

    AppBundle\Provider\FileProvider:
        autowire: true
        bind:
            $owners: '%uppler_core.file_owner%'

    AppBundle\Provider\Captcha\CaptchaProvider:
        arguments:
            - '@request_stack'
            - '@monolog.logger'
            - '%uppler_captcha%'

    AppBundle\Provider\Company\ReportProvider:
        arguments:
            - '@doctrine.orm.entity_manager'
            - '@security.token_storage'
            - '@app.resource_repository.report'
            - '%uppler_core.reportables%'
            - '%uppler_core.reportable_templates%'



    AppBundle\Provider\Email\EmailStatProvider:
        arguments:
            - '@monolog.logger'
            - '@doctrine.orm.entity_manager'
            - '@session'
            - '@AppBundle\Provider\ConfigurationProvider'
            - '@app.resource_repository.email_stat'

    AppBundle\Provider\MappingProvider:
        arguments:
            - '@app.resource_repository.mapping'
            - '@doctrine.orm.entity_manager'
            - '%uppler_core.mappables%'

    AppBundle\Provider\Tracking\TrackingProvider:
        arguments:
            - '@AppBundle\Provider\ConfigurationProvider'
            - '%uppler_core.tracking%'

    AppBundle\Provider\Cms\BlockProvider:
        bind:
            $frontendRouter: '@AppBundle\Router\FrontendRouter'
            $parametersAvailable: '%uppler_cms.block.parameters_available%'
            $routeParametersAvailable: '%uppler_cms.block.route_parameters_available%'
            $widgetTemplate: '%uppler_cms.block.widget_template%'
            $buttonTemplate: '%uppler_cms.block.button_template%'
            $buttonFileTemplate: '%uppler_cms.block.button_file_template%'
            $imageTemplate: '%uppler_cms.block.image_template%'
            $prettyPrintTemplates: '%uppler_cms.block.pretty_print_templates%'
            $menuTemplate: '%uppler_cms.block.menu_template%'
            $searchTemplate: '%uppler_cms.block.search_template%'
            $cartTemplate: '%uppler_cms.block.cart_template%'
            $orderTemplate: '%uppler_cms.block.order_template%'
            $priceSwitchTemplate: '%uppler_cms.block.price_switch_template%'
        tags:
            - { name: uppler.cache_decorator, class: AppBundle\Cache\Decorator\Provider\Cms\BlockProviderDecorator }

    AppBundle\Provider\Cms\EmailTemplateProvider:
        arguments:
            - '@app.resource_repository.email_template'
            - '@AppBundle\Provider\Cms\BlockProvider'
            - '%uppler_cms.email.type%'
            - '@AppBundle\Provider\ConfigurationProvider'
            - '@AppBundle\Provider\Cms\PageProvider'

    AppBundle\Provider\Company\CompanyProvider:
        autowire: true
        bind:
            $roles: '%uppler_company.roles%'
        tags:
            -
                name: uppler.cache_decorator
                class: AppBundle\Cache\Decorator\Provider\Company\CompanyProviderDecorator

    AppBundle\Provider\DataBuilder\TriggerProvider:
        arguments:
            - '@event_dispatcher'
            - "%uppler.data_builder.configurations%"

    AppBundle\Provider\Like\LikeProvider:
        arguments:
            - '@doctrine.orm.entity_manager'
            - '@security.token_storage'
            - '@app.resource_repository.like'
            - '%uppler_like.likables%'

    AppBundle\Provider\Message\ReferenceProvider:
        arguments:
            - '@doctrine.orm.entity_manager'
            - '%uppler_message.references%'

    AppBundle\Provider\Onboarding\OnboardingProvider:
        arguments:
            - "@AppBundle\\Provider\\Security\\AuthenticatedProvider"
            - '@event_dispatcher'
            - '@app.resource_repository.onboarding'
            - '@app.resource_repository.onboarding_type'
            - '@doctrine.orm.entity_manager'
            - "@AppBundle\\Context\\OnboardingContext"
            - '%uppler_onboarding.default_route%'
            - '%uppler_onboarding.fields%'
            - '@AppBundle\Provider\ConfigurationProvider'
            - "@AppBundle\\Provider\\Relationship\\RelationshipProvider"
            - '@router'

    AppBundle\Provider\Score\ScoreProvider:
        arguments:
            - '@app.resource_repository.score_configuration'
            - '@app.resource_repository.score'
            - '@doctrine.orm.entity_manager'
            - "%uppler_score.scorables%"

    AppBundle\Provider\Security\AuthorizeProvider:
        arguments:
            - "@app.resource_repository.authorization"
            - '@AppBundle\Provider\ConfigurationProvider'
            - "@doctrine.orm.entity_manager"
            - "@request_stack"
            - "@router"
            - "uppler_security_unsubscribe"
            - "uppler_security_unsubscribe_confirm"
            - "uppler_security_unsubscribe_cancel"
            - "%uppler_security.authorizers%"
            - "%uppler_security.default_url%"
            - "%uppler_security.default_scheme%"
    AppBundle\Provider\Shop\LoyaltyProgramProvider:
        arguments:
            - '@doctrine.orm.entity_manager'
            - '@translator'
            - '@AppBundle\Provider\Review\ReviewProvider'
            - '%uppler_loyalty_program_configuration%'


    # -------------------------------------------------------------
    #                           STATISTIC
    # _____________________________________________________________
    AppBundle\Provider\Statistic\DataProvider:
        arguments:
            - '@app.resource_repository.data'
            - '@doctrine.orm.entity_manager'
            - '%uppler_statistics.classes.owner%'

    AppBundle\Provider\Statistic\StatisticProvider:
        arguments:
            - '@app.resource_repository.statistic'
            - '@doctrine.orm.entity_manager'
            - '%uppler_statistics.classes.owner%'

    AppBundle\Provider\Widget\WidgetProvider:
        bind:
            $templates: '%uppler_widget.templates%'
            $automaticTypes: '%uppler_widget.automatic_type%'
            $locations: '%uppler_widget.emplacements%'

    AppBundle\Provider\Widget\ItemProvider:
        arguments:
            - '@doctrine.orm.entity_manager'
            - '%uppler_widget.items%'
            - '%uppler_widget.automatic_type%'

    AppBundle\Provider\Review\ReviewProvider:
        arguments:
            - '@doctrine.orm.entity_manager'
            - '@app.resource_repository.review'
            - '@app.resource_repository.review_configuration'
            - '@event_dispatcher'
            - '%uppler_core.referers%'
            - '%uppler_core.reviewables%'

    AppBundle\Provider\Connector\ConnectorFlowProvider:
        arguments:
            $flows: !tagged_locator { tag: connector_flow, default_index_method: getIndex }

    AppBundle\Provider\Connector\ConnectorProvider:
        arguments:
            - '@app.resource_repository.connector'
            - '@app.resource_repository.connector_type'
            - '@AppBundle\Provider\Security\AuthenticatedProvider'
            - '%uppler.connector_types%'

    AppBundle\Provider\Contract\ConfigurationProvider:
        arguments:
            - '@doctrine.orm.entity_manager'
            - '@AppBundle\Provider\ConfigurationProvider'
            - '%uppler.contract_types%'

    AppBundle\Provider\GentleForce\ForwardedIpProvider:
        tags:
            - { name: maba_gentle_force.identifier_provider, identifierType: forwarded_ip }

    AppBundle\Provider\Search\ElasticaProvider:
        arguments:
            - '@fos_elastica.pager_provider_registry'
            - '@fos_elastica.persister_registry'
            - '@fos_elastica.repository_manager'
            - '@fos_elastica.config_source.container'
            - '@app.transformer.model_to_elastica'
            - '@doctrine.orm.entity_manager'

    AppBundle\Provider\Company\CompanyRestrictionProvider:
        tags:
            - { name: uppler.cache_decorator, class: AppBundle\Cache\Decorator\Provider\Company\CompanyRestrictionProviderDecorator }

    AppBundle\Provider\Duplicate\DuplicateProvider:
        bind:
            $duplicateCheckables: '%uppler_duplicate.checkables%'

    AppBundle\Provider\Company\AccountProvider:
        tags:
            - { name: uppler.cache_decorator, class: AppBundle\Cache\Decorator\Provider\Company\AccountProviderDecorator }

    AppBundle\Provider\Matcher\AbstractMatcherProvider:

    AppBundle\Provider\Matcher\ProductMatcherProvider:
        parent: AppBundle\Provider\Matcher\AbstractMatcherProvider
        public: true
        autowire: true
        autoconfigure: false
        bind:
            $promotionStateWorkflow: '@state_machine.promotion_state'
        tags:
            - { name: uppler.provider_matcher, class: AppBundle\Provider\Matcher\ProductMatcherProvider }

    AppBundle\Provider\Shop\PricingProvider:
        tags:
            - { name: uppler.cache_decorator, class: AppBundle\Cache\Decorator\Provider\Shop\PricingProviderDecorator }

    AppBundle\Provider\CurrencySubstitutionProvider:
        tags:
            - { name: uppler.cache_decorator, class: AppBundle\Cache\Decorator\Provider\Shop\CurrencySubstitutionProviderDecorator }

    AppBundle\Provider\Matcher\CompanyMatcherProvider:
        tags:
            - { name: uppler.cache_decorator, class: AppBundle\Cache\Decorator\Provider\Matcher\CompanyMatcherProviderDecorator }
            - { name: uppler.provider_matcher }

    AppBundle\Provider\Matcher\OrderMatcherProvider:
        tags:
            - { name: uppler.cache_decorator, class: AppBundle\Cache\Decorator\Provider\Matcher\OrderMatcherProviderDecorator }
            - { name: uppler.provider_matcher }

    AppBundle\Provider\Shop\ShippingMethodProvider:
        tags:
            - { name: uppler.cache_decorator, class: AppBundle\Cache\Decorator\Provider\Shop\ShippingMethodProviderDecorator }

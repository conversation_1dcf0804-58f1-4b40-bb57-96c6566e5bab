services:
    _defaults:
        autowire: true
        autoconfigure: true

    # -------------------------------------------------------------
    #                           GLOBAL
    # _____________________________________________________________
    AppBundle\EventListener\RedirectExceptionListener:
        arguments:
            - '@router'
            - '@session'
            - '@translator'
        tags:
            - {name: kernel.event_listener, event: kernel.exception, method: onKernelException}

    AppBundle\EventListener\StaticLoaderListener:
        tags:
            - {name: kernel.event_listener, event: kernel.request, method: onKernelRequest}
            - {name: kernel.event_listener, event: console.command, method: onConsoleCommand}
        arguments:
            - '%app.resources.mapping.class%'
            - '%app.resources.mapping.name%'

    AppBundle\EventListener\InteractiveRequestListener:
        tags:
            - { name: kernel.event_listener, event: kernel.exception, method: onKernelException, priority: 128 }

    AppBundle\EventListener\RequestListener:
        tags:
            - {name: kernel.event_listener, event: kernel.request, method: onKernelRequest}

    AppBundle\EventListener\EventListenerDisabler:
        arguments:
            - '@doctrine.orm.entity_manager'

    # -------------------------------------------------------------
    #                           AVAILABLE
    # _____________________________________________________________
    AppBundle\EventListener\Available\AvailableListener:
        arguments:
            - '@app.provider.available.owner'
            - '@AppBundle\Provider\Available\HiddenProvider'

    # -------------------------------------------------------------
    #                           BANK
    # _____________________________________________________________
    AppBundle\EventListener\Bank\RenewListener:
        tags:
            - {name: kernel.event_listener, event: uppler_bank.renew, method: renew}
        arguments:
            - '@AppBundle\Provider\Bank\PaymentProvider'

    AppBundle\EventListener\Bank\TriggerListener:
        tags:
            - {name: kernel.event_listener, event: uppler_bank.trigger, method: trigger}
            - {name: kernel.event_listener, event: uppler_bank.trigger.create, method: triggerCreate}
            - {name: kernel.event_listener, event: app.api_operator.order.post_create, method: triggerCreate}
            - {name: kernel.event_listener, event: data_importer.order.post_create, method: triggerCreate}
            - {name: kernel.event_listener, event: app.api_seller.order.post_create, method: triggerCreate}
            - {name: kernel.event_listener, event: uppler_bank.trigger.transaction, method: triggerTransaction}
            - {name: kernel.event_listener, event: uppler_bank.trigger.charge, method: triggerCharge}
            - {name: kernel.event_listener, event: uppler_bank.trigger.invoice, method: triggerInvoice, priority: 5}
            - {name: kernel.event_listener, event: uppler_bank.trigger.invoice_payment, method: triggerInvoicePayment}
            - {name: kernel.event_listener, event: uppler_bank.trigger.refund, method: triggerRefund}
            - {name: kernel.event_listener, event: uppler_bank.trigger.credit, method: triggerCredit}
            - {name: kernel.event_listener, event: uppler.order.post_clear, method: triggerInsurance}
            - {name: kernel.event_listener, event: uppler.cart.post_payment, method: trigger, priority: -10}
            - {name: kernel.event_listener, event: uppler.cart.post_confirm, method: trigger, priority: -10}
            - {name: kernel.event_listener, event: uppler.order.state_updated, method: trigger, priority: 10}
            - {name: kernel.event_listener, event: uppler.order.post_create_supplier, method: trigger}
            - {name: kernel.event_listener, event: uppler.subscription.pre_update, method: trigger, priority: -10}
            - {name: kernel.event_listener, event: uppler.subscription.post_update, method: trigger, priority: -10}

    AppBundle\EventListener\ImageManagerListener:
        arguments:
            - '@liip_imagine.cache.manager'
        tags:
            - {name: kernel.event_listener, event: kernel.terminate, method: onTerminate }

    # -------------------------------------------------------------
    #                           CORE
    # _____________________________________________________________
    AppBundle\EventListener\TrackingListener:
        tags:
            - {name: kernel.event_listener, event: kernel.response, method: onKernelResponse}
        arguments:
            - '@twig'
            - '@AppBundle\Twig\Extension\TrackingExtension'
            - '@AppBundle\Provider\Tracking\TrackingProvider'

    AppBundle\EventListener\ConsoleExceptionListener:
        tags:
            - {name: kernel.event_listener, event: console.exception}
        arguments:
            - '@monolog.logger'

    AppBundle\EventListener\ContractListener:
        tags:
            - {name: kernel.event_listener, event: app.front_office.contract.pre_create, method: processNumber}
            - {name: kernel.event_listener, event: app.back_office.contract.pre_create, method: processNumber}
            - {name: kernel.event_listener, event: app.api_operator.contract.pre_create, method: processNumber}
            - {name: kernel.event_listener, event: app.api_seller.contract.pre_create, method: processNumber}
            - {name: kernel.event_listener, event: app.contract_process.contract.pre_renew, method: processNumber}
            - {name: kernel.event_listener, event: data_importer.contract.pre_create, method: processNumber}
            - {name: kernel.event_listener, event: app.front_office.contract.post_create, method: processContract}
            - {name: kernel.event_listener, event: app.front_office.contract.post_update, method: processContract}
            - {name: kernel.event_listener, event: app.api_seller.contract.post_create, method: processContract}
            - {name: kernel.event_listener, event: app.api_seller.contract.post_update, method: processContract}
            - {name: kernel.event_listener, event: uppler.contract.update_state, method: processContract}

    AppBundle\EventListener\ImageUploadListener:
        tags:
            - {name: kernel.event_listener, event: app.front_office.image.pre_create, method: onUploadImage}
            - {name: kernel.event_listener, event: app.front_office.image.pre_update, method: onUploadImage}
            - {name: kernel.event_listener, event: oneup_uploader.post_persist.avatars, method: onUpload}
            - {name: kernel.event_listener, event: oneup_uploader.post_persist.trading_area, method: onUpload}
            - {name: kernel.event_listener, event: oneup_uploader.post_persist.cover, method: onUpload}
            - {name: kernel.event_listener, event: oneup_uploader.post_persist.image, method: onUpload}
            - {name: kernel.event_listener, event: oneup_uploader.post_persist.uploads, method: onUpload}
            - {name: kernel.event_listener, event: oneup_uploader.post_persist.product_list, method: onUpload}
            - {name: kernel.event_listener, event: oneup_uploader.post_persist.import, method: onUpload}
            - {name: kernel.event_listener, event: oneup_uploader.post_persist.certification, method: onUpload}
            - {name: kernel.event_listener, event: oneup_uploader.post_persist.dynamic_field_image, method: onUpload}
        arguments:
            - '@AppBundle\FileHandler\ImageUploader'
            - '@AppBundle\Provider\Company\CompanyProvider'
            - '@AppBundle\Provider\Image\ImageProvider'
            - '@AppBundle\Provider\Security\AuthenticatedProvider'
            - '@liip_imagine.cache.manager'
            - '@event_dispatcher'
            - '@doctrine.orm.entity_manager'
            - '@app.filesystem.image'

    AppBundle\EventListener\FileUploadListener:
        tags:
            - {name: kernel.event_listener, event: oneup_uploader.validation, method: onValidate}
            - {name: kernel.event_listener, event: oneup_uploader.post_persist.file, method: onUpload}
            - {name: kernel.event_listener, event: oneup_uploader.post_persist.file_entity, method: onUpload}
            - {name: kernel.event_listener, event: oneup_uploader.post_persist.file_newsletter, method: onUpload}
            - {name: kernel.event_listener, event: oneup_uploader.post_persist.file_pdf, method: onUpload}
        arguments:
            - '@AppBundle\Provider\FileProvider'
            - '@AppBundle\FileHandler\FileUploader'
            - '@app.filesystem.file'
            - '@doctrine.orm.entity_manager'
            - '@uppler_api.security.authorization_checker'
            - '@Avasil\ClamAv\Scanner'
            - '@monolog.logger'
            - '%oneup_uploader.config%'

    AppBundle\EventListener\VideoUploadListener:
        tags:
            - {name: kernel.event_listener, event: oneup_uploader.post_persist.video, method: onUpload}
        arguments:
            - '@AppBundle\FileHandler\VideoUploader'
            - '@app.filesystem.video'

    AppBundle\EventListener\LimiterListener:
        tags:
            - {name: kernel.event_listener, event: uppler_message.pre_send, method: onLimiterMessage, priority: 10}
            - {name: kernel.event_listener, event: uppler_relationship.pre.created, method: onLimiterRelationship, priority: 10}
            - {name: kernel.event_listener, event: uppler_core.email.pre_send, method: onLimiterNewsletter, priority: 10}
            - {name: kernel.event_listener, event: app.front_office.account.pre_create, method: onLimiterSubAccount, priority: 10}
            - {name: kernel.event_listener, event: app.front_office.tender_proposal.pre_create, method: onLimiterTenderProposal, priority: 10}
            - {name: kernel.event_listener, event: factory_event.pre_create, method: onLimiterTender, priority: 10}
            - {name: kernel.event_listener, event: uppler.order.pre_create_by_seller, method: onLimiterOrder, priority: 10}
            - {name: kernel.event_listener, event: uppler.order.pre_create_wishlist, method: onLimiterWishlist, priority: 10}
        arguments:
            - '@session'
            - '@translator'
            - '@event_dispatcher'
            - '@AppBundle\Provider\Shop\ProductProvider'
            - '@AppBundle\Checker\Limiter\CheckerMessage'
            - '@AppBundle\Checker\Limiter\CheckerProduct'
            - '@AppBundle\Checker\Limiter\CheckerRelationship'
            - '@AppBundle\Checker\Limiter\CheckerNewsletter'
            - '@AppBundle\Checker\Limiter\CheckerSubAccount'
            - '@AppBundle\Checker\Limiter\CheckerTenderProposal'
            - '@AppBundle\Checker\Limiter\CheckerTender'
            - '@AppBundle\Checker\Limiter\CheckerOrder'
            - '@AppBundle\Checker\Limiter\CheckerQuote'
            - '@AppBundle\Checker\Limiter\CheckerWishlist'

    AppBundle\EventListener\RelationshipListener:
        tags:
            - {name: kernel.event_listener, event: fos_user.registration.completed, method: onRegister, priority: -100}
            - {name: kernel.event_listener, event: queue_event_cart_confirm, method: onCartCompleted}
            - {name: kernel.event_listener, event: uppler_company.created_by_seller, method: onCreateBySeller}
            - {name: kernel.event_listener, event: uppler_relationship.remove.pre.completed, method: onDelete}

        arguments:
            - '@AppBundle\Provider\Relationship\RelationshipProvider'
            - '@AppBundle\Requester\RelationshipRequester'
            - '@AppBundle\Creator\RelationshipCreator'
            - '@app.resource_repository.company'
            - '@app.resource_repository.invitation'
            - '@event_dispatcher'
            - '@session'
            - '@app.resource_repository.account_company'
            - '@app.resource_repository.account'
            - '@doctrine.orm.entity_manager'

    AppBundle\EventListener\RegisterListener:
        tags:
            - {name: kernel.event_listener, event: fos_user.registration.success, method: onRegister}
        arguments:
            - '@AppBundle\Provider\Rejected\RejectedDomainProvider'
            - '@AppBundle\Provider\Rejected\RejectedIpProvider'

    AppBundle\EventListener\SubscriptionCouponListener:
        tags:
            - {name: kernel.event_listener, event: uppler.subscription_coupon.pre_create, method: onPreCreate}
        arguments:
            - '@AppBundle\Context\CurrencyContext'

    AppBundle\EventListener\RoleListener:
        tags:
            - {name: kernel.event_listener, event: fos_user.registration.success, method: onRegister, priority: 2}
            - {name: kernel.event_listener, event: uppler_company.company.post_update, method: onOwnerUpdated}
            - {name: kernel.event_listener, event: app.back_office.seller.post_update, method: onOwnerUpdated}
            - {name: kernel.event_listener, event: app.back_office.buyer.post_update, method: onOwnerUpdated}
            - {name: kernel.event_listener, event: app.front_office.account.pre_create, method: onAccount}

    AppBundle\EventListener\TosListener:
        tags:
            - {name: kernel.event_listener, event: uppler_company.tos.submit_for_validation, method: onTosSubmitted}
            - {name: kernel.event_listener, event: uppler_company.tos.deleted, method: onTosDeleted}
            - {name: kernel.event_listener, event: uppler_company.tos.edited, method: onTosContentUpdate}
            - {name: kernel.event_listener, event: uppler_company.tos.accepted, method: onTosAccepted}
            - {name: kernel.event_listener, event: uppler_company.tos.refused, method: onTosRefused}
        arguments:
            - '@doctrine.orm.entity_manager'
            - '@AppBundle\Provider\Company\CompanyProvider'

    AppBundle\EventListener\RedirectUserListener:
        tags:
            - {name: kernel.event_listener, event: kernel.request, method: onKernelRequest, priority: 5}
        arguments:
            - '@security.token_storage'
            - '@AppBundle\Security\Authorizer\RouterAuthorizer'
            - '@session'

    AppBundle\EventListener\OrderDelayRefuseListener:
        tags:
            - {name: kernel.event_listener, event: uppler_retention.post.order_delay_refuse, method: onOrderDelayToRefuseWillExpire}
        arguments:
            - '@app.resource_repository.order'
            - '@AppBundle\Provider\Shop\OrderProvider'
            - '@AppBundle\Creator\NotificationCreator'

    # -------------------------------------------------------------
    #                           COMPANY
    # _____________________________________________________________
    AppBundle\EventListener\Company\AccountListener:
        tags:
            - { name: kernel.event_listener, event: app.front_office.account.pre_create, method: onAccount }
            - { name: kernel.event_listener, event: app.front_office.account.pre_update, method: onAccount }
            - { name: kernel.event_listener, event: app.front_office.account.pre_delete, method: onDelete }

    AppBundle\EventListener\Company\CompanySubscriptionListener:
        tags:
            - { name: doctrine.orm.entity_listener, entity: \AppBundle\Entity\Company, event: preUpdate, method: enableSubscription, priority: -100 }

    AppBundle\EventListener\Company\CategoryDeleteListener:
        arguments:
            - '@doctrine.orm.entity_manager'
        tags:
            - { name: kernel.event_listener, event: app.front_office.category.pre_delete, method: processCategory }
            - { name: kernel.event_listener, event: app.back_office.category.pre_delete, method: processCategory }
            - { name: kernel.event_listener, event: app.api_operator.category.pre_delete, method: processCategory }

    AppBundle\EventListener\Company\CompanyDeleteListener:
        arguments:
            - '@doctrine.orm.entity_manager'
        tags:
            - { name: kernel.event_listener, event: app.front_office.company.pre_delete, method: processCompany }
            - { name: kernel.event_listener, event: app.back_office.seller.pre_delete, method: processCompany }
            - { name: kernel.event_listener, event: app.back_office.buyer.pre_delete, method: processCompany }

    AppBundle\EventListener\Company\CompanyZoneUpdaterListener:
        arguments:
            - '@doctrine.orm.entity_manager'
            - '@AppBundle\Provider\Company\CompanyProvider'
        tags:
            - { name: kernel.event_listener, event: app.back_office.address.pre_update, method: updateZonesFromAddress }
            - { name: kernel.event_listener, event: app.front_office.address.pre_update, method: updateZonesFromAddress }
            - { name: kernel.event_listener, event: app.back_office.seller.pre_update, method: updateZonesFromCompanyEdit }
            - { name: kernel.event_listener, event: app.back_office.buyer.pre_update, method: updateZonesFromCompanyEdit }
            - { name: kernel.event_listener, event: uppler_company.profile.edit.completed, method: updateZonesFromCompanyProfile }
            - { name: kernel.event_listener, event: fos_user.registration.completed, method: updateZonesFromCompanyRegistration }
            - { name: kernel.event_listener, event: uppler_data_importer.company.address, method: updateZonesFromAddress }

    AppBundle\EventListener\Company\CompanyCatalogStateListener:
        tags:
            - { name: doctrine.event_listener, event: postPersist, lazy: true }
            - { name: doctrine.event_listener, event: postSoftDelete, lazy: true }
            - { name: doctrine.event_listener, event: preRemove, lazy: true }
            - { name: doctrine.event_listener, event: postUpdate, lazy: true }

    AppBundle\EventListener\FileOwnerListener:
        tags:
            - { name: doctrine.event_listener, event: postPersist, lazy: true }

    # -------------------------------------------------------------
    #                           ONBOARDING
    # _____________________________________________________________
    AppBundle\EventListener\Onboarding\ActiveListener:
        arguments:
            - '@security.token_storage'
            - '@security.authorization_checker'
            - '@AppBundle\Provider\Onboarding\OnboardingProvider'
        tags:
            - { name: kernel.event_listener, event: kernel.controller, method: onKernelController, priority: 10 }

    AppBundle\EventListener\Onboarding\OnboardingNotProceedExceptionListener:
        arguments:
            - '@router'
            - '@AppBundle\Provider\Onboarding\OnboardingProvider'
        tags:
            - { name: kernel.event_listener, event: kernel.exception, method: onKernelException }


    AppBundle\EventListener\Onboarding\OnboardingStateListener:
        tags:
            - { name: kernel.event_listener, event: uppler.onboarding.pre_complete, method: resolveOnboardingState }
            - { name: kernel.event_listener, event: uppler.onboarding.pre_skip, method: resolveOnboardingState }
            - { name: kernel.event_listener, event: uppler.onboarding.pre_display, method: resolveOnboardingState }

    # -------------------------------------------------------------
    #                           PAGE
    # _____________________________________________________________
    AppBundle\EventListener\Page\PageListener:
        arguments:
            - '@AppBundle\Provider\Cms\PageProvider'
        tags:
            - { name: kernel.event_listener, event: app.back_office.page.pre_update, method: processPage }
            - { name: kernel.event_listener, event: app.back_office.page.pre_create, method: processPage }
            - { name: kernel.event_listener, event: app.back_office.page.pre_delete, method: processPage }
            - { name: kernel.event_listener, event: app.front_office.page.pre_update, method: processPage }
            - { name: kernel.event_listener, event: app.front_office.page.pre_create, method: processPage }
            - { name: kernel.event_listener, event: app.front_office.page.pre_delete, method: processPage }
            - { name: kernel.event_listener, event: app.back_office.pattern.pre_create, method: processPage }
            - { name: kernel.event_listener, event: app.back_office.pattern.pre_update, method: processPage }
            - { name: kernel.event_listener, event: app.back_office.pattern.pre_delete, method: processPage }

    # -------------------------------------------------------------
    #                           DataBuilder
    # _____________________________________________________________
    AppBundle\EventListener\DataBuilderTriggerListener:
        arguments:
            - '@AppBundle\Provider\DataBuilder\TriggerProvider'
            - '@monolog.logger'
        tags:
            - {name: doctrine.event_listener, event: postPersist}
            - {name: doctrine.event_listener, event: preRemove}
            - {name: doctrine.event_listener, event: postSoftDelete}
            - {name: doctrine.event_listener, event: preUpdate}
            - {name: doctrine.event_listener, event: postUpdate}
            - {name: kernel.event_listener, event: kernel.terminate, method: onKernelTerminate}
            - {name: kernel.event_listener, event: console.terminate, method: onConsoleTerminate}
            - {name: kernel.event_listener, event: uppler.data_builder.queue_build, method: onQueueBuild }
            - {name: kernel.event_listener, event: uppler.data_builder.queue_rebuild, method: onQueueRebuild }
            - {name: kernel.event_listener, event: uppler.data_builder.queue_clean, method: onQueueClean }

    AppBundle\EventListener\DataBuilderListener:
        tags:
            - {name: kernel.event_listener, event: uppler.data_builder.build_data, method: build }
            - {name: kernel.event_listener, event: uppler.data_builder.rebuild_data, method: rebuild }
            - {name: kernel.event_listener, event: uppler.data_builder.clean_data, method: clean }

    # -------------------------------------------------------------
    #                           SHOP
    # _____________________________________________________________
    AppBundle\EventListener\Shop\TaxonListener:
        arguments:
            - '@doctrine.orm.entity_manager'
            - '@app.resource_repository.taxon'
            - '@app.resource_repository.family'
            - '@AppBundle\Resolver\Shop\TaxonResolver'
            - '@request_stack'
            - '@session'
            - '@translator'
        tags:
            - { name: kernel.event_listener, event: app.back_office.taxon.post_create, method: postProcess }
            - { name: kernel.event_listener, event: app.back_office.taxon.post_update, method: postProcess }
            - { name: kernel.event_listener, event: app.back_office.taxon.pre_create, method: process }
            - { name: kernel.event_listener, event: app.back_office.taxon.pre_create, method: resolveTaxonomy }
            - { name: kernel.event_listener, event: app.back_office.taxon.pre_delete, method: cleanDelete }
            - { name: kernel.event_listener, event: app.back_office.taxon.pre_update, method: process }
            - { name: kernel.event_listener, event: app.back_office.taxonomy.post_create, method: postProcess }
            - { name: kernel.event_listener, event: app.back_office.taxonomy.post_update, method: postProcess }
            - { name: kernel.event_listener, event: app.back_office.taxonomy.pre_create, method: process }
            - { name: kernel.event_listener, event: app.back_office.taxonomy.pre_delete, method: cleanDelete }
            - { name: kernel.event_listener, event: app.back_office.taxonomy.pre_update, method: process }
            - { name: kernel.event_listener, event: app.front_office.taxon.post_create, method: postProcess }
            - { name: kernel.event_listener, event: app.front_office.taxon.post_update, method: postProcess }
            - { name: kernel.event_listener, event: app.front_office.taxon.pre_create, method: process }
            - { name: kernel.event_listener, event: app.front_office.taxon.pre_create, method: resolveTaxonomy }
            - { name: kernel.event_listener, event: app.front_office.taxon.pre_delete, method: cleanDelete }
            - { name: kernel.event_listener, event: app.front_office.taxon.pre_update, method: process }
            - { name: kernel.event_listener, event: app.front_office.taxonomy.post_create, method: postProcess }
            - { name: kernel.event_listener, event: app.front_office.taxonomy.post_update, method: postProcess }
            - { name: kernel.event_listener, event: app.front_office.taxonomy.pre_create, method: process }
            - { name: kernel.event_listener, event: app.front_office.taxonomy.pre_delete, method: cleanDelete }
            - { name: kernel.event_listener, event: app.front_office.taxonomy.pre_update, method: process }

            - { name: kernel.event_listener, event: app.api_operator.taxon.post_create, method: postProcess }
            - { name: kernel.event_listener, event: app.api_operator.taxon.post_update, method: postProcess }
            - { name: kernel.event_listener, event: app.api_operator.taxon.pre_create, method: process }
            - { name: kernel.event_listener, event: app.api_operator.taxon.pre_delete, method: cleanDelete }
            - { name: kernel.event_listener, event: app.api_operator.taxon.pre_update, method: process }

    AppBundle\EventListener\Shop\TransiteoSyncListener:
        tags:
            - { name: kernel.event_listener, event: uppler.order.post_check, method: syncProduct }

    AppBundle\EventListener\Shop\CartApprovalListener:
        tags:
            - { name: kernel.event_listener, event: uppler.cart.pre_summary, method: preSummary }
            - { name: kernel.event_listener, event: uppler.cart.pre_add, method: preAdd }
            - { name: kernel.event_listener, event: uppler.cart.pre_approving, method: preApproving }

    AppBundle\EventListener\Shop\CartAuthorizerListener:
        arguments:
            - '@AppBundle\Provider\Shop\CartProvider'
            - '@AppBundle\Provider\Shop\CartAuthorizerProvider'
        tags:
            - { name: kernel.event_listener, event: uppler.cart_authorizer.post_validate, method: postValidate }
            - { name: kernel.event_listener, event: uppler.cart_authorizer.post_refuse, method: postRefuse }
            - { name: kernel.event_listener, event: uppler.cart_authorizer.pre_validate, method: setAuthorizerUser }
            - { name: kernel.event_listener, event: uppler.cart_authorizer.pre_refuse, method: setAuthorizerUser }

    AppBundle\EventListener\Shop\CartListener:
        tags:
            - { name: kernel.event_listener, event: uppler.cart.pre_payment, method: updateTos }
            - { name: kernel.event_listener, event: app.api_operator.order.pre_create, method: updateTos }
            - { name: kernel.event_listener, event: data_importer.order.pre_create, method: updateTos }
            - { name: kernel.event_listener, event: app.api_seller.order.pre_create, method: updateTos }
            - { name: kernel.event_listener, event: data_importer.order.post_create, method: validateCartByOrder, priority: -100 }
            - { name: kernel.event_listener, event: app.api_seller.order.post_create, method: validateCartByOrder, priority: -100 }
            - { name: kernel.event_listener, event: uppler.order.post_create, method: onOrderCreate }
            - { name: kernel.event_listener, event: uppler.order.post_quote_order, method: onOrderCreate }
            - { name: kernel.event_listener, event: uppler.cart.pre_save, method: onOrderSave }
            - { name: kernel.event_listener, event: uppler.cart_change, method: refreshOrder, priority: 255 }
            - { name: kernel.event_listener, event: uppler.cart_item.update_quantity, method: refreshOrder, priority: 255 }
            - { name: kernel.event_listener, event: uppler.cart_item.add, method: refreshOrder, priority: 255 }
            - { name: kernel.event_listener, event: uppler.cart.apply_strategy, method: applyCartMergeStrategy }
            - { name: kernel.event_listener, event: uppler_company.state.not_treated, method: applyCartMergeStrategy }
            - { name: kernel.event_listener, event: uppler.order.post_create_supplier, method: onSupplierOrderCreate }
            - { name: kernel.event_listener, event: uppler.shop.success_converted_wishlist, method: onSuccessConvertedWishlist }
            - { name: kernel.event_listener, event: uppler.cart.pre_address, method: checkAddressCart }


    AppBundle\EventListener\Shop\CartStateListener:
        tags:
            - { name: kernel.event_listener, event: uppler.cart.step_change, method: checkPunchoutState }
            - { name: kernel.event_listener, event: uppler.cart.punchout, method: processPunchout }
            - { name: kernel.event_listener, event: uppler.cart.post_cancel, method: cancelOrders }

    AppBundle\EventListener\Shop\CartOrderConfirmeListener:
        tags:
            - { name: kernel.event_listener, event: uppler.cart.post_confirm, method: confirmCartOrder, priority: -10 }
            - { name: kernel.event_listener, event: data_importer.order.post_create, method: confirmCartOrder, priority: 10 }
            - { name: kernel.event_listener, event: uppler.quote.send_dropshipped, method: confirmCartOrder, priority: 10 }

    AppBundle\EventListener\Shop\OrderTokenListener:
        arguments:
            - '@AppBundle\Security\OrderTokenizer'
            - '@doctrine.orm.entity_manager'
        tags:
            - { name: kernel.event_listener, event: uppler.order.post_create, method: tokenOrder }
            - { name: kernel.event_listener, event: uppler.order.pre_edit, method: tokenOrder }
            - { name: kernel.event_listener, event: uppler.order.post_quote_order, method: tokenOrder, priority: 10 }
            - { name: kernel.event_listener, event: uppler.order.post_create_recurring, method: tokenOrder, priority: 10 }
            - { name: kernel.event_listener, event: uppler.order.post_create_recurrence, method: tokenOrder, priority: 10 }
            - { name: kernel.event_listener, event: uppler.order.post_transfer_wishlist, method: tokenOrder }
            - { name: kernel.event_listener, event: app.api_operator.order.post_create, method: tokenOrder }
            - { name: kernel.event_listener, event: data_importer.order.post_create, method: tokenOrder }
            - { name: kernel.event_listener, event: app.api_seller.order.post_create, method: tokenOrder }
            - { name: kernel.event_listener, event: uppler.order.post_create_approval, method: tokenOrder }

    AppBundle\EventListener\Shop\OrderItemListener:
        tags:
            - { name: kernel.event_listener, event: uppler.order.pre_edit, method: updatePrice, priority: 10 }
            - { name: kernel.event_listener, event: uppler.order.pre_item_cancel, method: cancelOrderItem, priority: 10 }
            - { name: kernel.event_listener, event: uppler.order.pre_cancel, method: cancelAllOrderItem, priority: 10 }
            - { name: kernel.event_listener, event: uppler.order.post_item_cancel, method: setOrderItemTotal, priority: 10 }

    AppBundle\EventListener\Shop\OrderCurrencyListener:
        arguments:
            - '@AppBundle\Context\CurrencyContext'
            - '@security.token_storage'
            - '@AppBundle\Provider\Shop\ShopProvider'
            - '@doctrine.orm.entity_manager'
            - '@AppBundle\Provider\Shop\PricingProvider'
        tags:
            - { name: kernel.event_listener, event: uppler.order.pre_create, method: processOrderCurrency }
            - { name: kernel.event_listener, event: uppler.order.pre_edit, method: processOrderCurrency, priority: 11 }
            - { name: kernel.event_listener, event: uppler.order.pre_edit, method: processUpdateCurrency }
            - { name: kernel.event_listener, event: uppler.order.pre_create_recurring, method: processOrderCurrency }
            - { name: kernel.event_listener, event: uppler.order.pre_create_recurrence, method: processOrderCurrency }
            - { name: kernel.event_listener, event: app.api_operator.order.pre_create, method: processOrderCurrency }
            - { name: kernel.event_listener, event: data_importer.order.pre_create, method: processOrderCurrency }
            - { name: kernel.event_listener, event: app.api_seller.order.pre_create, method: processOrderCurrency }

    AppBundle\EventListener\Shop\OrderNumberListener:
        arguments:
            - '@AppBundle\Creator\OrderNumberGenerator'
        tags:
            - { name: kernel.event_listener, event: uppler.order.pre_create_by_seller, method: generateOrderNumber, priority: 10 }
            - { name: kernel.event_listener, event: uppler.cart.post_confirm, method: generateOrderNumber, priority: 10 }
            - { name: kernel.event_listener, event: data_importer.order.post_create, method: generateOrderNumber, priority: 10 }
            - { name: kernel.event_listener, event: uppler.order.pre_quote_order, method: generateOrderNumber, priority: 10 }
            - { name: kernel.event_listener, event: uppler.order.pre_create_recurrence, method: generateOrderNumber }
            - { name: kernel.event_listener, event: uppler.order.post_create_supplier, method: generateOrderNumber }
            - { name: kernel.event_listener, event: app.api_operator.order.pre_create, method: generateOrderNumber }
            - { name: kernel.event_listener, event: app.api_seller.order.pre_create, method: generateOrderNumber }

    AppBundle\EventListener\Shop\OrderPaymentListener:
        arguments:
            - '@AppBundle\Creator\InvoiceNumberGenerator'
            - '@doctrine.orm.entity_manager'
            - '@event_dispatcher'
            - '@AppBundle\Provider\Bank\Provider'
            - '@AppBundle\Provider\Bank\PaymentProvider'
        tags:
            - { name: kernel.event_listener, event: uppler_bank.trigger.invoice, method: generateInvoiceNumber, priority: 10 }
            - { name: kernel.event_listener, event: uppler_bank.trigger.charge, method: chargePayment, priority: -10 }
            - { name: kernel.event_listener, event: uppler.order.post_item_cancel, method: updateCanceledItemPayment, priority: -10 }
            - { name: kernel.event_listener, event: uppler.order.pre_edit, method: updateCartPaymentMethod }

    AppBundle\EventListener\Shop\RecurringNumberListener:
        arguments:
            - '@AppBundle\Creator\RecurringNumberGenerator'
        tags:
            - { name: kernel.event_listener, event: uppler.order.pre_create_recurring, method: generateRecurringNumber }

    AppBundle\EventListener\Shop\OrderStockListener:
        tags:
            - { name: kernel.event_listener, event: uppler.cart.confirm, method: holdStock }
            - { name: kernel.event_listener, event: uppler.order.pre_send, method: holdStock }
            - { name: kernel.event_listener, event: uppler.order.pre_confirmation, method: decreaseStock, priority: -100 }
            - { name: kernel.event_listener, event: uppler.order.pre_refuse, method: releaseStock, priority: -100 }
            - { name: kernel.event_listener, event: uppler.order.pre_cancel, method: releaseStock, priority: 100 }
            - { name: kernel.event_listener, event: uppler.order.pre_item_cancel, method: releaseStockItem, priority: 100 }
            - { name: kernel.event_listener, event: uppler.order.pre_outside_treated, method: releaseStock, priority: -100 }
            - { name: kernel.event_listener, event: uppler.order.pre_create_recurrence, method: holdStock }
            - { name: kernel.event_listener, event: app.api_operator.order.pre_create, method: holdStock }
            - { name: kernel.event_listener, event: data_importer.order.pre_create, method: holdStock }
            - { name: kernel.event_listener, event: app.api_seller.order.pre_create, method: holdStock }
            - { name: kernel.event_listener, event: uppler.order.pre_edit, method: updateStockPickings }
            - { name: kernel.event_listener, event: app.api_operator.order.pre_update, method: updateStockPickings }

    AppBundle\EventListener\Shop\OrderShipmentListener:
        tags:
            - { name: kernel.event_listener, event: uppler.cart_change, method: createOrderShippingAddress }
            - { name: kernel.event_listener, event: uppler.cart.pre_shipping_method, method: createOrderShipment }
            - { name: kernel.event_listener, event: uppler.cart.pre_shipping_method, method: amountApply, priority: -1000 }
            - { name: kernel.event_listener, event: uppler.cart.shipping_completed, method: createOrderShipment }
            - { name: kernel.event_listener, event: uppler.cart.shipping_completed, method: amountApply, priority: -1000 }
            - { name: kernel.event_listener, event: uppler.cart_change, method: createOrderShipment }
            - { name: kernel.event_listener, event: uppler.cart_item.add, method: createOrderShipment }
            - { name: kernel.event_listener, event: uppler.order.pre_create, method: createOrderShipment }
            - { name: kernel.event_listener, event: uppler.order.pre_edit, method: createOrderShipment, priority: 10 }
            - { name: kernel.event_listener, event: data_importer.order.calculate_order, method: createOrderShipment, priority: 10 }
            - { name: kernel.event_listener, event: uppler.order.pre_edit, method: amountApply, priority: -1000 }
            - { name: kernel.event_listener, event: uppler.cart.post_confirm, method: amountApply, priority: 10 }
            - { name: kernel.event_listener, event: uppler.order.pre_create_supplier, method: amountApply, priority: -100 }
            - { name: kernel.event_listener, event: uppler.order.pre_create_recurring, method: createOrderShipment }
            - { name: kernel.event_listener, event: uppler.order.pre_create_recurrence, method: createOrderShipment }
            - { name: kernel.event_listener, event: app.api_operator.order.pre_create, method: amountApply, priority: -1000 }
            - { name: kernel.event_listener, event: data_importer.order.post_create, method: createImportedQuoteShipments, priority: -1000 }
            - { name: kernel.event_listener, event: data_importer.order.pre_create, method: amountApply, priority: -1000 }
            - { name: kernel.event_listener, event: app.api_seller.order.pre_create, method: amountApply, priority: -1000 }
            - { name: kernel.event_listener, event: uppler.order.post_item_cancel, method: updateOrderShipmentState, priority: -1000 }

    AppBundle\EventListener\Shop\OrderDiscountListener:
        arguments:
            - '@AppBundle\Processor\DiscountProcessor'
        tags:
            - { name: kernel.event_listener, event: uppler.cart_change, method: applyDiscount, priority: 1 }
            - { name: kernel.event_listener, event: uppler.cart_item.update_quantity, method: applyDiscount, priority: 1 }
            - { name: kernel.event_listener, event: uppler.cart_item.add, method: applyDiscount, priority: 1 }

    AppBundle\EventListener\Shop\OrderTaxationListener:
        arguments:
            - '@AppBundle\Processor\TaxationProcessor'
            - '@AppBundle\Provider\ConfigurationProvider'
        tags:
            - { name: kernel.event_listener, event: uppler.cart.pre_post_address, method: applyTaxes }
            - { name: kernel.event_listener, event: uppler.cart.post_shipping_method_proposal, method: applyTaxes }
            - { name: kernel.event_listener, event: uppler.cart.shipping_completed, method: applyTaxes, priority: -1 }
            - { name: kernel.event_listener, event: uppler.cart_change, method: applyTaxes, priority: -1 }
            - { name: kernel.event_listener, event: uppler.cart_item.add, method: applyTaxes, priority: -1 }
            - { name: kernel.event_listener, event: uppler.cart_item.update_quantity, method: applyTaxes, priority: -1 }
            - { name: kernel.event_listener, event: uppler.cart.confirm, method: applyTaxes }
            - { name: kernel.event_listener, event: uppler.order.pre_edit, method: applyTaxes, priority: -100 }
            - { name: kernel.event_listener, event: uppler.order.pre_create_recurring, method: applyTaxes }
            - { name: kernel.event_listener, event: uppler.order.pre_create_recurrence, method: applyTaxes }
            - { name: kernel.event_listener, event: uppler.order.pre_create_supplier, method: applyTaxes, priority: -50 }
            - { name: kernel.event_listener, event: app.api_operator.order.pre_create, method: applyTaxes }
            - { name: kernel.event_listener, event: data_importer.order.calculate_order, method: applyTaxes }
            - { name: kernel.event_listener, event: app.api_seller.order.pre_create, method: applyTaxes }
            - { name: kernel.event_listener, event: uppler.cart.post_shipping_method_proposal, method: applyTaxesOnFees, priority: -2000 }
            - { name: kernel.event_listener, event: uppler.cart.shipping_completed, method: applyTaxesOnFees, priority: -2000 }
            - { name: kernel.event_listener, event: uppler.cart.confirm, method: applyTaxesOnFees, priority: -2000 }
            - { name: kernel.event_listener, event: uppler.cart.post_confirm, method: applyTaxesOnFees, priority: -2000 }
            - { name: kernel.event_listener, event: uppler.order.pre_edit, method: applyTaxesOnFees, priority: -2000 }
            - { name: kernel.event_listener, event: uppler.order.pre_create, method: applyTaxesOnFees, priority: -2000 }
            - { name: kernel.event_listener, event: uppler.cart_change, method: applyTaxesOnFees, priority: -2000 }

    AppBundle\EventListener\Shop\OrderPromotionListener:
        tags:
            - { name: kernel.event_listener, event: uppler.order.pre_edit, method: applyPromotions, priority: 1 }
            - { name: kernel.event_listener, event: uppler.order.pre_create_recurring, method: applyPromotions }
            - { name: kernel.event_listener, event: uppler.order.pre_create_recurrence, method: applyPromotions }
            - { name: kernel.event_listener, event: uppler.order.pre_create_supplier, method: applyPromotions }
            - { name: kernel.event_listener, event: data_importer.order.pre_create, method: applyPromotions }
            - { name: kernel.event_listener, event: data_importer.order.pre_update, method: applyPromotions }

    AppBundle\EventListener\Shop\OrderStateListener:
        arguments:
            - '@AppBundle\Resolver\Shop\StateResolver'
            - '@AppBundle\Provider\ConfigurationProvider'
            - '@AppBundle\Provider\Security\AuthenticatedProvider'
            - '@AppBundle\Provider\Shop\OrderProvider'
            - '@monolog.logger.payment'
        tags:
            - { name: kernel.event_listener, event: uppler.cart.post_confirm, method: resolveOrderStateAfterCartConfirmation }
            - { name: kernel.event_listener, event: uppler.order.post_create_supplier, method: resolveOrderStateAfterCartConfirmation }
            - { name: kernel.event_listener, event: data_importer.order.post_create, method: resolveImportedOrderState }
            - { name: kernel.event_listener, event: uppler.order.pre_confirmation, method: processOrderConfirmation }
            - { name: kernel.event_listener, event: uppler.order.pre_send, method: processOrderEdit }
            - { name: kernel.event_listener, event: uppler.order.pre_send_edit, method: processOrderEdit }
            - { name: kernel.event_listener, event: uppler.order.pre_refuse, method: processOrderRefuse }
            - { name: kernel.event_listener, event: uppler.order.pre_expired, method: processOrderExpired }
            - { name: kernel.event_listener, event: uppler.order.pre_cancel, method: processOrderCancel }
            - { name: kernel.event_listener, event: uppler.order.post_payment_accepte, method: processOrderPayment }
            - { name: kernel.event_listener, event: uppler.order.pre_outside_treated, method: processOrderOutsideTreated }
            - { name: kernel.event_listener, event: uppler.order.pre_create, method: processOrderCreate }
            - { name: kernel.event_listener, event: app.api_operator.order.pre_create, method: processOrderCreate }
            - { name: kernel.event_listener, event: data_importer.order.pre_create, method: processOrderCreate }
            - { name: kernel.event_listener, event: app.api_seller.order.pre_create, method: processOrderCreate }
            - { name: kernel.event_listener, event: app.api_operator.order.pre_update, method: resolveOrderStates }
            - { name: kernel.event_listener, event: data_importer.order.pre_update, method: resolveOrderStates }
            - { name: kernel.event_listener, event: app.api_seller.order.pre_update, method: resolveOrderStates }
            - { name: kernel.event_listener, event: uppler.order.pre_edit, method: processOrderEdit, priority: -10 }
            - { name: kernel.event_listener, event: uppler.order.pre_edit, method: checkShippingStates, priority: -20 }
            - { name: kernel.event_listener, event: uppler.order.pre_ship, method: resolveOrderStates, priority: -10 }
            - { name: kernel.event_listener, event: uppler.order.pre_cancel, method: resolveOrderStates, priority: -10 }
            - { name: kernel.event_listener, event: uppler.order.pre_return, method: resolveOrderStates, priority: -10 }
            - { name: kernel.event_listener, event: uppler.order.pre_create_recurring, method: resolveOrderStates }
            - { name: kernel.event_listener, event: uppler.order.pre_create_recurrence, method: resolveOrderStates }
            - { name: kernel.event_listener, event: uppler.order.pre_submit_changes, method: processOrderSubmitChanges }
            - { name: kernel.event_listener, event: uppler.order.pre_accept_changes, method: processOrderAcceptChanges }

    AppBundle\EventListener\Shop\OrderDateListener:
        tags:
            - { name: kernel.event_listener, event: uppler.order.pre_edit, method: updateDropshippedQuoteExpiresAt }
            - { name: kernel.event_listener, event: uppler.order.pre_confirmation, method: setDefaultQuoteExpiresAt }
            - { name: kernel.event_listener, event: uppler.order.post_edit, method: updateInvoiceDateOfMainOrder }

    AppBundle\EventListener\Shop\OrderArchiveListener:
        tags:
            - { name: kernel.event_listener, event: uppler.order.pre_archive, method: processArchive }

    AppBundle\EventListener\Shop\ProductLiveListener:
        arguments:
            - '@event_dispatcher'
        tags:
            - { name: kernel.event_listener, event: uppler_live.pre_send, method: processPreLive }
            - { name: kernel.event_listener, event: uppler_live.post_send, method: processPostLive }

    AppBundle\EventListener\Shop\ShippingMethodListener:
        arguments:
            - '@doctrine.orm.entity_manager'
        tags:
            - { name: kernel.event_listener, event: app.front_office.shipping_method.pre_update, method: resolveEnabledRules }

    # -------------------------------------------------------------
    #                           SUBSCRIPTION
    # _____________________________________________________________
    AppBundle\EventListener\Subscription\CleanerListener:
        arguments:
            - '@AppBundle\Cleaner\SubscriptionCleaner'
            - '@AppBundle\Provider\Subscription\SubscriptionProvider'
        tags:
            - { name: kernel.event_listener, event: uppler_subscription.pre_complete, method: onCleaner, priority: -10 }

    AppBundle\EventListener\Subscription\FlashListener:
        arguments:
            - '@session'
            - '@translator'
            - '@AppBundle\Provider\Subscription\SubscriptionProvider'
        tags:
            - { name: kernel.event_listener, event: uppler_subscription.post_complete, method: addFlashPurchase }

    AppBundle\EventListener\Subscription\ForcePaymentExceptionListener:
        arguments:
            - '@router'
            - '%uppler_subscription.route_to_subscribe%'
        tags:
            - { name: kernel.event_listener, event: kernel.exception, method: onKernelException }

    AppBundle\EventListener\Subscription\LimiterExceptionListener:
        arguments:
            - '@router'
            - '@session'
            - '@translator'
            - '@event_dispatcher'
            - '@request_stack'
            - '%uppler_subscription.route_to_subscribe%'
        tags:
            - { name: kernel.event_listener, event: kernel.exception, method: onKernelException }

    AppBundle\EventListener\Subscription\SubscriptionListener:
        tags:
            - { name: kernel.event_listener, event: uppler_subscription.trial_start, method: onTrialStart, priority: 10 }
            - { name: kernel.event_listener, event: uppler_subscription.expire, method: onSubscriptionExpire }
            - { name: kernel.event_listener, event: app.front_office.subscription.pre_update, method: onSubscriptionUpdate }
            - { name: kernel.event_listener, event: app.back_office.subscription.pre_update, method: onSubscriptionUpdate }
            - { name: kernel.event_listener, event: uppler_bank.payment.created, method: onSubscriptionPaymentCreated }

    # -------------------------------------------------------------
    #                           USER
    # _____________________________________________________________
    AppBundle\EventListener\User\UserEntityListener:
        bind:
            $requestStack: '@request_stack'
            $userManager: '@fos_user.user_manager'
        tags:
            - { name: doctrine.event_listener, event: preUpdate, priority: 1 }


    # -------------------------------------------------------------
    #                           REVIEW
    # _____________________________________________________________
    AppBundle\EventListener\ReviewListener:
        arguments:
            - '@AppBundle\Provider\Review\ReviewProvider'
        tags:
            - { name: kernel.event_listener, event: app.front_office.review.pre_create, method: onCreate }
            - { name: kernel.event_listener, event: app.review.pre_publish, method: onPublish }
            - { name: kernel.event_listener, event: app.review.pre_refuse, method: onRefuse }

    AppBundle\EventListener\ReviewReplyListener:
        arguments:
            - '@AppBundle\Provider\Review\ReviewReplyProvider'
        tags:
            - { name: kernel.event_listener, event: app.front_office.review_reply.pre_create, method: onCreate }

    # -------------------------------------------------------------
    #                           DynamicField
    # _____________________________________________________________

    AppBundle\EventListener\DynamicFieldListener:
        tags:
            - { name: doctrine.event_listener, event: postLoad }
            - { name: doctrine.event_listener, event: postPersist }
            - { name: doctrine.event_listener, event: preUpdate, lazy: true }
            - { name: doctrine.event_listener, event: postUpdate }
            - { name: doctrine.orm.entity_listener, event: preFlush, entity: AppBundle\Entity\DynamicField, lazy: true }
            - { name: doctrine.orm.entity_listener, event: prePersist, entity: AppBundle\Entity\DynamicField, lazy: true }

    AppBundle\EventListener\DynamicFieldOwnerListener:
        tags:
            - { name: doctrine.event_listener, event: postPersist, lazy: true }
            - { name: doctrine.event_listener, event: onFlush, lazy: true }

    # -------------------------------------------------------------
    #                             Cache
    # _____________________________________________________________
    AppBundle\EventListener\Cache\ConfigurationCacheInvalidatorListener:
        tags:
            - { name: doctrine.event_listener, event: preUpdate, lazy: true }
            - { name: doctrine.event_listener, event: postUpdate, lazy: true }

    AppBundle\EventListener\Cache\CompanyRestrictionCacheInvalidatorListener:
        tags:
            - { name: doctrine.event_listener, event: postPersist, lazy: true }
            - { name: doctrine.event_listener, event: preUpdate, lazy: true }
            - { name: doctrine.event_listener, event: postUpdate, lazy: true }
            - { name: doctrine.event_listener, event: preRemove, lazy: true }

    AppBundle\EventListener\Cache\CurrencyCacheInvalidatorListener:
        tags:
            - { name: doctrine.event_listener, event: postPersist, lazy: true }
            - { name: doctrine.event_listener, event: preUpdate, lazy: true }
            - { name: doctrine.event_listener, event: preRemove, lazy: true }

    AppBundle\EventListener\Cache\CurrencySubstitutionCacheInvalidatorListener:
        tags:
            - { name: doctrine.event_listener, event: postPersist, lazy: true }
            - { name: doctrine.event_listener, event: preUpdate, lazy: true }
            - { name: doctrine.event_listener, event: preRemove, lazy: true }

    AppBundle\EventListener\Cache\ContractCacheInvalidatorListener:
        tags:
            - { name: doctrine.event_listener, event: postPersist, lazy: true }
            - { name: doctrine.event_listener, event: postUpdate, lazy: true }
            - { name: doctrine.event_listener, event: preRemove, lazy: true }

    AppBundle\EventListener\Cache\PriceCacheInvalidatorListener:
        tags:
            - { name: doctrine.event_listener, event: postPersist, lazy: true }
            - { name: doctrine.event_listener, event: preUpdate, lazy: true }
            - { name: doctrine.event_listener, event: postUpdate, lazy: true }
            - { name: doctrine.event_listener, event: preRemove, lazy: true }

    AppBundle\EventListener\Cache\ProductCacheInvalidatorListener:
        tags:
            - { name: doctrine.event_listener, event: postPersist, lazy: true }
            - { name: doctrine.event_listener, event: postUpdate, lazy: true }
            - { name: doctrine.event_listener, event: preRemove, lazy: true }

    AppBundle\EventListener\Cache\ProductPropertyCacheInvalidatorListener:
        tags:
            - { name: doctrine.event_listener, event: postPersist, lazy: true }
            - { name: doctrine.event_listener, event: postUpdate, lazy: true }
            - { name: doctrine.event_listener, event: preRemove, lazy: true }

    AppBundle\EventListener\Cache\PromotionCacheInvalidatorListener:
        tags:
            - { name: doctrine.event_listener, event: postPersist, lazy: true }
            - { name: doctrine.event_listener, event: postUpdate, lazy: true }
            - { name: doctrine.event_listener, event: preRemove, lazy: true }

    AppBundle\EventListener\Cache\PropertyCacheInvalidatorListener:
        tags:
            - { name: doctrine.event_listener, event: postPersist, lazy: true }
            - { name: doctrine.event_listener, event: postUpdate, lazy: true }
            - { name: doctrine.event_listener, event: preRemove, lazy: true }


    AppBundle\EventListener\Cache\PageCacheInvalidatorListener:
        tags:
            - { name: doctrine.event_listener, event: postUpdate, lazy: true }
            - { name: doctrine.event_listener, event: preRemove, lazy: true }

    AppBundle\EventListener\Cache\MenuCacheInvalidatorListener:
        tags:
            - { name: doctrine.event_listener, event: postPersist, lazy: true }
            - { name: doctrine.event_listener, event: postUpdate, lazy: true }
            - { name: doctrine.event_listener, event: preRemove, lazy: true }

    AppBundle\EventListener\Cache\SearchCacheInvalidatorListener:
        tags:
            - { name: doctrine.event_listener, event: postPersist, lazy: true }
            - { name: doctrine.event_listener, event: preUpdate, lazy: true }
            - { name: doctrine.event_listener, event: postUpdate, lazy: true }
            - { name: doctrine.event_listener, event: preRemove, lazy: true }

    AppBundle\EventListener\Cache\SubscriptionCacheInvalidatorListener:
        tags:
            - { name: doctrine.event_listener, event: postPersist, lazy: true }
            - { name: doctrine.event_listener, event: postUpdate, lazy: true }
            - { name: doctrine.event_listener, event: preRemove, lazy: true }

    AppBundle\EventListener\Cache\ZoneCacheInvalidatorListener:
        tags:
            - { name: doctrine.event_listener, event: postPersist, lazy: true }
            - { name: doctrine.event_listener, event: preUpdate, lazy: true }
            - { name: doctrine.event_listener, event: preRemove, lazy: true }

    AppBundle\EventListener\Cache\FileTypeCacheInvalidatorListener:
        tags:
            - { name: doctrine.event_listener, event: postPersist, lazy: true }
            - { name: doctrine.event_listener, event: postUpdate, lazy: true }
            - { name: doctrine.event_listener, event: preRemove, lazy: true }

    AppBundle\EventListener\Address\AddressListener:
        tags:
            - { name: doctrine.event_listener, event: onFlush, lazy: true }
            - { name: doctrine.event_listener, event: preRemove, lazy: true }
            - { name: kernel.event_listener, event: uppler.cart.address, method: updateCartSellerAddress }

    # -------------------------------------------------------------
    #                             Property
    # _____________________________________________________________

    AppBundle\EventListener\SellerListener:
        tags:
            - { name: kernel.event_listener, event: data_importer.subscriber.pre_create, method: processCompany }
            - { name: kernel.event_listener, event: app.api_operator.seller.pre_create, method: processCompany }
            - { name: kernel.event_listener, event: fos_user.registration.completed, method: processRegisterCompany }

    AppBundle\EventListener\RefererOwnerListener:
        tags:
            - { name: doctrine.event_listener, event: onFlush, lazy: true }
            - { name: doctrine.event_listener, event: postFlush, lazy: true }

    # -------------------------------------------------------------
    #                             Family
    # _____________________________________________________________
    AppBundle\EventListener\FamilyListener:
        tags:
            - { name: doctrine.event_listener, event: preSoftDelete, lazy: true }

    # -------------------------------------------------------------
    #                           Payment
    # _____________________________________________________________

    AppBundle\EventListener\PaymentListener:
        arguments:
            - '@doctrine.orm.entity_manager'
        tags:
            - { name: doctrine.event_listener, event: postUpdate }
            - { name: kernel.event_listener, event: app.back_office.payment.pre_update, method: updatePaymentDate }

    # -------------------------------------------------------------
    #                           Order Approval
    # _____________________________________________________________

    AppBundle\EventListener\OrderApprovalListener:
        tags:
            - { name: kernel.event_listener, event: workflow.order_approval_state.entered.refused, method: processSubOrderApprovalRefuse }

    # -------------------------------------------------------------
    #                           Authentication
    # _____________________________________________________________

    AppBundle\EventListener\AuthenticationListenerDecorator:
        decorates: fos_user.listener.authentication

    #                           Order Fee
    # _____________________________________________________________
    AppBundle\EventListener\OrderFeeListener:
        tags:
            - { name: kernel.event_listener, event: uppler.cart.post_shipping_method_proposal, method: applyFee, priority: -1001 }
            - { name: kernel.event_listener, event: uppler.cart.shipping_completed, method: applyFee, priority: -1000 }
            - { name: kernel.event_listener, event: uppler.order.pre_create, method: applyFee, priority: -1000 }
            - { name: kernel.event_listener, event: uppler.order.pre_edit, method: applyFee, priority: -1000 }

    #                           Addon
    # _____________________________________________________________
    AppBundle\EventListener\Shop\AddonPriceListener:
        autowire: true
        tags:
            - { name: kernel.event_listener, event: uppler.cart_change, method: applyAddonPrice, priority: 1 }
            - { name: kernel.event_listener, event: uppler.cart_item.update_quantity, method: applyAddonPrice, priority: 1 }
            - { name: kernel.event_listener, event: uppler.cart_item.add, method: applyAddonPrice, priority: 1 }
            - { name: kernel.event_listener, event: uppler.order.pre_edit, method: applyAddonPrice, priority: 1 }

    #                           Payment reconciliation
    # _____________________________________________________________
    AppBundle\EventListener\Bank\ReconciliationStateListener:
        autowire: true
        tags:
            - { name: kernel.event_listener, event: uppler_bank.reconciliation.accept, method: resolveReconciliationStates }
            - { name: kernel.event_listener, event: uppler_bank.reconciliation.refuse, method: resolveReconciliationStates }
            - { name: kernel.event_listener, event: uppler_bank.reconciliation.extra, method: resolveReconciliationStates }

    AppBundle\EventListener\Shop\OrderPromotionAllocationListener:
        autowire: true
        tags:
            - { name: kernel.event_listener, event: uppler.cart.post_confirm, method: processPromotionAllocation, priority: -20 }
            - { name: kernel.event_listener, event: uppler.cart.post_confirm, method: processPromotionDeduction, priority: -21 }

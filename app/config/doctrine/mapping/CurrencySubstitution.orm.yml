AppBundle\Entity\CurrencySubstitution:
    type: entity
    table: currency_substitution
    repositoryClass: AppBundle\Repository\CurrencySubstitutionRepository
    gedmo:
        loggable: true
    id:
        id:
            id: true
            type: integer
            generator:
                strategy: IDENTITY

    manyToOne:
        origin:
            targetEntity: AppBundle\Entity\Currency
            nullable: false
            inversedBy: displayedCurrencies
            fetch: EAGER
            joinColumn:
                name: origin_id
                referencedColumnName: id
                nullable: false
            gedmo:
                versioned: versioned
        target:
            targetEntity: AppBundle\Entity\Currency
            nullable: false
            fetch: EAGER
            joinColumn:
                name: target_id
                referencedColumnName: id
                nullable: false
            gedmo:
                versioned: versioned

    oneToOne:
        buyerCompanyMatcher:
            targetEntity: AppBundle\Entity\CompanyMatcher
            cascade:
                - persist
            nullable: false
            joinColumn:
                name: buyer_company_matcher_id
                referencedColumnName: id
                nullable: false
            gedmo:
                versioned: versioned

    lifecycleCallbacks: {  }

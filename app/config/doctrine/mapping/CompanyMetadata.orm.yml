AppBundle\Entity\CompanyMetadata:
    type: entity
    table: company_metadata
    repositoryClass: AppBundle\Repository\CompanyMetadataRepository
    gedmo:
        loggable: true
    id:
        id:
            id: true
            type: integer
            generator:
                strategy: IDENTITY
    fields:

        cover:
            type: text
            nullable: true
        coverCrop:
            type: text
            nullable: true
            column: cover_crop
        descriptionPicture:
            type: text
            nullable: true
            column: description_picture
        number:
            nullable: true
            type: string
            gedmo:
                versioned: versioned

        commercialRegister:
            nullable: true
            type: string
            column: commercial_register
            gedmo:
                versioned: versioned

        phone:
            length: 50
            nullable: true
            type: string
            gedmo:
                versioned: versioned

        mobile:
            length: 50
            nullable: true
            type: string
            gedmo:
                versioned: versioned

        fax:
            length: 50
            nullable: true
            type: string
            gedmo:
                versioned: versioned

        website:
            length: 150
            nullable: true
            type: string
            gedmo:
                versioned: versioned

        websiteTwo:
            length: 150
            nullable: true
            type: string
            column: website_two
            gedmo:
                versioned: versioned

        instagram:
            length: 150
            nullable: true
            type: string
            gedmo:
                versioned: versioned

        youtube:
            length: 150
            nullable: true
            type: string
            gedmo:
                versioned: versioned

        tva:
            length: 100
            nullable: true
            type: string
            gedmo:
                versioned: versioned

        email:
            length: 150
            nullable: true
            type: string
            gedmo:
                versioned: versioned

        position:
            length: 50
            nullable: true
            type: string
            gedmo:
                versioned: versioned

        madeIn:
            length: 100
            nullable: true
            type: string
            column: made_in
            gedmo:
                versioned: versioned

        hideLegal:
            type: boolean
            nullable: true
            column: hide_legal
            gedmo:
                versioned: versioned

        legalType:
            nullable: true
            type: string
            column: legal_type
            gedmo:
                versioned: versioned

        legalSector:
            length: 150
            nullable: true
            type: string
            column: legal_sector
            gedmo:
                versioned: versioned

        numberEmployee:
            length: 50
            nullable: true
            type: string
            column: number_employee
            gedmo:
                versioned: versioned

        executiveName:
            nullable: true
            type: string
            column: executive_name
            gedmo:
                versioned: versioned

        executiveRole:
            nullable: true
            type: string
            column: executive_role
            gedmo:
                versioned: versioned

        capital:
            nullable: true
            type: string
            gedmo:
                versioned: versioned

        turnover:
            nullable: true
            type: string
            gedmo:
                versioned: versioned

        creationDate:
            type: datetime
            nullable: true
            column: creation_date
            gedmo:
                versioned: versioned

        locationsNumber:
            nullable: true
            type: string
            column: locations_number
            gedmo:
                versioned: versioned

        urbanUnitSize:
            length: 100
            nullable: true
            type: string
            column: urban_unit_size
            gedmo:
                versioned: versioned

        surface:
            length: 100
            nullable: true
            type: string
            gedmo:
                versioned: versioned

        contractCode:
            length: 40
            nullable: true
            type: string
            column: contract_code
            gedmo:
                versioned: versioned

        geographicalScope:
            nullable: true
            type: string
            column: geographical_scope
            gedmo:
                versioned: versioned

        headOffice:
            nullable: true
            type: string
            column: head_office
            gedmo:
                versioned: versioned

        fiscalCode:
            nullable: true
            type: string
            column: fiscal_code
            gedmo:
                versioned: versioned

        market:
            nullable: true
            type: string
            gedmo:
                versioned: versioned

        facebook:
            nullable: true
            type: string
            gedmo:
                versioned: versioned

        yearlyProduction:
            nullable: true
            type: string
            column: yearly_production
            gedmo:
                versioned: versioned

        exportTurnoverPercent:
            type: decimal
            precision: 10
            scale: 5
            nullable: true
            column: export_turnover_percent
            gedmo:
                versioned: versioned

        contactTitle:
            length: 100
            nullable: true
            type: string
            column: contact_title
            gedmo:
                versioned: versioned

        contactFirstName:
            length: 100
            nullable: true
            type: string
            column: contact_first_name
            gedmo:
                versioned: versioned

        contactLastName:
            length: 100
            nullable: true
            type: string
            column: contact_last_name
            gedmo:
                versioned: versioned

        contactEmail:
            nullable: true
            type: string
            column: contact_email
            gedmo:
                versioned: versioned

        contactPhone:
            length: 50
            nullable: true
            type: string
            column: contact_phone
            gedmo:
                versioned: versioned

        secondContactTitle:
            length: 100
            nullable: true
            type: string
            column: second_contact_title
            gedmo:
                versioned: versioned

        secondContactFirstName:
            length: 100
            nullable: true
            type: string
            column: second_contact_first_name
            gedmo:
                versioned: versioned

        secondContactLastName:
            length: 100
            nullable: true
            type: string
            column: second_contact_last_name
            gedmo:
                versioned: versioned

        secondContactEmail:
            length: 150
            nullable: true
            type: string
            column: second_contact_email
            gedmo:
                versioned: versioned

        secondContactPhone:
            length: 50
            nullable: true
            type: string
            column: second_contact_phone
            gedmo:
                versioned: versioned

        accountantFirstName:
            length: 100
            nullable: true
            type: string
            column: accountant_first_name
            gedmo:
                versioned: versioned

        accountantLastName:
            length: 100
            nullable: true
            type: string
            column: accountant_last_name
            gedmo:
                versioned: versioned

        accountantTitle:
            length: 100
            nullable: true
            type: string
            column: accountant_title
            gedmo:
                versioned: versioned

        accountantEmail:
            length: 150
            nullable: true
            type: string
            column: accountant_email
            gedmo:
                versioned: versioned

        accountantPhone:
            length: 50
            nullable: true
            type: string
            column: accountant_phone
            gedmo:
                versioned: versioned

        headOfficeAddressType:
            length: 50
            nullable: true
            type: string
            column: head_office_address_type
            gedmo:
                versioned: versioned

        headOfficeStreetNumber:
            length: 50
            type: integer
            nullable: true
            column: head_office_street_number
            gedmo:
                versioned: versioned

        headOfficeStreetNumberAdditionnal:
            length: 50
            nullable: true
            type: string
            column: head_office_street_number_additionnal
            gedmo:
                versioned: versioned

        headOfficeStreetType:
            length: 50
            nullable: true
            type: string
            column: head_office_street_type
            gedmo:
                versioned: versioned

        headOfficeStreetName:
            length: 100
            nullable: true
            type: string
            column: head_office_street_name
            gedmo:
                versioned: versioned

        headOfficeStreetSupplement:
            length: 100
            nullable: true
            type: string
            column: head_office_street_supplement
            gedmo:
                versioned: versioned

        headOfficeAdditionnalAddress1:
            length: 150
            nullable: true
            type: string
            column: head_office_additionnal_address1
            gedmo:
                versioned: versioned

        headOfficeAdditionnalAddress2:
            length: 150
            nullable: true
            type: string
            column: head_office_additionnal_address2
            gedmo:
                versioned: versioned

        headOfficeAdditionnalAddress3:
            length: 150
            nullable: true
            type: string
            column: head_office_additionnal_address3
            gedmo:
                versioned: versioned

        headOfficePostcode:
            nullable: true
            type: string
            column: head_office_postcode
            gedmo:
                versioned: versioned

        headOfficeCity:
            length: 150
            nullable: true
            type: string
            column: head_office_city
            gedmo:
                versioned: versioned

        headOfficeCountry:
            length: 50
            nullable: true
            type: string
            column: head_office_country
            gedmo:
                versioned: versioned

        iban:
            length: 40
            nullable: true
            type: string
            gedmo:
                versioned: versioned

        bic:
            length: 12
            nullable: true
            type: string
            gedmo:
                versioned: versioned

        accountNumber:
            nullable: true
            type: string
            column: account_number
            gedmo:
                versioned: versioned

        accountCurrency:
            length: 3
            nullable: true
            type: string
            column: account_currency
            gedmo:
                versioned: versioned

        signatoryLastName:
            length: 50
            nullable: true
            type: string
            column: signatory_last_name
            gedmo:
                versioned: versioned

        signatoryFirstName:
            length: 50
            nullable: true
            type: string
            column: signatory_first_name
            gedmo:
                versioned: versioned

        signatoryTitle:
            length: 50
            nullable: true
            type: string
            column: signatory_title
            gedmo:
                versioned: versioned
        signatoryEmail:
            length: 50
            nullable: true
            type: string
            column: signatory_email
            gedmo:
                versioned: versioned
        signatoryPhone:
            length: 50
            nullable: true
            type: string
            column: signatory_phone
            gedmo:
                versioned: versioned
        signatoryFunction:
            length: 50
            nullable: true
            type: string
            column: signatory_function
            gedmo:
                versioned: versioned
        signatoryCountry:
            length: 50
            nullable: true
            type: string
            column: signatory_country
            gedmo:
                versioned: versioned
        signatoryBirthDate:
            length: 100
            type: datetime
            nullable: true
            column: signatory_birth_date
            gedmo:
                versioned: versioned
        signatoryBirthPlace:
            length: 200
            nullable: true
            type: string
            column: signatory_birth_place
            gedmo:
                versioned: versioned
        signatoryIssueDate:
            length: 150
            type: datetime
            nullable: true
            column: signatory_issue_date
            gedmo:
                versioned: versioned
        signatoryIssuePlace:
            length: 150
            nullable: true
            type: string
            column: signatory_issue_place
            gedmo:
                versioned: versioned
        signatoryIssueAuthority:
            length: 150
            nullable: true
            type: string
            column: signatory_issue_authority
            gedmo:
                versioned: versioned
        signatoryExpiryDate:
            length: 150
            type: datetime
            nullable: true
            column: signatory_expiry_date
            gedmo:
                versioned: versioned
        signatoryDocNumber:
            length: 150
            nullable: true
            type: string
            column: signatory_doc_number
            gedmo:
                versioned: versioned
        beneficiaryOneTitle:
            length: 150
            nullable: true
            type: string
            column: beneficiary_one_title
            gedmo:
                versioned: versioned
        beneficiaryOneLastName:
            length: 150
            nullable: true
            type: string
            column: beneficiary_one_last_name
            gedmo:
                versioned: versioned
        beneficiaryOneFirstName:
            length: 150
            nullable: true
            type: string
            column: beneficiary_one_first_name
            gedmo:
                versioned: versioned
        beneficiaryOneEmail:
            length: 150
            nullable: true
            type: string
            column: beneficiary_one_email
            gedmo:
                versioned: versioned
        beneficiaryOnePhone:
            length: 50
            nullable: true
            type: string
            column: beneficiary_one_phone
            gedmo:
                versioned: versioned
        beneficiaryOneCapitalPercent:
            type: decimal
            precision: 10
            scale: 5
            nullable: true
            column: beneficiary_one_capital_percent
            gedmo:
                versioned: versioned
        beneficiaryOneNationality:
            length: 150
            nullable: true
            type: string
            column: beneficiary_one_nationality
            gedmo:
                versioned: versioned
        beneficiaryOneBirthDate:
            length: 150
            type: datetime
            nullable: true
            column: beneficiary_one_birth_date
            gedmo:
                versioned: versioned
        beneficiaryOneBirthPlace:
            length: 150
            nullable: true
            type: string
            column: beneficiary_one_birth_place
            gedmo:
                versioned: versioned
        beneficiaryOneBirthCountry:
            length: 20
            nullable: true
            type: string
            column: beneficiary_one_birth_country
            gedmo:
                versioned: versioned
        beneficiaryOneIssueDate:
            length: 150
            type: datetime
            nullable: true
            column: beneficiary_one_issue_date
            gedmo:
                versioned: versioned
        beneficiaryOneIssuePlace:
            nullable: true
            type: string
            column: beneficiary_one_issue_place
            gedmo:
                versioned: versioned
        beneficiaryOneIssueAuthority:
            length: 150
            nullable: true
            type: string
            column: beneficiary_one_issue_authority
            gedmo:
                versioned: versioned
        beneficiaryOneExpiryDate:
            length: 150
            type: datetime
            nullable: true
            column: beneficiary_one_expiry_date
            gedmo:
                versioned: versioned
        beneficiaryOneFiscalCode:
            length: 150
            nullable: true
            type: string
            column: beneficiary_one_fiscal_code
            gedmo:
                versioned: versioned
        beneficiaryOneCorporateName:
            length: 150
            nullable: true
            type: string
            column: beneficiary_one_corporate_name
            gedmo:
                versioned: versioned
        beneficiaryOneDocNumber:
            length: 150
            nullable: true
            type: string
            column: beneficiary_one_doc_number
            gedmo:
                versioned: versioned
        beneficiaryOneAddress:
            length: 150
            nullable: true
            type: string
            column: beneficiary_one_address
            gedmo:
                versioned: versioned
        beneficiaryTwoTitle:
            length: 100
            nullable: true
            type: string
            column: beneficiary_two_title
            gedmo:
                versioned: versioned
        beneficiaryTwoLastName:
            length: 100
            nullable: true
            type: string
            column: beneficiary_two_last_name
            gedmo:
                versioned: versioned
        beneficiaryTwoFirstName:
            length: 100
            nullable: true
            type: string
            column: beneficiary_two_first_name
            gedmo:
                versioned: versioned
        beneficiaryTwoEmail:
            length: 150
            nullable: true
            type: string
            column: beneficiary_two_email
            gedmo:
                versioned: versioned
        beneficiaryTwoPhone:
            length: 50
            nullable: true
            type: string
            column: beneficiary_two_phone
            gedmo:
                versioned: versioned
        beneficiaryTwoCapitalPercent:
            type: decimal
            precision: 10
            scale: 5
            nullable: true
            column: beneficiary_two_capital_percent
            gedmo:
                versioned: versioned
        beneficiaryTwoNationality:
            length: 150
            nullable: true
            type: string
            column: beneficiary_two_nationality
            gedmo:
                versioned: versioned
        beneficiaryTwoBirthDate:
            length: 150
            type: datetime
            nullable: true
            column: beneficiary_two_birth_date
            gedmo:
                versioned: versioned
        beneficiaryTwoBirthPlace:
            length: 150
            nullable: true
            type: string
            column: beneficiary_two_birth_place
            gedmo:
                versioned: versioned
        beneficiaryTwoBirthCountry:
            length: 20
            nullable: true
            type: string
            column: beneficiary_two_birth_country
            gedmo:
                versioned: versioned
        beneficiaryTwoIssueDate:
            length: 150
            type: datetime
            nullable: true
            column: beneficiary_two_issue_date
            gedmo:
                versioned: versioned
        beneficiaryTwoIssuePlace:
            length: 150
            nullable: true
            type: string
            column: beneficiary_two_issue_place
            gedmo:
                versioned: versioned
        beneficiaryTwoIssueAuthority:
            length: 150
            nullable: true
            type: string
            column: beneficiary_two_issue_authority
            gedmo:
                versioned: versioned
        beneficiaryTwoExpiryDate:
            length: 150
            type: datetime
            nullable: true
            column: beneficiary_two_expiry_date
            gedmo:
                versioned: versioned
        beneficiaryTwoFiscalCode:
            length: 150
            nullable: true
            type: string
            column: beneficiary_two_fiscal_code
            gedmo:
                versioned: versioned
        beneficiaryTwoCorporateName:
            length: 150
            nullable: true
            type: string
            column: beneficiary_two_corporate_name
            gedmo:
                versioned: versioned
        beneficiaryTwoDocNumber:
            length: 100
            nullable: true
            type: string
            column: beneficiary_two_doc_number
            gedmo:
                versioned: versioned
        beneficiaryTwoAddress:
            length: 200
            nullable: true
            type: string
            column: beneficiary_two_address
            gedmo:
                versioned: versioned
        beneficiaryThreeTitle:
            length: 100
            nullable: true
            type: string
            column: beneficiary_three_title
            gedmo:
                versioned: versioned
        beneficiaryThreeLastName:
            length: 100
            nullable: true
            type: string
            column: beneficiary_three_last_name
            gedmo:
                versioned: versioned
        beneficiaryThreeFirstName:
            length: 100
            nullable: true
            type: string
            column: beneficiary_three_first_name
            gedmo:
                versioned: versioned
        beneficiaryThreeEmail:
            length: 150
            nullable: true
            type: string
            column: beneficiary_three_email
            gedmo:
                versioned: versioned
        beneficiaryThreePhone:
            length: 50
            nullable: true
            type: string
            column: beneficiary_three_phone
            gedmo:
                versioned: versioned
        beneficiaryThreeCapitalPercent:
            type: decimal
            precision: 10
            scale: 5
            nullable: true
            column: beneficiary_three_capital_percent
            gedmo:
                versioned: versioned
        beneficiaryThreeNationality:
            length: 50
            nullable: true
            type: string
            column: beneficiary_three_nationality
            gedmo:
                versioned: versioned
        beneficiaryThreeBirthDate:
            length: 150
            type: datetime
            nullable: true
            column: beneficiary_three_birth_date
            gedmo:
                versioned: versioned
        beneficiaryThreeBirthPlace:
            length: 150
            nullable: true
            type: string
            column: beneficiary_three_birth_place
            gedmo:
                versioned: versioned
        beneficiaryThreeBirthCountry:
            length: 20
            nullable: true
            type: string
            column: beneficiary_three_birth_country
            gedmo:
                versioned: versioned
        beneficiaryThreeIssueDate:
            length: 150
            type: datetime
            nullable: true
            column: beneficiary_three_issue_date
            gedmo:
                versioned: versioned
        beneficiaryThreeIssuePlace:
            length: 150
            nullable: true
            type: string
            column: beneficiary_three_issue_place
            gedmo:
                versioned: versioned
        beneficiaryThreeIssueAuthority:
            nullable: true
            type: string
            column: beneficiary_three_issue_authority
            gedmo:
                versioned: versioned
        beneficiaryThreeExpiryDate:
            length: 150
            type: datetime
            nullable: true
            column: beneficiary_three_expiry_date
            gedmo:
                versioned: versioned
        beneficiaryThreeFiscalCode:
            length: 50
            nullable: true
            type: string
            column: beneficiary_three_fiscal_code
            gedmo:
                versioned: versioned
        beneficiaryThreeCorporateName:
            nullable: true
            type: string
            column: beneficiary_three_corporate_name
            gedmo:
                versioned: versioned
        beneficiaryThreeDocNumber:
            nullable: true
            type: string
            column: beneficiary_three_doc_number
            gedmo:
                versioned: versioned
        beneficiaryThreeAddress:
            length: 200
            nullable: true
            type: string
            column: beneficiary_three_address
            gedmo:
                versioned: versioned
        beneficiaryFourTitle:
            length: 50
            nullable: true
            type: string
            column: beneficiary_Four_title
            gedmo:
                versioned: versioned
        beneficiaryFourLastName:
            length: 50
            nullable: true
            type: string
            column: beneficiary_four_last_name
            gedmo:
                versioned: versioned
        beneficiaryFourFirstName:
            length: 50
            nullable: true
            type: string
            column: beneficiary_four_first_name
            gedmo:
                versioned: versioned
        beneficiaryFourEmail:
            length: 150
            nullable: true
            type: string
            column: beneficiary_four_email
            gedmo:
                versioned: versioned
        beneficiaryFourPhone:
            length: 50
            nullable: true
            type: string
            column: beneficiary_four_phone
            gedmo:
                versioned: versioned
        beneficiaryFourCapitalPercent:
            type: decimal
            precision: 10
            scale: 5
            nullable: true
            column: beneficiary_four_capital_percent
            gedmo:
                versioned: versioned
        beneficiaryFourNationality:
            length: 150
            nullable: true
            type: string
            column: beneficiary_four_nationality
            gedmo:
                versioned: versioned
        beneficiaryFourBirthDate:
            length: 150
            type: datetime
            nullable: true
            column: beneficiary_four_birth_date
            gedmo:
                versioned: versioned
        beneficiaryFourBirthPlace:
            length: 150
            nullable: true
            type: string
            column: beneficiary_four_birth_place
            gedmo:
                versioned: versioned
        beneficiaryFourBirthCountry:
            length: 20
            nullable: true
            type: string
            column: beneficiary_four_birth_country
            gedmo:
                versioned: versioned
        beneficiaryFourIssueDate:
            length: 150
            type: datetime
            nullable: true
            column: beneficiary_four_issue_date
            gedmo:
                versioned: versioned
        beneficiaryFourIssuePlace:
            length: 150
            nullable: true
            type: string
            column: beneficiary_four_issue_place
            gedmo:
                versioned: versioned
        beneficiaryFourIssueAuthority:
            nullable: true
            type: string
            column: beneficiary_four_issue_authority
            gedmo:
                versioned: versioned
        beneficiaryFourExpiryDate:
            length: 150
            type: datetime
            nullable: true
            column: beneficiary_four_expiry_date
            gedmo:
                versioned: versioned
        beneficiaryFourFiscalCode:
            length: 150
            nullable: true
            type: string
            column: beneficiary_four_fiscal_code
            gedmo:
                versioned: versioned
        beneficiaryFourCorporateName:
            length: 150
            nullable: true
            type: string
            column: beneficiary_four_corporate_name
            gedmo:
                versioned: versioned
        beneficiaryFourDocNumber:
            length: 150
            nullable: true
            type: string
            column: beneficiary_four_doc_number
            gedmo:
                versioned: versioned
        beneficiaryFourAddress:
            length: 200
            nullable: true
            type: string
            column: beneficiary_four_address
            gedmo:
                versioned: versioned
        bankDomiciliation:
            length: 100
            nullable: true
            type: string
            column: bank_domiciliation
            gedmo:
                versioned: versioned
        bankAddress:
            length: 200
            nullable: true
            type: string
            column: bank_address
            gedmo:
                versioned: versioned
        rcsNumber:
            nullable: true
            type: string
            column: rcs_number
            gedmo:
                versioned: versioned
        membership:
            nullable: true
            type: string
            column: membership
            gedmo:
                versioned: versioned
        vatRegime:
            nullable: true
            type: string
            column: vat_regime
            gedmo:
                versioned: versioned
        specificInsurance:
            nullable: true
            type: string
            column: specific_insurance
            gedmo:
                versioned: versioned
        registrationCity:
            nullable: true
            type: string
            column: registration_city
            gedmo:
                versioned: versioned
        directoryNumber:
            nullable: true
            type: string
            column: directory_number
            gedmo:
                versioned: versioned
        createdAt:
            type: datetime
            column: created_at
            gedmo:
                timestampable:
                    on: create
        updatedAt:
            type: datetime
            column: updated_at
            gedmo:
                timestampable:
                    on: update
                versioned: versioned
        noAccessRelationship:
            type: boolean
            nullable: true
            column: no_access_relationship
    manyToOne:
        signatoryAddress:
            targetEntity: AppBundle\Entity\Address
            cascade:
                - persist
            fetch: LAZY
            joinColumns:
                signatory_address_id:
                    referencedColumnName: id
            gedmo:
                versioned: versioned
        beneficiaryOneAddressObject:
            targetEntity: AppBundle\Entity\Address
            cascade:
                - persist
            fetch: LAZY
            joinColumns:
                beneficiary_one_address_id:
                    referencedColumnName: id
            gedmo:
                versioned: versioned
        beneficiaryTwoAddressObject:
            targetEntity: AppBundle\Entity\Address
            cascade:
                - persist
            fetch: LAZY
            joinColumns:
                beneficiary_two_address_id:
                    referencedColumnName: id
            gedmo:
                versioned: versioned
        beneficiaryThreeAddressObject:
            targetEntity: AppBundle\Entity\Address
            cascade:
                - persist
            fetch: LAZY
            joinColumns:
                beneficiary_three_address_id:
                    referencedColumnName: id
            gedmo:
                versioned: versioned
        beneficiaryFourAddressObject:
            targetEntity: AppBundle\Entity\Address
            cascade:
                - persist
            fetch: LAZY
            joinColumns:
                beneficiary_four_address_id:
                    referencedColumnName: id
            gedmo:
                versioned: versioned
    lifecycleCallbacks: {  }

App<PERSON><PERSON>le\Entity\Review:
    type: entity
    table: review
    repositoryClass: AppBundle\Repository\ReviewRepository
    gedmo:
        loggable: true
    id:
        id:
            id: true
            type: integer
            generator:
                strategy: IDENTITY
    fields:
        state:
            type: string
            gedmo:
                versioned: versioned
        rate:
            type: integer
            nullable: true
            gedmo:
                versioned: versioned
        title:
            type: string
            nullable: true
            gedmo:
                versioned: versioned
        comment:
            type: text
            nullable: true
            gedmo:
                versioned: versioned
        reviewableType:
            type: string
            column: 'reviewable_type'
            gedmo:
                versioned: versioned
        reviewableId:
            type: integer
            column: 'reviewable_id'
            gedmo:
                versioned: versioned
        refererType:
            type: string
            nullable: true
            column: 'referer_type'
            gedmo:
                versioned: versioned
        refererId:
            type: integer
            nullable: true
            column: 'referer_id'
            gedmo:
                versioned: versioned
        publishedBy:
            type: string
            nullable: true
            column: 'published_by'
            gedmo:
                versioned: versioned
        createdAt:
            type: datetime
            column: created_at
            gedmo:
                timestampable:
                    on: create
        updatedAt:
            type: datetime
            column: updated_at
            gedmo:
                timestampable:
                    on: update
                versioned: versioned
        publishedAt:
            type: datetime
            column: published_at
            nullable: true
            gedmo:
                versioned: versioned

    manyToOne:
        configuration:
            targetEntity: AppBundle\Entity\ReviewConfiguration
            cascade: {  }
            fetch: LAZY
            inversedBy: null
            joinColumns:
                configuration_id:
                    referencedColumnName: id
                    nullable: true
            orphanRemoval: false
            gedmo:
                versioned: versioned

        author:
            targetEntity: AppBundle\Entity\User
            cascade: {  }
            fetch: LAZY
            inversedBy: null
            joinColumns:
                user_id:
                    referencedColumnName: id
                    nullable: true
            orphanRemoval: false
            gedmo:
                versioned: versioned
    lifecycleCallbacks: {  }

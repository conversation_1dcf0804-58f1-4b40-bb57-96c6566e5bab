AppBundle\Entity\VariantRestock:
    type: entity
    table: variant_restock
    repositoryClass: AppBundle\Repository\VariantRestockRepository
    gedmo:
        loggable: true
        soft_deleteable:
            field_name: deletedAt
    indexes:
        current_stock_idx:
            columns:
                - is_current_stock
        enabled_date_idx:
            columns:
                - is_enabled
                - date
    id:
        id:
            id: true
            type: integer
            generator:
                strategy: IDENTITY
    fields:
        onHold:
            type: integer
            column: on_hold
            nullable: true
            gedmo:
                versioned: versioned
        onHand:
            type: integer
            column: on_hand
            nullable: false
            gedmo:
                versioned: versioned

        isCurrentStock:
            column: is_current_stock
            type: boolean
            nullable: false
            gedmo:
                versioned: versioned

        isEnabled:
            column: is_enabled
            type: boolean
            nullable: false
            options:
                default: 1
            gedmo:
                versioned: versioned
        date:
            type: datetime
            nullable: true
            gedmo:
                versioned: versioned
        createdAt:
            type: datetime
            column: created_at
            gedmo:
                timestampable:
                    on: create
        updatedAt:
            type: datetime
            nullable: true
            column: updated_at
            gedmo:
                timestampable:
                    on: update
        deletedAt:
            type: datetime
            nullable: true
            column: deleted_at
        externalId:
            type: string
            nullable: true
            column: external_id
            gedmo:
                versioned: versioned
    manyToOne:
        variant:
            targetEntity: AppBundle\Entity\Variant
            fetch: LAZY
            inversedBy: restocks
            joinColumns:
                variant_id:
                    referencedColumnName: id
            gedmo:
                versioned: versioned
        warehouse:
            targetEntity: AppBundle\Entity\Warehouse
            fetch: EAGER
            orphanRemoval: true
            joinColumns:
                warehouse_id:
                    referencedColumnName: id
            gedmo:
                versioned: versioned
    lifecycleCallbacks: {  }

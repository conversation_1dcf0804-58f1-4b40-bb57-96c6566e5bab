AppBundle\Entity\SubscriptionLimiterTranslation:
    type: entity
    table: subscription_limiter_translation
    repositoryClass: AppBundle\Repository\SubscriptionLimiterTranslationRepository
    gedmo:
        loggable: true
    id:
        id:
            id: true
            type: integer
            generator:
                strategy: IDENTITY
    fields:
        flashMessage:
            type: string
            nullable: true
            column: flash_message
            gedmo:
                versioned: versioned

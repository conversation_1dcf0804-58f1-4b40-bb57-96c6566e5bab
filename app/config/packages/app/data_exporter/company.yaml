app:
    data_exporter:
        definitions:
            seller_back_office:
                class: App:Company
                gateway: default
                exposed: false
                custom_repository: AppBundle\ResourceRepository\SellerRepository
                repository_method: exportBy

            buyer_back_office:
                class: App:Company
                gateway: default
                exposed: false
                custom_repository: AppBundle\ResourceRepository\BuyerRepository
                repository_method: iterateBy

            relationship_front_office:
                class: App:Company
                gateway: default
                exposed: false
                repository_method: iterateBy
                properties:
                    id:
                        default: true
                    external_id:
                    company_name:
                    parent_company:
                    corporate_name:
                        default: true
                    intracommunautary_tva_number:
                    company_identity_number:
                    company_type:
                    category:
                    email:
                    customer_service_email:
                    phone:
                    firstname:
                    lastname:
                    username:
                    language:
                    currency:
                    address:
                    street:
                    postcode:
                    city:
                    province_name:
                    country_name:
                    logo:
                    description:
                    website:
                    billing_address:
                    shipping_address:
                    civility:
                    function:
                    legal_sector:
                    fiscal_code:
                    legal_type:
                    social_capital:
                    turnover:

            company_back_office:
                class: App:Company
                gateway: default
                allowed_connector_types: [ftp, sftp, local]
                repository_method: iterateBy
                default_criteria:
                    updated_at_from: true
                contexts: [back_office]
                properties:
                    id:
                        default: true
                    roles:
                    external_id:
                    company_name:
                    parent_company:
                    corporate_name:
                        default: true
                    intracommunautary_tva_number:
                    company_identity_number:
                    visibility:
                    state:
                    profile_state:
                    catalog_state:
                    kyc_state:
                    cgv_state:
                    company_type:
                    category:
                    created_at:
                    user_id:
                    email:
                    customer_service_email:
                    phone:
                    firstname:
                    lastname:
                    username:
                    sub_account_number:
                    language:
                    currency:
                    address:
                    street:
                    postcode:
                    city:
                    province_name:
                    country_name:
                    logo:
                    description:
                    website:
                    website_two:
                    facebook:
                    instagram:
                    youtube:
                    billing_address:
                    shipping_address:
                    civility:
                    function:
                    legal_sector:
                    fiscal_code:
                    legal_type:
                    social_capital:
                    turnover:
                    signatory_firstname:
                    signatory_lastname:
                    signatory_civility:
                    signatory_email:
                    signatory_phone:
                    signatory_function:
                    signatory_country:
                    signatory_birth_day:
                    signatory_birth_place:
                    accountant_firstname:
                    accountant_lastname:
                    accountant_civility:
                    accountant_email:
                    accountant_phone:
                    last_login:
                    subscription_id:
                    subscription_name:
                    subscription_price:
                    subscription_start_date:
                    subscription_end_date:
                    subscription_max_offer_number:
                    login_number:
                    number_employee:
                    support_openings:
                    accepted_contact:
                    accepted_contact_receiver:
                    waiting_contact:
                    asked_contact:
                    pending_cart:
                    order_completed_number:
                    order_refused_canceled_number:
                    order_total_amount:
                    order_pending_number:
                    order_average_amount:
                    quote_completed_number:
                    quote_pending_number:
                    quote_refused_number:
                    seller_bought_at_number:
                    coupon_used_number:
                    dispute_number:
                    message_received:
                    message_sent:
                    dynamic-field:
                    offer_number:
                    pricelist_number:
                    shipping_method_number:
                    pending_contact:
                    subAccount:
                    coupon_number:
                    order_received_number:
                    quote_number:
                filters:
                    roles:
                        type: AppBundle\Form\Type\CompanyTypeType



imports:
    - { resource: config.yml }
    # Ignored if not exists, generated by uppler:dev:marketplace:switch, can be updated manualy
    - { resource: config_dev_marketplace.yml, ignore_errors: true }
    # Ignored if not exists, you can customize config for your env
    - { resource: config_dev_custom.yml, ignore_errors: true }

doctrine:
    dbal:
        logging: true
        profiling: true

framework:
    router:
        resource: '%kernel.root_dir%/config/routing_dev.yml'
        strict_requirements: false
    profiler: { only_exceptions: false }
    http_client:
        default_options:
            verify_peer: false
            verify_host: false

web_profiler:
    toolbar: "%enable_profiler%"
    intercept_redirects: false

maker:
    root_namespace: 'AppBundle'

swiftmailer:
    transport: "%mailer_transport%"
    host:      "%mailer_host%"
    username:  "%mailer_user%"
    password:  "%mailer_password%"
    port: '%mailer_port%'
    spool: { type: memory }

expert_coder_swiftmailer_send_grid:
    api_key: '%sendgrid_api_key%'

monolog:
    channels: ['payment']
    handlers:
        main:
            type: stream
            path: '%kernel.logs_dir%/%kernel.environment%.log'
            level: notice
            channels: '!event'

        console:
            type:   console
            process_psr_3_messages: false
            channels: ['!event', '!doctrine', '!console']

        payment:
            type: stream
            path: '%kernel.logs_dir%/payment_debug.log'
            level: debug
            channels: ['payment']

app:
    dev_tools:
        translations:
            api_key: ~
            api_host: ~
            languages:
                - en
                - fr
                - de
                - es
                - it
                - ja
                - ko
                - pt
                - ru
                - tr
                - zh
                - ar
            domains:
                - messages
                - validators
                - flashes

debug:
   dump_destination: "tcp://%env(VAR_DUMPER_SERVER)%"

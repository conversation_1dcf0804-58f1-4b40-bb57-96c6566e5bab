{% extends 'BackOffice/layout.html.twig' %}
{% import 'Macros/buttons.html.twig' as buttons %}
{% import 'Macros/misc.html.twig' as misc %}
{% from 'BackOffice/Payment/macros.html.twig' import list %}
{% set withExport = app.request.get('_with_export') %}

{% block bodyclass %}bo-payments{% endblock %}

{% block content %}
    <h1 class="title-page">{{ ('uppler.administrator.payment.index_header')|trans }}</h1>
    {% set type = app.request.get('type', 'order') %}
    <div class="page-header widget-invoices">
        <div class="actions-menu">
            {{ buttons.btn(path('back_office_payment_index_type', { 'type' : constant("AppBundle\\Client\\Bank\\BankClientInterface::TYPE_ORDER") }), 'uppler.payment.invoice.type.order', null, null, (type == 'order' or type is null ? 'btn-default btn-payment-list active' : 'btn-default btn-payment-list')) }}

            {{ buttons.btn(path('back_office_payment_index_type', { 'type' : constant("AppBundle\\Client\\Bank\\BankClientInterface::TYPE_FEE") }), 'uppler.payment.invoice.type.fee', 'fee-nav', null, (type == constant("AppBundle\\Client\\Bank\\BankClientInterface::TYPE_FEE") ? 'btn-default btn-payment-list active' : 'btn-default btn-payment-list')) }}

            {{ buttons.btn(path('back_office_payment_index_type', { 'type' : constant("AppBundle\\Client\\Bank\\BankClientInterface::TYPE_CREDIT") }), 'uppler.payment.invoice.type.credit', 'credit-nav', null, (type == constant("AppBundle\\Client\\Bank\\BankClientInterface::TYPE_CREDIT") ? 'btn-default btn-payment-list active' : 'btn-default btn-payment-list')) }}

            {{ buttons.btn(path('back_office_payment_index_type', { 'type' : constant("AppBundle\\Client\\Bank\\BankClientInterface::TYPE_REFUND") }), 'uppler.payment.invoice.type.refund', 'refund-nav', null, (type == constant("AppBundle\\Client\\Bank\\BankClientInterface::TYPE_REFUND") ? 'btn-default btn-payment-list active' : 'btn-default btn-payment-list')) }}

            {{ buttons.btn(path('back_office_payment_index_type', { 'type' : constant("AppBundle\\Client\\Bank\\BankClientInterface::TYPE_FEE_PAYOUT") }), 'uppler.payment.invoice.type.fee_payout', 'payout-fee-nav', null, (type == constant("AppBundle\\Client\\Bank\\BankClientInterface::TYPE_FEE_PAYOUT") ? 'btn-default btn-payment-list active' : 'btn-default btn-payment-list')) }}

            {{ buttons.btn(path('back_office_payment_index_type', { 'type' : constant("AppBundle\\Client\\Bank\\BankClientInterface::TYPE_PAYOUT") }), 'uppler.payment.invoice.type.payout', 'payout-nav', null, (type == constant("AppBundle\\Client\\Bank\\BankClientInterface::TYPE_PAYOUT") ? 'btn-default btn-payment-list active' : 'btn-default btn-payment-list')) }}

            {{ buttons.btn(path('back_office_payment_index_type', { 'type' : constant("AppBundle\\Client\\Bank\\BankClientInterface::TYPE_SUBSCRIPTION") }), 'uppler.payment.invoice.type.subscription', null, null, (type == constant("AppBundle\\Client\\Bank\\BankClientInterface::TYPE_SUBSCRIPTION") ? 'btn-default btn-payment-list active' : 'btn-default btn-payment-list')) }}

            {{ buttons.btn(path('back_office_payment_index_type', { 'type' : constant("AppBundle\\Client\\Bank\\BankClientInterface::TYPE_ALL") }), 'uppler.payment.invoice.type.all', 'payout-nav', null, (type == constant("AppBundle\\Client\\Bank\\BankClientInterface::TYPE_ALL") ? 'btn-default btn-payment-list active' : 'btn-default btn-payment-list')) }}
            <div class="clearfix"></div>
        </div>
    </div>

    <div class="widget-container widget-list widget-payment-period boxed">
        {% set criteria = app.request.get('criteria')|default({})|merge({'type': type}) %}

        {{ render(controller('AppBundle:Form:filter', {'type': filterType('payment', 'BackOffice'), 'template': 'BackOffice/Payment/_filterForm.html.twig', 'criteria': criteria, '_route' : app.request.attributes.get('_route'), 'export': withExport })) }}

        <div class="inner">
            {{ misc.pagination(payments) }}
            {{ list(payments) }}
            {{ misc.pagination(payments) }}
        </div>
    </div>
{% endblock %}

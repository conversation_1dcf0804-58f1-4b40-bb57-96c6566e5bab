{% extends 'BackOffice/Default/index.html.twig' %}

{% import 'Macros/misc.html.twig' as misc %}
{% import 'Macros/buttons.html.twig' as buttons %}

{% block resourcesBox %}
    <table class="table table-striped">
        <thead>
            <tr>
                <th>{{ 'uppler.administrator.configuration.shipping_method_gateways'|trans }}</th>
                <th> </th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>
                    {% for shippingMethodGateway in configuration.shippingMethodGateways %}
                        <label class="label label-info">{{ ('uppler.administrator.shipping_method_gateways.'~shippingMethodGateway)|trans }}</label>
                    {% endfor %}
                </td>
                <td>
                    {% if is_granted('uppler_administrator', 'shop:gateway_setup:update') %}
                        {{ buttons.edit(path('back_office_shipping_gateway_setup_gateway_update'), null) }}
                    {% endif %}
                </td>
            </tr>
        </tbody>
    </table>

    {% if is_granted('uppler_administrator', 'shop:gateway_setup:credentials') %}
        {% set available_gateway = available_gateway(constant('AppBundle\\Entity\\GatewaySetupInterface::GATEWAY_TYPE_SHIPPING')) %}

        {% if available_gateway is not empty %}
            <div class="page-header">
                <div class="btn-group">
                    <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        {{ 'uppler.administrator.shipping_gateway_setup.create'|trans }}
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu pull-left">
                        {% for gateway_name in available_gateway %}
                            <li>
                                <a href="{{ path('back_office_shipping_gateway_setup_create', {'gatewayName': gateway_name}) }}">
                                    {{ ('uppler.administrator.shipping_method_gateways.'~gateway_name)|trans }}
                                </a>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        {% endif %}
    {% endif %}

    {{ parent() }}
{% endblock %}

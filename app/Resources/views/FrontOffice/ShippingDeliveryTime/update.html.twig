{% extends 'FrontOffice/Company/showroomLayout.html.twig' %}
{% import 'Macros/actions.html.twig' as actions %}
{% import 'Macros/buttons.html.twig' as buttons %}

{% block showroom_content %}
    {% include 'FrontOffice/Product/_header.html.twig' %}
	<div class="widget-action">
		{{ buttons.manage(path('uppler_shop_shipping_method_index'), 'uppler.shipping_method.manage'|trans) }}
        {{ buttons.manage(path('uppler_shop_shipping_method_show', {'id': shipping_method.id}), 'uppler.shipping_method.show_header'|trans) }}
        {{ buttons.manage(path('uppler_shop_shipping_method_show_delivery_times', {'id': shipping_method.id}), 'uppler.shipping_method.update_delivery_times') }}
	</div>
	<div class="widget-container boxed">
		<h3 class="widget-title">{{ 'uppler.shipping_method.update_delivery_times'|trans }}</h3>
		{% if app.request.attributes.get('_route') == 'uppler_shop_shipping_method_update' %}
			{% set path = 'uppler_shop_shipping_method_update' %}
		{% elseif app.request.attributes.get('_route') == 'uppler_shop_shipping_method_update_delivery_times' %}
			{% set path = 'uppler_shop_shipping_method_update_delivery_times' %}
		{% endif %}

		{{ form_start(form, {'action': path(path, {'id': shipping_method.id}), 'attr': {'novalidate': 'novalidate', 'class': 'form-shipping-method'}}) }}
    		<div class="inner">
				{% include 'FrontOffice/ShippingDeliveryTime/_form.html.twig' %}
		    </div>
		    <div class="row-submit">
		    	{{ actions.update() }}
			</div>
		{{ form_end(form, {render_rest: false}) }}

	</div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
	{{ encore_entry_script_tags('frontoffice_shipping_delivery_time') }}

{% endblock javascripts %}
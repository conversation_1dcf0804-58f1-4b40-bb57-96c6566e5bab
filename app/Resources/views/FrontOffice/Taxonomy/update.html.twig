{% extends 'FrontOffice/Company/showroomLayout.html.twig' %}
{% import 'Macros/actions.html.twig' as actions %}
{% import 'Macros/buttons.html.twig' as buttons %}

{% block showroom_content %}
<div class="widget-action">
    {{ buttons.manage(path('uppler_shop_taxonomy_index'), 'uppler.taxonomy.manage'|trans) }}
    {{ buttons.delete(path('uppler_shop_taxonomy_delete', {'id': taxonomy.id})) }}
</div>
<div class="widget-container boxed">
    <h3 class="widget-title">{{ 'uppler.taxonomy.create_header'|trans }}</h3>

    {{ form_start(form, {'action': path('uppler_shop_taxonomy_update', {'id': taxonomy.id, '_method': 'PUT'}), 'attr': {'novalidate': 'novalidate'}}) }}
        <div class="inner">

            {% include 'FrontOffice/Taxonomy/_form.html.twig' %}
        </div>
        <div class="row-submit">
            {{ actions.update(path('uppler_shop_taxonomy_index'), null, null, true) }}
        </div>
    {{ form_end(form) }}
</div>
{% endblock %}

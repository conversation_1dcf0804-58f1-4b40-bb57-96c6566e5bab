{% extends 'FrontOffice/layout.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('frontoffice_data_importer') }}
{% endblock %}

{% block content %}
    <div id="data-importer" class="data-importer-product">
        {% set importer = form.vars.value %}
        {% include [
            'FrontOffice/DataImporter/_header_' ~ resourceName ~ '.html.twig',
            'FrontOffice/DataImporter/_header.html.twig'
        ] %}

        {% if importer.hasError %}
            <div class="panel panel-danger">
                <div class="panel-heading text-center">
                    <span>{{ 'uppler.data_importer.errors.title'|trans }}</span>
                </div>
                <div class="panel-body">
                    {% set autoResolutionHash = false %}
                    <ul>
                        {% for error in importer.errors %}
                            <li>
                                {{ error.key|trans(error.parameters, error.domain) }}
                            </li>
                            {% if error.metadata.cause|default == 'autoresolution' %}
                                {% set autoResolutionHash = error.metadata.origin|default %}
                            {% endif %}
                        {% endfor %}
                    </ul>
                </div>
                <div class="panel-footer text-right">
                    <a href="{{ path('uppler_data_importer_configuration_index', {'resourceName': resourceName}) }}" class="btn btn-sm btn-default">
                        {{ 'uppler.data_importer.actions.back_to_list'|trans }}
                    </a>

                    {% if autoResolutionHash %}
                        <button type="button" data-target="{{ path('uppler_data_importer_remove_autoresolution', {'hash': autoResolutionHash}) }}" class="btn btn-sm btn-danger btn-remove-autoresolution">
                            <i class='fa fa-trash'></i>
                            {{ 'uppler.data_importer.actions.remove_auto_resolution'|trans }}
                        </button>
                    {% endif %}
                </div>
            </div>
        {% else %}
            {% set warningForms = uppler_data_importer_ordered_warnings(form.unresolvedWarnings.children) %}
            {% if warningForms is not empty %}
                {{ form_start(form) }}
                {% set minRow = importer.minRowWithUnresolvedWarning + 1 %}
                {% set maxRow = importer.maxRowWithUnresolvedWarning + 1 %}
                <div class="widget-spinner reporting-content">
                    {% include 'FrontOffice/DataImporter/_report_actions.html.twig' with {'warning_rows': true} %}
                    {% include 'Commons/_spinner.html.twig' %}
                    {{ form_errors(form) }}
                    {% set position = 0 %}

                    {# ugly fix for header warnings #}
                    {% if minRow == 1 and maxRow == 1 %}
                        {% set minRow = 0 %}
                    {% endif %}
                    {% for i in minRow..maxRow %}
                        {% if loop.last %}
                            {% set position = 0 %}
                        {% endif %}
                        <div id="line-number-{{ position }}" >
                            {% include 'FrontOffice/DataImporter/_data_importer_flash_messages_row.inc.html.twig' with { 'position': position } only %}
                            {% if warningForms[i] is defined %}
                                {% include [
                                    'FrontOffice/DataImporter/warning_' ~ resourceName ~ '.inc.html.twig',
                                    'FrontOffice/DataImporter/warning.inc.html.twig'
                                ] %}
                                {% set position = position +1 %}
                            {% endif %}
                        </div>
                    {% endfor %}
                    {% include 'FrontOffice/DataImporter/_report_actions.html.twig' %}
                    <div class="hidden">
                        {% for warning in form.unresolvedWarnings %}
                            {{ form_widget(warning.response) }}
                        {% endfor %}
                        {{ form_rest(form) }}
                    </div>
                </div>
                {{ form_end(form) }}
            {% else %}
                {% if importer.isCancelled %}
                    <div class="alert alert-primary text-center">
                        <div class="alert-body">
                            <p>{{ "uppler.data_importer.cancelled"|trans }}</p>
                        </div>
                    </div>
                {% endif %}
            {% endif %}
        {% endif %}
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('frontoffice_data_importer') }}
{% endblock %}

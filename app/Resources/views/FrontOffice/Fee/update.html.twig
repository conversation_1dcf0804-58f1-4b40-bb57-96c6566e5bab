{% extends 'FrontOffice/Company/showroomLayout.html.twig' %}
{% import 'Macros/buttons.html.twig' as buttons %}
{% import 'Macros/actions.html.twig' as actions %}

{% block showroom_content %}
    {% include 'FrontOffice/Product/_header.html.twig' %}
    <div class="widget-action">
        {{ buttons.manage(path('front_office_fee_index'), 'uppler.fee.manage'|trans) }}
        {{ buttons.delete(path('front_office_fee_delete', {'id': fee.id})) }}
    </div>
    <div class="widget-container boxed">
        <h3 class="widget-title">{{ 'uppler.fee.update_header'|trans }}</h3>
        {{ form_start(form, {'action': path('front_office_fee_update', {'id': fee.id}), 'attr': {'novalidate': 'novalidate', 'class': 'form-fee'}}) }}
            <div class="inner">
                {{ form_errors(form) }}
                {% include 'FrontOffice/Fee/_form.html.twig' %}
                {{ actions.update() }}
            </div>
        {{ form_end(form) }}
    </div>
{% endblock %}

api_buyer_file_create:
    path: /
    methods: [POST]
    defaults:
        _controller: app.controller.api_buyer.file_entity:createAction
        _uppler_resource:
            form: AppBundle\Form\Type\Resource\Api\FileEntityType
            serialization_groups: [api_buyer_file_create]
            resource_voter: [create_api]
            mandatory_values: [file]

api_buyer_file_update:
    path: /{id}
    methods: [PATCH]
    defaults:
        _controller: app.controller.api_buyer.file_entity:updateAction
        _uppler_resource:
            form: AppBundle\Form\Type\Resource\Api\FileEntityType
            validation_groups: [api_buyer_file]
            serialization_groups: [api_buyer_file_update]
            resource_voter: [update_api]
    requirements:
        id: \d+

api_buyer_file_delete:
    path: /{id}
    methods: [DELETE]
    defaults:
        _controller: app.controller.api_buyer.file_entity:deleteAction
        _uppler_resource:
            resource_voter: [delete_api]
    requirements:
        id: \d+

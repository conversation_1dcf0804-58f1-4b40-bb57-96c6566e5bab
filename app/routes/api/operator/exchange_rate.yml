api_operator_exchange_rate_index:
    path: /
    methods: [GET]
    defaults:
        _controller: app.controller.api_operator.exchange_rate:indexAction
        _uppler_resource:
            sortable: true
            filterable: true
            paginate: 20
            expandables: [currency]
            serialization_groups: [api_exchange_rate_index]
            allowed_sorting: [id]
            allowed_criteria: [id]

api_operator_exchange_rate_show:
    path: /{id}
    methods: [GET]
    defaults:
        _controller: app.controller.api_operator.exchange_rate:showAction
        _uppler_resource:
            serialization_groups: [api_exchange_rate_show]
            expandables: [currency]
    requirements:
        id: \d+

api_operator_exchange_rate_create:
    path: /
    methods: [POST]
    defaults:
        _controller: app.controller.api_operator.exchange_rate:createAction
        _uppler_resource:
            form: AppBundle\Form\Type\Resource\Api\ExchangeRateType
            mandatory_values: [currency_base_id, currency_target_id, rate, is_auto]
            validation_groups: [exchange_rate_create]
            serialization_groups: [api_exchange_rate_create]
            redirect: api_operator_exchange_rate_show

api_operator_exchange_rate_update:
    path: /{id}
    methods: [PATCH]
    defaults:
        _controller: app.controller.api_operator.exchange_rate:updateAction
        _uppler_resource:
            form: AppBundle\Form\Type\Resource\Api\ExchangeRateType
            serialization_groups: [api_exchange_rate_update]
    requirements:
        id: \d+
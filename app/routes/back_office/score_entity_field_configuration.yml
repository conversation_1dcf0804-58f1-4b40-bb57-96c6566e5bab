back_office_score_entity_field_configuration_create:
    path: /new/{scoreEntityConfigurationId}
    methods: [GET, POST]
    defaults:
        _breadcrumb: [bo,advanced_settings,score_configuration,score_entity_field_configuration_create]
        _controller: app.controller.back_office.score_entity_field_configuration:createAction
        _resource_name: score_entity_field_configuration
        _uppler_resource:
            template: BackOffice/ScoreEntityFieldConfiguration/create.html.twig
            form: 'AppBundle\Form\Type\Resource\BackOffice\ScoreEntityFieldConfigurationType'
            redirect:
                route: back_office_score_configuration_show
                parameters: {'id': $scoreEntityConfigurationId}
    requirements:
        scoreEntityConfigurationId: \d+


back_office_score_entity_field_configuration_update:
    path: /{id}/edit/{scoreEntityConfigurationId}
    methods: [GET, POST]
    defaults:
        _breadcrumb: [bo,advanced_settings,score_configuration,score_entity_field_configuration_update]
        _controller: app.controller.back_office.score_entity_field_configuration:updateAction
        _resource_name: score_entity_field_configuration
        _uppler_resource:
            template: BackOffice/ScoreEntityFieldConfiguration/update.html.twig
            form: 'AppBundle\Form\Type\Resource\BackOffice\ScoreEntityFieldConfigurationType'
            redirect:
                route: back_office_score_configuration_show
                parameters: {'id': $scoreEntityConfigurationId}
    requirements:
        id: \d+
        scoreEntityConfigurationId: \d+

back_office_score_entity_field_configuration_delete:
    path: /{id}/{scoreEntityConfigurationId}
    methods: [DELETE]
    defaults:
        _controller: app.controller.back_office.score_entity_field_configuration:deleteAction
        _uppler_resource:
            template: BackOffice/Misc/delete.html.twig
            redirect:
                route: back_office_score_configuration_show
                parameters: {'id': $scoreEntityConfigurationId}
    requirements:
        id: \d+
        scoreEntityConfigurationId: \d+
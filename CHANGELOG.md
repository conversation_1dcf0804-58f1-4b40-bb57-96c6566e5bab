## [4.107.2](https://github.com/uppler/uppler/compare/v4.107.1...v4.107.2) (2025-07-01)


### Bug Fixes

* 🌷 bugs_upl [#62303](https://github.com/uppler/uppler/issues/62303) Erreur 500 lors du choix de la méthode de livraison  ([f94cd27](https://github.com/uppler/uppler/commit/f94cd270895366803be7d78382549bf43042f2f6)), closes [#20426](https://github.com/uppler/uppler/issues/20426)
* 💥 Log erreur unresolved host Upelgo  ([c022626](https://github.com/uppler/uppler/commit/c0226263c4e767ac3a3e0b75090b23bbce92aced)), closes [#20427](https://github.com/uppler/uppler/issues/20427)
* bugs_upl [#62284](https://github.com/uppler/uppler/issues/62284) Problème information livraison connecteur  ([e572528](https://github.com/uppler/uppler/commit/e57252824a321dd1cf3442cef204e9325b3c33b7)), closes [#20425](https://github.com/uppler/uppler/issues/20425) [#20425](https://github.com/uppler/uppler/issues/20425)

## [4.107.1](https://github.com/uppler/uppler/compare/v4.107.0...v4.107.1) (2025-06-26)


### Bug Fixes

* 🌷 bugs_upl [#62141](https://github.com/uppler/uppler/issues/62141) Moteur de recherche v1  ([3809a41](https://github.com/uppler/uppler/commit/3809a41d440fb028a1f95cc17d57f7fb1c6bd699)), closes [#20420](https://github.com/uppler/uppler/issues/20420) [#285](https://github.com/uppler/uppler/issues/285) [#14344](https://github.com/uppler/uppler/issues/14344)

# [4.107.0](https://github.com/uppler/uppler/compare/v4.106.0...v4.107.0) (2025-06-25)


### Features

* ⭐ Mock tests API  ([3f34eae](https://github.com/uppler/uppler/commit/3f34eae4129fd8b94222bbca2d4534905d09e143)), closes [#20417](https://github.com/uppler/uppler/issues/20417)

# [4.106.0](https://github.com/uppler/uppler/compare/v4.105.2...v4.106.0) (2025-06-24)


### Bug Fixes

* 🌷 bugs_upl [#61914](https://github.com/uppler/uppler/issues/61914) Paiement refusé mais commande envoyée au vendeur  ([6ba2188](https://github.com/uppler/uppler/commit/6ba21886e5d3f03c72ac0604a298f583ec4beb56)), closes [#20412](https://github.com/uppler/uppler/issues/20412)
* 🌷 bugs_upl [#62035](https://github.com/uppler/uppler/issues/62035) Valeur incorrecte de l'attribut available_on_demand dans l'API  ([96d4942](https://github.com/uppler/uppler/commit/96d49426b09269a061795af7181609de6cc4df43)), closes [#20415](https://github.com/uppler/uppler/issues/20415) [#20415](https://github.com/uppler/uppler/issues/20415)


### Features

* ⭐  VEL Autoriser l'opérateur à utiliser des images du vendeur pour créer un produit master  ([25b3910](https://github.com/uppler/uppler/commit/25b391020dd8be078fba361110186b9cc90ce488)), closes [#20416](https://github.com/uppler/uppler/issues/20416)

## [4.105.2](https://github.com/uppler/uppler/compare/v4.105.1...v4.105.2) (2025-06-24)


### Bug Fixes

* 🌷 bugs_upl [#61941](https://github.com/uppler/uppler/issues/61941) Import produit - paramètre remove ne fonctionne pas  ([4727e00](https://github.com/uppler/uppler/commit/4727e00d7ea9c246b46715712471d9c5886e5a5e)), closes [#20409](https://github.com/uppler/uppler/issues/20409) [#20409](https://github.com/uppler/uppler/issues/20409)
* 💥 Fix UpdateCatalogStateHandler  ([258282f](https://github.com/uppler/uppler/commit/258282f906e271d99a0a6608d9e1597bffc1cca8)), closes [#20413](https://github.com/uppler/uppler/issues/20413)

## [4.105.1](https://github.com/uppler/uppler/compare/v4.105.0...v4.105.1) (2025-06-19)


### Bug Fixes

* 🌷 bugs_upl [#61840](https://github.com/uppler/uppler/issues/61840) Analyse down Panachats  ([e69a953](https://github.com/uppler/uppler/commit/e69a9537213f3bcc97d5bc885da7e94ddd28a5ff)), closes [#20402](https://github.com/uppler/uppler/issues/20402)
* 🌷 bugs_upl [#62000](https://github.com/uppler/uppler/issues/62000) Méthode de livraison par point relais ne s'applique plus  ([91ea2fd](https://github.com/uppler/uppler/commit/91ea2fdd087589a7393bbc5323738f1c09c596cc)), closes [#20410](https://github.com/uppler/uppler/issues/20410)
* 💥 Operator sample files not available  ([bb9c3b2](https://github.com/uppler/uppler/commit/bb9c3b263b536a207c3d5bbf5cfa86f571a07441)), closes [#20408](https://github.com/uppler/uppler/issues/20408)

# [4.105.0](https://github.com/uppler/uppler/compare/v4.104.1...v4.105.0) (2025-06-18)


### Bug Fixes

* 🌷 bugs_upl [#61020](https://github.com/uppler/uppler/issues/61020) Offres non présente dans l'export - V2  ([7507aba](https://github.com/uppler/uppler/commit/7507aba0dc293fdf1624e43602fae20d995c3425)), closes [#20406](https://github.com/uppler/uppler/issues/20406)


### Features

* ⭐ MAD4 - Newsletter  ([4f2c2a7](https://github.com/uppler/uppler/commit/4f2c2a7f7cb1532af97661bd64a91a92d3f733e4)), closes [#20399](https://github.com/uppler/uppler/issues/20399) [#20405](https://github.com/uppler/uppler/issues/20405) [#20405](https://github.com/uppler/uppler/issues/20405)

## [4.104.1](https://github.com/uppler/uppler/compare/v4.104.0...v4.104.1) (2025-06-16)


### Bug Fixes

* 🌷 bugs_upl [#61692](https://github.com/uppler/uppler/issues/61692) Opti - Nettoyage données BO  ([a3942bd](https://github.com/uppler/uppler/commit/a3942bd397e1093bf36075ead3097904023205db)), closes [#20397](https://github.com/uppler/uppler/issues/20397)

# [4.104.0](https://github.com/uppler/uppler/compare/v4.103.1...v4.104.0) (2025-06-16)


### Bug Fixes

* 💥 Amélioration logs data exporter/importer  ([f6779a8](https://github.com/uppler/uppler/commit/f6779a83e1d0b4ab7ae12f3a03fa2398d7746565)), closes [#20394](https://github.com/uppler/uppler/issues/20394)


### Features

* ⭐ Mise à jour éditeur de texte - Pages & modèles  ([2d2fa7e](https://github.com/uppler/uppler/commit/2d2fa7e6f3d5404464b53581bd667c3b99658a9e)), closes [#20391](https://github.com/uppler/uppler/issues/20391)

## [4.103.1](https://github.com/uppler/uppler/compare/v4.103.0...v4.103.1) (2025-06-12)


### Bug Fixes

* 🌷 bugs_upl [#61682](https://github.com/uppler/uppler/issues/61682) Graylog - AppBundle\Twig\Extension\ProductExtension::getPathProduct(): Argument [#1](https://github.com/uppler/uppler/issues/1) ($product) must be of type AppBundle\Entity\ProductInterface, null given  ([ecd50c6](https://github.com/uppler/uppler/commit/ecd50c6fabce800778a75213c8e5cf394fa3e7b6)), closes [#20395](https://github.com/uppler/uppler/issues/20395)
* 💥 Amélioration logs indexation  ([68befa5](https://github.com/uppler/uppler/commit/68befa543ca75112e3b0a4704017da3d7e3237c2)), closes [#20392](https://github.com/uppler/uppler/issues/20392)

# [4.103.0](https://github.com/uppler/uppler/compare/v4.102.4...v4.103.0) (2025-06-10)


### Bug Fixes

* 🌷 bugs_upl [#60151](https://github.com/uppler/uppler/issues/60151) Propriété type texte ne peut pas être mise en avant sur la recherche V2  ([662a6df](https://github.com/uppler/uppler/commit/662a6df276c056f3d4a1c466164b0e7f73ff5319)), closes [#20383](https://github.com/uppler/uppler/issues/20383) [#20383](https://github.com/uppler/uppler/issues/20383) [#20383](https://github.com/uppler/uppler/issues/20383) [#20383](https://github.com/uppler/uppler/issues/20383)
* 🌷 bugs_upl [#61522](https://github.com/uppler/uppler/issues/61522) Fonctionnalité non visible  ([e6d5117](https://github.com/uppler/uppler/commit/e6d511761236be220b3098fd3348c812e29eedc7)), closes [#20384](https://github.com/uppler/uppler/issues/20384) [#20384](https://github.com/uppler/uppler/issues/20384) [#20389](https://github.com/uppler/uppler/issues/20389) [#20384](https://github.com/uppler/uppler/issues/20384)
* 💥 Tests d'acceptance attribution bon d'achat  ([de1c2e6](https://github.com/uppler/uppler/commit/de1c2e66838511eaf82831bd3a043c3213010d67)), closes [#20390](https://github.com/uppler/uppler/issues/20390)


### Features

* ⭐ Mise à jour Docker iso OVH  ([8296855](https://github.com/uppler/uppler/commit/829685525b99f29890a1041e75f416aedb05d29b)), closes [#20058](https://github.com/uppler/uppler/issues/20058) [Codeception/Codeception#5349](https://github.com/Codeception/Codeception/issues/5349)

## [4.102.4](https://github.com/uppler/uppler/compare/v4.102.3...v4.102.4) (2025-06-10)


### Bug Fixes

* 🌷 bugs_upl [#60145](https://github.com/uppler/uppler/issues/60145) UX Tri par catégorie en recherche v2  ([9f7a602](https://github.com/uppler/uppler/commit/9f7a6020dc5042bdf2323779e7d492a328ce3108)), closes [#20364](https://github.com/uppler/uppler/issues/20364) [#20364](https://github.com/uppler/uppler/issues/20364) [#20364](https://github.com/uppler/uppler/issues/20364)
* 🌷 bugs_upl [#60889](https://github.com/uppler/uppler/issues/60889) Correctif sur Promotion non filtrable sur le shop ne filtre pas la bonne promotion  ([2e981eb](https://github.com/uppler/uppler/commit/2e981eb6c6de7aa67b7a6e4ad7c303717f9abd85)), closes [#20347](https://github.com/uppler/uppler/issues/20347)
* 🌷 bugs_upl [#61458](https://github.com/uppler/uppler/issues/61458) Erreur lors de l'appel Upelgo - Stockage du token de connexion  ([9e7b523](https://github.com/uppler/uppler/commit/9e7b52300b72c9f03716accacf3f6d724e63f10a)), closes [#20387](https://github.com/uppler/uppler/issues/20387)

## [4.102.3](https://github.com/uppler/uppler/compare/v4.102.2...v4.102.3) (2025-06-05)


### Bug Fixes

* 🌷 bugs_upl [#60656](https://github.com/uppler/uppler/issues/60656) Erreur 500 log Gedmo dirty  ([00ef71e](https://github.com/uppler/uppler/commit/00ef71e9e65db13491fa54be5acc91f4bb8bf237)), closes [#20380](https://github.com/uppler/uppler/issues/20380) [#60461](https://github.com/uppler/uppler/issues/60461)
* 🌷 bugs_upl [#61020](https://github.com/uppler/uppler/issues/61020) Offres non présente dans l'export  ([bd262ee](https://github.com/uppler/uppler/commit/bd262ee79f7a495ee95dcd0e68a97d8d23f13a56)), closes [#20379](https://github.com/uppler/uppler/issues/20379)

## [4.102.2](https://github.com/uppler/uppler/compare/v4.102.1...v4.102.2) (2025-06-04)


### Bug Fixes

* 🌷 bugs_upl [#60997](https://github.com/uppler/uppler/issues/60997) Erreurs graylog proachat  ([4cd96b7](https://github.com/uppler/uppler/commit/4cd96b70bc852658c0574fb7b2dcd00096a9a202)), closes [#20363](https://github.com/uppler/uppler/issues/20363) [#20363](https://github.com/uppler/uppler/issues/20363)
* 🌷 bugs_upl [#61384](https://github.com/uppler/uppler/issues/61384) Problème d'affichage sur la map quand plus d'une méthode de livraison  ([104337f](https://github.com/uppler/uppler/commit/104337f14ace692e4a1e94a0b0b0a332e9f4109f)), closes [#20374](https://github.com/uppler/uppler/issues/20374)
* 🌷 bugs_upl [#61405](https://github.com/uppler/uppler/issues/61405) Configuration passerelle MO Impossible  ([efdc418](https://github.com/uppler/uppler/commit/efdc418ae51dccbe70874a6cb14a524da5819c4b)), closes [#20375](https://github.com/uppler/uppler/issues/20375)
* 🌷 bugs_upl [#61458](https://github.com/uppler/uppler/issues/61458) Erreur lors de l'appel Upelgo  ([5fe29ce](https://github.com/uppler/uppler/commit/5fe29ce8cdc1d8bbec0778fcfc089d7edfdb5496)), closes [#20377](https://github.com/uppler/uppler/issues/20377)

## [4.102.1](https://github.com/uppler/uppler/compare/v4.102.0...v4.102.1) (2025-06-04)


### Bug Fixes

* 🌷 bugs_upl [#61383](https://github.com/uppler/uppler/issues/61383) Affichage d'infos supplémentaires sur livraison  ([96b1eda](https://github.com/uppler/uppler/commit/96b1eda0354ce6d718cb4cec3461d232cc64e2c8)), closes [#20371](https://github.com/uppler/uppler/issues/20371)

# [4.102.0](https://github.com/uppler/uppler/compare/v4.101.0...v4.102.0) (2025-06-02)


### Bug Fixes

* 🌷 bugs_upl [#61382](https://github.com/uppler/uppler/issues/61382) Problème point relais avec plus d'un produit  ([0f4cd89](https://github.com/uppler/uppler/commit/0f4cd894087a87df6a4e23891480ee2d7e7eff49)), closes [#20370](https://github.com/uppler/uppler/issues/20370)
* 💥 MAD3 - Remonter infos Upelgo sur Sage  ([b6b3ecd](https://github.com/uppler/uppler/commit/b6b3ecdf8f905f876cde309e2f89e05dac2be652)), closes [#20372](https://github.com/uppler/uppler/issues/20372)


### Features

* ⭐ DEV TOOLS - Intégration LibreTranslate  ([561752f](https://github.com/uppler/uppler/commit/561752fedfae35e17807013631c2203c81c70f07)), closes [#20366](https://github.com/uppler/uppler/issues/20366)

# [4.101.0](https://github.com/uppler/uppler/compare/v4.100.1...v4.101.0) (2025-05-30)


### Features

* ⭐ TODO Madewis - Logistique - Upela : Relais Colis  ([7906b3e](https://github.com/uppler/uppler/commit/7906b3e4716b562ee366d06d69144e8cf62a62f5)), closes [#20352](https://github.com/uppler/uppler/issues/20352) [#20352](https://github.com/uppler/uppler/issues/20352) [#20352](https://github.com/uppler/uppler/issues/20352) [#20352](https://github.com/uppler/uppler/issues/20352) [#20352](https://github.com/uppler/uppler/issues/20352) [#20352](https://github.com/uppler/uppler/issues/20352) [#20367](https://github.com/uppler/uppler/issues/20367)

## [4.100.1](https://github.com/uppler/uppler/compare/v4.100.0...v4.100.1) (2025-05-27)


### Bug Fixes

* 🌷 bugs_upl [#60983](https://github.com/uppler/uppler/issues/60983) Dépendance externe au code utilisée, non fiable  ([7ebc9ea](https://github.com/uppler/uppler/commit/7ebc9eaf3910dc4e0ee5bed65493d0301beb6bd9)), closes [#20359](https://github.com/uppler/uppler/issues/20359)

# [4.100.0](https://github.com/uppler/uppler/compare/v4.99.5...v4.100.0) (2025-05-23)


### Bug Fixes

* 🌷 bugs_upl [#60888](https://github.com/uppler/uppler/issues/60888) Inscription avec un email contenant un ;  ([c061c1b](https://github.com/uppler/uppler/commit/c061c1b7c1c18a64ad68ba4f64fd2e9087f0834f)), closes [#20349](https://github.com/uppler/uppler/issues/20349) [#20349](https://github.com/uppler/uppler/issues/20349) [#20350](https://github.com/uppler/uppler/issues/20350) [#20350](https://github.com/uppler/uppler/issues/20350) [#20350](https://github.com/uppler/uppler/issues/20350) [#20350](https://github.com/uppler/uppler/issues/20350)
* 💥 MAD3 - Génération bon d'achat - Notification dans mail de commande  ([b7f0d79](https://github.com/uppler/uppler/commit/b7f0d79a9ea0eb0b10a16cbbb6e7b79b447e84c2)), closes [#20362](https://github.com/uppler/uppler/issues/20362)
* 💥 MAD3 - Génération bon d'achat - Notification dans mail de commande  ([a093370](https://github.com/uppler/uppler/commit/a093370a63e8fdb5ee237d5bfc5b9730bb8daa3d)), closes [#20361](https://github.com/uppler/uppler/issues/20361)
* 💥 Méthode non existante appelée  ([7d777a6](https://github.com/uppler/uppler/commit/7d777a607de4e57fd394f5a90f45a9fccd204729)), closes [#20358](https://github.com/uppler/uppler/issues/20358)


### Features

* ⭐ MAD3 - Génération automatique d’un Bon d’achat  ([2e84ef1](https://github.com/uppler/uppler/commit/2e84ef135cfd0c25ace807b450d5c382a60f82be)), closes [#20338](https://github.com/uppler/uppler/issues/20338) [#20355](https://github.com/uppler/uppler/issues/20355) [#20355](https://github.com/uppler/uppler/issues/20355)

## [4.99.5](https://github.com/uppler/uppler/compare/v4.99.4...v4.99.5) (2025-05-21)


### Bug Fixes

* 🌷 bugs_upl [#60603](https://github.com/uppler/uppler/issues/60603) KYC validé sur Viva, non validé sur Uppler  ([8433495](https://github.com/uppler/uppler/commit/84334954c988ea15bcfcb0cdde9b03159a2555ad)), closes [#20339](https://github.com/uppler/uppler/issues/20339) [#20339](https://github.com/uppler/uppler/issues/20339) [#20339](https://github.com/uppler/uppler/issues/20339)

## [4.99.4](https://github.com/uppler/uppler/compare/v4.99.3...v4.99.4) (2025-05-21)


### Bug Fixes

* 🌷 bugs_upl [#60943](https://github.com/uppler/uppler/issues/60943) Taux de change automatique via Fixer KO en recette  ([cbc4d3a](https://github.com/uppler/uppler/commit/cbc4d3adc93c919243821a9989ad471b24bffe13)), closes [#20348](https://github.com/uppler/uppler/issues/20348) [#20348](https://github.com/uppler/uppler/issues/20348)

## [4.99.3](https://github.com/uppler/uppler/compare/v4.99.2...v4.99.3) (2025-05-15)


### Bug Fixes

* 🌷 bugs_upl [#60678](https://github.com/uppler/uppler/issues/60678) Sous comptes visibles en BO mais non en FO, donc non réactivable  ([e653a97](https://github.com/uppler/uppler/commit/e653a97685160323772a90c29187405cf67e4b8c)), closes [#20341](https://github.com/uppler/uppler/issues/20341)
* 🌷 bugs_upl [#60789](https://github.com/uppler/uppler/issues/60789) Promotion non filtrable sur le shop  ([b482808](https://github.com/uppler/uppler/commit/b482808b13b7d8ea1e4981b44276f0ef2e6559f7)), closes [#20343](https://github.com/uppler/uppler/issues/20343) [#20345](https://github.com/uppler/uppler/issues/20345) [#20345](https://github.com/uppler/uppler/issues/20345) [#20345](https://github.com/uppler/uppler/issues/20345)
* 💥 MAD2 - Statut de paiement "transféré" non dispo à la config  ([b4d01eb](https://github.com/uppler/uppler/commit/b4d01eb4c8f20a7098196e8e625286d805d0e1c6)), closes [#20342](https://github.com/uppler/uppler/issues/20342)

## [4.99.2](https://github.com/uppler/uppler/compare/v4.99.1...v4.99.2) (2025-05-12)


### Bug Fixes

* 🌷 bugs_upl [#60461](https://github.com/uppler/uppler/issues/60461) Status d'acceptation KO sur commande  ([8605f5e](https://github.com/uppler/uppler/commit/8605f5e561de3757f1d2da67179de10a2b6362ca)), closes [#20333](https://github.com/uppler/uppler/issues/20333) [#20334](https://github.com/uppler/uppler/issues/20334) [#20334](https://github.com/uppler/uppler/issues/20334)

## [4.99.1](https://github.com/uppler/uppler/compare/v4.99.0...v4.99.1) (2025-05-06)


### Bug Fixes

* 🌷 bugs_upl [#60417](https://github.com/uppler/uppler/issues/60417) Moteur de recherche : Filtre affiche l'ID de la valeur de propriété et non le nom + chargement dans le vide.  ([7afd574](https://github.com/uppler/uppler/commit/7afd574f3bad0ed947f48ca7d72b5c5a8008cc20)), closes [#20332](https://github.com/uppler/uppler/issues/20332)


### Reverts

* Revert "Remove invalid zone identifiers ADS files (which blocks cloning repos to an NTFS file system)" ([df4af32](https://github.com/uppler/uppler/commit/df4af3244cb291bd155d29ebcc6e4c344dab27ce))

# [4.99.0](https://github.com/uppler/uppler/compare/v4.98.0...v4.99.0) (2025-04-30)


### Features

* ⭐ MADEWIS SPRINT 2 ([8ced255](https://github.com/uppler/uppler/commit/8ced25501e0d04fe2ba56f0892f5888b44f5c718)), closes [#20297](https://github.com/uppler/uppler/issues/20297) [#20299](https://github.com/uppler/uppler/issues/20299) [#20299](https://github.com/uppler/uppler/issues/20299)

# [4.98.0](https://github.com/uppler/uppler/compare/v4.97.1...v4.98.0) (2025-04-28)


### Bug Fixes

* 🌷 bugs_upl [#60281](https://github.com/uppler/uppler/issues/60281) Page des factures en erreur 500 pour un acheteur  ([5ed58a2](https://github.com/uppler/uppler/commit/5ed58a2cd92a8d097e86f7b4cd02f86a1b035b72)), closes [#20320](https://github.com/uppler/uppler/issues/20320) [#20322](https://github.com/uppler/uppler/issues/20322) [#20322](https://github.com/uppler/uppler/issues/20322)


### Features

* ⭐ MAD2 - Validation auto des commandes  ([97ace05](https://github.com/uppler/uppler/commit/97ace055020bf207a85487f7013f4389941abcd8)), closes [#20324](https://github.com/uppler/uppler/issues/20324) [#20299](https://github.com/uppler/uppler/issues/20299) [#20299](https://github.com/uppler/uppler/issues/20299)

## [4.97.1](https://github.com/uppler/uppler/compare/v4.97.0...v4.97.1) (2025-04-24)


### Bug Fixes

* 🌷 💥 KO : bugs_upl [#59076](https://github.com/uppler/uppler/issues/59076) Sous-Sous-Catégorie pas prise en compte pour le listing des résultats  ([c68d81a](https://github.com/uppler/uppler/commit/c68d81a94192c89f1581b59e5993baa0c97a3463)), closes [#20318](https://github.com/uppler/uppler/issues/20318)

# [4.97.0](https://github.com/uppler/uppler/compare/v4.96.0...v4.97.0) (2025-04-17)


### Bug Fixes

* 🌷 bugs_upl [#59076](https://github.com/uppler/uppler/issues/59076) Sous-Sous-Catégorie pas prise en compte pour le listing des résultats  ([e64dac8](https://github.com/uppler/uppler/commit/e64dac8e3ada9a4d017da14233dcb5cd3a3616d3)), closes [#20313](https://github.com/uppler/uppler/issues/20313)


### Features

* ⭐ LAF1 - Import et gestion de contrats  ([0332358](https://github.com/uppler/uppler/commit/0332358e4c8709f6ee9b4110058f7e291401d081)), closes [#20309](https://github.com/uppler/uppler/issues/20309)

# [4.96.0](https://github.com/uppler/uppler/compare/v4.95.30...v4.96.0) (2025-04-16)


### Bug Fixes

* 🌷 bugs_upl [#60040](https://github.com/uppler/uppler/issues/60040) Erreur 500 sur appel API v1/buyer/variant  ([b144fbe](https://github.com/uppler/uppler/commit/b144fbec1533340a189f712829ac9e1684b77ecd)), closes [#20310](https://github.com/uppler/uppler/issues/20310)
* 💥 Tri des promotions en BO KO  ([aa23176](https://github.com/uppler/uppler/commit/aa23176*********************************)), closes [#20311](https://github.com/uppler/uppler/issues/20311)


### Features

* ⭐ Ajout modification numéro de panier via API  ([1776ddf](https://github.com/uppler/uppler/commit/1776ddfdcee35c83439860f78f10ff45fed5611d)), closes [#20216](https://github.com/uppler/uppler/issues/20216)

## [4.95.30](https://github.com/uppler/uppler/compare/v4.95.29...v4.95.30) (2025-04-15)


### Bug Fixes

* 🌷 bugs_upl [#59314](https://github.com/uppler/uppler/issues/59314) Type de wallet acheteur créé par Uppler sur Lemonway ne convient pas à Qantis  ([6027991](https://github.com/uppler/uppler/commit/602799153ed4ac44e15fac2dd407b81ae1bb89a6)), closes [#20306](https://github.com/uppler/uppler/issues/20306) [#20307](https://github.com/uppler/uppler/issues/20307) [#20307](https://github.com/uppler/uppler/issues/20307)
* 🌷 bugs_upl [#59699](https://github.com/uppler/uppler/issues/59699) Balise H4 avant les balises H1 / H2 - Impact SEO négatif.  ([6327d23](https://github.com/uppler/uppler/commit/6327d23c52cdf8e4fea468cd5290ca1db7b29ea7)), closes [#20304](https://github.com/uppler/uppler/issues/20304)
* 💥 Dépréciations BO Uppler  ([bc8f7c8](https://github.com/uppler/uppler/commit/bc8f7c86a6f6444aaa1c2b34a6b461a37e871eba)), closes [#20293](https://github.com/uppler/uppler/issues/20293) [#20293](https://github.com/uppler/uppler/issues/20293) [#20293](https://github.com/uppler/uppler/issues/20293) [#20293](https://github.com/uppler/uppler/issues/20293) [#20293](https://github.com/uppler/uppler/issues/20293) [#20293](https://github.com/uppler/uppler/issues/20293) [#20293](https://github.com/uppler/uppler/issues/20293) [#20293](https://github.com/uppler/uppler/issues/20293) [#20293](https://github.com/uppler/uppler/issues/20293)
* 💥 TODO - Logs Payment gateway  ([1c94f22](https://github.com/uppler/uppler/commit/1c94f225349afb00238cbadeec13aa3ec99f365c)), closes [#20305](https://github.com/uppler/uppler/issues/20305) [#20305](https://github.com/uppler/uppler/issues/20305) [#20305](https://github.com/uppler/uppler/issues/20305) [#20305](https://github.com/uppler/uppler/issues/20305) [#20305](https://github.com/uppler/uppler/issues/20305)

## [4.95.29](https://github.com/uppler/uppler/compare/v4.95.28...v4.95.29) (2025-04-07)


### Bug Fixes

* 🌷 tech_upl [#59537](https://github.com/uppler/uppler/issues/59537) Déplacer la pagination sur le shop d'un vendeur  ([07372fa](https://github.com/uppler/uppler/commit/07372fa178f0054b191b57e40a16a60a00f8765f)), closes [#20301](https://github.com/uppler/uppler/issues/20301)

## [4.95.28](https://github.com/uppler/uppler/compare/v4.95.27...v4.95.28) (2025-03-31)


### Bug Fixes

* :tulip: bugs_upl [#59394](https://github.com/uppler/uppler/issues/59394) Lemonway Refund KO ([bad1d5c](https://github.com/uppler/uppler/commit/bad1d5c752267b55ef1df41324f9cee741a3630b)), closes [#20294](https://github.com/uppler/uppler/issues/20294) [#20295](https://github.com/uppler/uppler/issues/20295) [#20295](https://github.com/uppler/uppler/issues/20295)

## [4.95.27](https://github.com/uppler/uppler/compare/v4.95.26...v4.95.27) (2025-03-31)


### Bug Fixes

* :star: bugs_upl [#56081](https://github.com/uppler/uppler/issues/56081) Suivi reconciliations Lemonway ([14566fb](https://github.com/uppler/uppler/commit/14566fb53a6f04c719ff795cbf43d11db7116002)), closes [#20236](https://github.com/uppler/uppler/issues/20236)
* 🌷 bugs_upl [#59123](https://github.com/uppler/uppler/issues/59123) Document supprimé reste "in queue"  ([2c75605](https://github.com/uppler/uppler/commit/2c75605dcb4652ad1defb62c9da887f29851ac7a)), closes [#20291](https://github.com/uppler/uppler/issues/20291) [#20291](https://github.com/uppler/uppler/issues/20291) [#20291](https://github.com/uppler/uppler/issues/20291)
* 🌷bugs_upl [#59094](https://github.com/uppler/uppler/issues/59094) KYC Lemonway refusé sans aucune explication  ([020a5d1](https://github.com/uppler/uppler/commit/020a5d1596217ad88c94750381d2cbeb0fc9a681)), closes [#20289](https://github.com/uppler/uppler/issues/20289) [#20289](https://github.com/uppler/uppler/issues/20289) [#20289](https://github.com/uppler/uppler/issues/20289)

## [4.95.26](https://github.com/uppler/uppler/compare/v4.95.25...v4.95.26) (2025-03-25)


### Bug Fixes

* 🌷 bugs_upl [#58822](https://github.com/uppler/uppler/issues/58822) Email automatique ne semble pas partir  ([86ceb66](https://github.com/uppler/uppler/commit/86ceb66426adb8a16a0ea778923379e549f1c4eb)), closes [#20282](https://github.com/uppler/uppler/issues/20282) [#20282](https://github.com/uppler/uppler/issues/20282) [#20282](https://github.com/uppler/uppler/issues/20282) [#20282](https://github.com/uppler/uppler/issues/20282) [#20282](https://github.com/uppler/uppler/issues/20282) [#20282](https://github.com/uppler/uppler/issues/20282) [#20282](https://github.com/uppler/uppler/issues/20282)
* 🌷 bugs_upl [#59064](https://github.com/uppler/uppler/issues/59064) Erreur 500 import de promotion  ([fee6ef6](https://github.com/uppler/uppler/commit/fee6ef66b4af4a4f3273ef8b4dfe9bce24f4a973)), closes [#20284](https://github.com/uppler/uppler/issues/20284) [#20284](https://github.com/uppler/uppler/issues/20284) [#20284](https://github.com/uppler/uppler/issues/20284)

## [4.95.25](https://github.com/uppler/uppler/compare/v4.95.24...v4.95.25) (2025-03-25)


### Bug Fixes

* 🌷 bugs_upl [#57545](https://github.com/uppler/uppler/issues/57545) Graylog - Entity product null  ([6b51c8d](https://github.com/uppler/uppler/commit/6b51c8dbac8cd167c876ec374dc091887b5797c4)), closes [#20283](https://github.com/uppler/uppler/issues/20283)
* 🌷 bugs_upl [#57548](https://github.com/uppler/uppler/issues/57548) Graylog - Call getToken() on null  ([2dfa8b8](https://github.com/uppler/uppler/commit/2dfa8b8a56ef62e5dff5897ed5fc307837acb9ef)), closes [#20279](https://github.com/uppler/uppler/issues/20279) [#20279](https://github.com/uppler/uppler/issues/20279) [#20279](https://github.com/uppler/uppler/issues/20279) [#20279](https://github.com/uppler/uppler/issues/20279)
* 🌷 bugs_upl [#57550](https://github.com/uppler/uppler/issues/57550) Graylog - Args null sur canManageAllOrders  ([93afd59](https://github.com/uppler/uppler/commit/93afd594497513e6c2ebcfa7534910bfda2d7aa1)), closes [#20254](https://github.com/uppler/uppler/issues/20254) [#20254](https://github.com/uppler/uppler/issues/20254)
* 🌷 bugs_upl [#58065](https://github.com/uppler/uppler/issues/58065) Suggestion de la recherche remonte des produits qui ont été supprimés  ([2b3090c](https://github.com/uppler/uppler/commit/2b3090ceded3746392d97291bf40aa0c1c5b9391)), closes [#20281](https://github.com/uppler/uppler/issues/20281)

## [4.95.24](https://github.com/uppler/uppler/compare/v4.95.23...v4.95.24) (2025-03-24)


### Bug Fixes

* 🌷 bugs_upl [#58606](https://github.com/uppler/uppler/issues/58606) Avis impossible si le compte master en a déjà laissé un ([9e485c6](https://github.com/uppler/uppler/commit/9e485c6f4b48c198aefd3ce694ca1c7fdfe766dd)), closes [#20277](https://github.com/uppler/uppler/issues/20277)

## [4.95.23](https://github.com/uppler/uppler/compare/v4.95.22...v4.95.23) (2025-03-19)


### Bug Fixes

* 🌷 bugs_upl [#58340](https://github.com/uppler/uppler/issues/58340) url pdf erroné export produit  ([b2b7c86](https://github.com/uppler/uppler/commit/b2b7c862f0489a1b28fbc75578a9de3b08e6317f)), closes [#20272](https://github.com/uppler/uppler/issues/20272) [#20278](https://github.com/uppler/uppler/issues/20278) [#20278](https://github.com/uppler/uppler/issues/20278)
* 🌷 bugs_upl [#58389](https://github.com/uppler/uppler/issues/58389) import valeur option EN  ([8ea1b59](https://github.com/uppler/uppler/commit/8ea1b598eb8fe6e16d7ef801d85be6eb7fe08792)), closes [#20265](https://github.com/uppler/uppler/issues/20265)
* 🌷 bugs_upl [#58417](https://github.com/uppler/uppler/issues/58417) Méthode de livraison forcé KO  ([8133c03](https://github.com/uppler/uppler/commit/8133c03bb6de99f3199c3167650b56a83385ef93)), closes [#20269](https://github.com/uppler/uppler/issues/20269)
* 🌷 bugs_upl [#58421](https://github.com/uppler/uppler/issues/58421) Double méthode de livraison appliqué sur un panier  ([b6af7db](https://github.com/uppler/uppler/commit/b6af7dbd1329ff3dc9b1d594074da72bdb964c76)), closes [#20266](https://github.com/uppler/uppler/issues/20266)
* 🌷 bugs_upl [#58509](https://github.com/uppler/uppler/issues/58509) Correction avertissement d'un import provoque une erreur 500  ([3177d08](https://github.com/uppler/uppler/commit/3177d08bd831bd0bf400d0082f6ab9d7c05e0b02)), closes [#20275](https://github.com/uppler/uppler/issues/20275)

## [4.95.22](https://github.com/uppler/uppler/compare/v4.95.21...v4.95.22) (2025-03-18)


### Bug Fixes

* 🌷 bugs_upl [#58065](https://github.com/uppler/uppler/issues/58065) Suggestion de la recherche remonte des produits qui ont été supprimés V3  ([9fb52db](https://github.com/uppler/uppler/commit/9fb52db7a20f75126b70f85b1eada7f8e706e59b)), closes [#20274](https://github.com/uppler/uppler/issues/20274)

## [4.95.21](https://github.com/uppler/uppler/compare/v4.95.20...v4.95.21) (2025-03-17)


### Bug Fixes

* 🌷 bugs_upl [#58036](https://github.com/uppler/uppler/issues/58036) Modele de fichier non présent à l'étape d'ajout de fichier sur un panier  ([0ff30b3](https://github.com/uppler/uppler/commit/0ff30b3bf0c428431f82691b662aa77e68e3b5f5)), closes [#20258](https://github.com/uppler/uppler/issues/20258)


### Reverts

* Revert "hotfix elastica crash" ([54ab512](https://github.com/uppler/uppler/commit/54ab51250b6fbfc062f23519249f025e21d6286a))

## [4.95.20](https://github.com/uppler/uppler/compare/v4.95.19...v4.95.20) (2025-03-12)


### Bug Fixes

* 🌷 bugs_upl [#57074](https://github.com/uppler/uppler/issues/57074) Erreur 500 lors d'une recherche  ([365b05d](https://github.com/uppler/uppler/commit/365b05d21470193ab9f2b656b80a09a444abf5eb)), closes [#20249](https://github.com/uppler/uppler/issues/20249)
* 🌷 bugs_upl [#58065](https://github.com/uppler/uppler/issues/58065) Suggestion de la recherche remonte des produits qui ont été supprimés  ([185d3f4](https://github.com/uppler/uppler/commit/185d3f425a51e1115ea6cd3728d3c21a2d75be90)), closes [#20267](https://github.com/uppler/uppler/issues/20267)

## [4.95.19](https://github.com/uppler/uppler/compare/v4.95.18...v4.95.19) (2025-03-10)


### Bug Fixes

* 🌷 bugs_upl [#58377](https://github.com/uppler/uppler/issues/58377) Paiement Lemonway Multivendeur Carte  ([80ad92d](https://github.com/uppler/uppler/commit/80ad92d9d619e5af38d62887273fab6f35f625e6)), closes [#20263](https://github.com/uppler/uppler/issues/20263)

## [4.95.18](https://github.com/uppler/uppler/compare/v4.95.17...v4.95.18) (2025-03-03)


### Bug Fixes

* 🌷 bugs_upl [#58065](https://github.com/uppler/uppler/issues/58065) Suggestion de la recherche remonte des produits qui ont été supprimés  ([0f1040e](https://github.com/uppler/uppler/commit/0f1040e14646662d4f10d2fc91856ce981793921)), closes [#20261](https://github.com/uppler/uppler/issues/20261) [#20261](https://github.com/uppler/uppler/issues/20261)
* 🌷 bugs_upl [#58098](https://github.com/uppler/uppler/issues/58098) Retention 'purchase_order_uncomplete' ne fonctionne pas  ([697f860](https://github.com/uppler/uppler/commit/697f8606e72979d04bf5fa087cb41eb656f0e7d6)), closes [#20259](https://github.com/uppler/uppler/issues/20259) [#20259](https://github.com/uppler/uppler/issues/20259)

## [4.95.17](https://github.com/uppler/uppler/compare/v4.95.16...v4.95.17) (2025-02-24)


### Bug Fixes

* 🌷 bugs_upl [#57993](https://github.com/uppler/uppler/issues/57993) Nouvelles valeurs dans import ne se créent que dans une langue  ([62ae269](https://github.com/uppler/uppler/commit/62ae26942f09946e01a750c3f3838213166c93f1)), closes [#20255](https://github.com/uppler/uppler/issues/20255)

## [4.95.16](https://github.com/uppler/uppler/compare/v4.95.15...v4.95.16) (2025-02-19)


### Bug Fixes

* 🌷 bugs_upl [#57549](https://github.com/uppler/uppler/issues/57549) Graylog - Query builder expr mal utilisé  ([2e99028](https://github.com/uppler/uppler/commit/2e99028d86ce775b7e2a1696ef50f701a52b2bd2)), closes [#20252](https://github.com/uppler/uppler/issues/20252) [#20252](https://github.com/uppler/uppler/issues/20252) [#20252](https://github.com/uppler/uppler/issues/20252)
* 🌷 edi_upl [#57836](https://github.com/uppler/uppler/issues/57836) Explication erreur connecteur devis Ozyme  ([bbfb986](https://github.com/uppler/uppler/commit/bbfb9868044fa161375c3bdf2911e0f869c4049a)), closes [#20251](https://github.com/uppler/uppler/issues/20251)

## [4.95.15](https://github.com/uppler/uppler/compare/v4.95.14...v4.95.15) (2025-02-18)


### Bug Fixes

* 🌷 bugs_upl [#56896](https://github.com/uppler/uppler/issues/56896) Import en erreur : l existe de nombreuses valeurs « 5 Kits » pour l'option Size  ([c2d8345](https://github.com/uppler/uppler/commit/c2d834506aaa8804fa20da8cd0e3d119a0678cdd)), closes [#20234](https://github.com/uppler/uppler/issues/20234)
* 🌷 bugs_upl [#57823](https://github.com/uppler/uppler/issues/57823) Filtre KO sur les pages catégories  ([4e172a2](https://github.com/uppler/uppler/commit/4e172a28d9556a66e3316360418c28462be8b77d)), closes [#20248](https://github.com/uppler/uppler/issues/20248)

## [4.95.14](https://github.com/uppler/uppler/compare/v4.95.13...v4.95.14) (2025-02-12)


### Bug Fixes

* 🌷 bugs_upl [#57697](https://github.com/uppler/uppler/issues/57697) Erreur 500 /cart  ([bc5b7b4](https://github.com/uppler/uppler/commit/bc5b7b495cbc58ad378dbe157beb785f1c92b652)), closes [#20246](https://github.com/uppler/uppler/issues/20246)


### Reverts

* Revert "Update cron for release deploy for a one shot release on friday (to release a technical PR)" ([2a4021c](https://github.com/uppler/uppler/commit/2a4021ceeb53feeeb6d4a6f1222bbc5a2c4865f8))

## [4.95.13](https://github.com/uppler/uppler/compare/v4.95.12...v4.95.13) (2025-02-07)


### Bug Fixes

* 🌷 bugs_upl [#57553](https://github.com/uppler/uppler/issues/57553) Ajouter configs Elasticsearch dans les params  ([a803036](https://github.com/uppler/uppler/commit/a80303685127466788329b503a87fa6ac78aef63)), closes [#20244](https://github.com/uppler/uppler/issues/20244)

## [4.95.12](https://github.com/uppler/uppler/compare/v4.95.11...v4.95.12) (2025-02-03)


### Bug Fixes

* 🌷 bugs_upl [#57417](https://github.com/uppler/uppler/issues/57417) Migration Doctrine MariaDB  ([01748b5](https://github.com/uppler/uppler/commit/01748b51ec0b506f568c1a61f69ec46e0b0dc561)), closes [#20242](https://github.com/uppler/uppler/issues/20242)

## [4.95.11](https://github.com/uppler/uppler/compare/v4.95.10...v4.95.11) (2025-02-03)


### Bug Fixes

* 🌷 bugs_upl [#57296](https://github.com/uppler/uppler/issues/57296) Méthode de livraison non active  ([6e76013](https://github.com/uppler/uppler/commit/6e7601332630fde65470a4357e18c578da8b92f2)), closes [#20240](https://github.com/uppler/uppler/issues/20240) [#20240](https://github.com/uppler/uppler/issues/20240)

## [4.95.10](https://github.com/uppler/uppler/compare/v4.95.9...v4.95.10) (2025-01-29)


### Bug Fixes

* 🌷 bugs_upl [#56999](https://github.com/uppler/uppler/issues/56999) Bouton "télécharger le modèle" ne fonctionne pas BO  ([d72383f](https://github.com/uppler/uppler/commit/d72383fd4849acf6c5388256f454e3ffa1fe7c59)), closes [#20237](https://github.com/uppler/uppler/issues/20237) [#20237](https://github.com/uppler/uppler/issues/20237)

## [4.95.9](https://github.com/uppler/uppler/compare/v4.95.8...v4.95.9) (2025-01-29)


### Bug Fixes

* 🌷 bugs_upl [#56873](https://github.com/uppler/uppler/issues/56873) Commentaire vide publié sur un produit.  ([13e892e](https://github.com/uppler/uppler/commit/13e892e81e915582b7ad9b5116a49a774dbdbec6)), closes [#20227](https://github.com/uppler/uppler/issues/20227) [#20227](https://github.com/uppler/uppler/issues/20227) [#20227](https://github.com/uppler/uppler/issues/20227) [#20231](https://github.com/uppler/uppler/issues/20231) [#20231](https://github.com/uppler/uppler/issues/20231) [#20231](https://github.com/uppler/uppler/issues/20231)
* 🌷 bugs_upl [#56916](https://github.com/uppler/uppler/issues/56916) Création de 4 paniers au clic "ajout au panier"  ([d5597ec](https://github.com/uppler/uppler/commit/d5597ec522c88a198992df9d07df5dd69a15ae91)), closes [#20235](https://github.com/uppler/uppler/issues/20235) [#20235](https://github.com/uppler/uppler/issues/20235)

## [4.95.8](https://github.com/uppler/uppler/compare/v4.95.7...v4.95.8) (2025-01-23)


### Bug Fixes

* 🌷 bugs_upl [#57074](https://github.com/uppler/uppler/issues/57074) Erreur 500 lors d'une recherche  ([0294670](https://github.com/uppler/uppler/commit/02946701112a859e71cb69dc53c7eb8367c469eb)), closes [#20228](https://github.com/uppler/uppler/issues/20228) [#20229](https://github.com/uppler/uppler/issues/20229) [#20229](https://github.com/uppler/uppler/issues/20229)

## [4.95.7](https://github.com/uppler/uppler/compare/v4.95.6...v4.95.7) (2025-01-22)


### Bug Fixes

* 🌷 bugs_upl [#56505](https://github.com/uppler/uppler/issues/56505) Colonne "preparation-delay" vide dans les exports produits  ([1720502](https://github.com/uppler/uppler/commit/1720502d57da229278effe0cc2317fad14f02e9c)), closes [#20213](https://github.com/uppler/uppler/issues/20213) [#20221](https://github.com/uppler/uppler/issues/20221) [#20221](https://github.com/uppler/uppler/issues/20221)
* 🌷 bugs_upl [#56657](https://github.com/uppler/uppler/issues/56657) Document KYC non téléchargeable  ([5da01f6](https://github.com/uppler/uppler/commit/5da01f6e5232f5080fb9dbd18f683fcf2f9d43f2)), closes [#20214](https://github.com/uppler/uppler/issues/20214)
* 🌷 bugs_upl [#56905](https://github.com/uppler/uppler/issues/56905) Montant panier à valider différent du devis associer (panier à 0€)  ([975ee9b](https://github.com/uppler/uppler/commit/975ee9b5f219a9ea6b533f4b758a69399e27147d)), closes [#20219](https://github.com/uppler/uppler/issues/20219)
* 💥 SUITE Afficher les informations des produits/vendeurs avec l'indexation V2  ([f9eb4e9](https://github.com/uppler/uppler/commit/f9eb4e95091042e96ad8294804ade89fdda87022)), closes [#20195](https://github.com/uppler/uppler/issues/20195) [#20195](https://github.com/uppler/uppler/issues/20195) [#20195](https://github.com/uppler/uppler/issues/20195) [#20195](https://github.com/uppler/uppler/issues/20195) [#20195](https://github.com/uppler/uppler/issues/20195) [#20195](https://github.com/uppler/uppler/issues/20195) [#20195](https://github.com/uppler/uppler/issues/20195) [#20195](https://github.com/uppler/uppler/issues/20195) [#20195](https://github.com/uppler/uppler/issues/20195) [#20195](https://github.com/uppler/uppler/issues/20195)

## [4.95.6](https://github.com/uppler/uppler/compare/v4.95.5...v4.95.6) (2025-01-21)


### Bug Fixes

* 🌷 bugs_upl [#55779](https://github.com/uppler/uppler/issues/55779) Filtre "expand" sur API Buyer  ([22fdb24](https://github.com/uppler/uppler/commit/22fdb243025113ab340858ed6e4c0f9390abeb1e)), closes [#20217](https://github.com/uppler/uppler/issues/20217) [#20220](https://github.com/uppler/uppler/issues/20220)
* 💥 [bugs_upl [#56461](https://github.com/uppler/uppler/issues/56461)] Prix erroné dans le DataLayer comparé au panier réel.  ([da65dfd](https://github.com/uppler/uppler/commit/da65dfd46d0511ad6e3eeef34ae2a4449279c38a)), closes [#20197](https://github.com/uppler/uppler/issues/20197)

## [4.95.5](https://github.com/uppler/uppler/compare/v4.95.4...v4.95.5) (2025-01-14)


### Bug Fixes

* 💥 [bugs_upl [#56583](https://github.com/uppler/uppler/issues/56583)] Commande de ré-indexation ne prend pas de paramètres  ([9bb51ec](https://github.com/uppler/uppler/commit/9bb51ec463149ee44fded0668bb7ee1e562ec34f)), closes [#20200](https://github.com/uppler/uppler/issues/20200) [#20200](https://github.com/uppler/uppler/issues/20200) [#20207](https://github.com/uppler/uppler/issues/20207)

## [4.95.4](https://github.com/uppler/uppler/compare/v4.95.3...v4.95.4) (2025-01-08)


### Bug Fixes

* 💥 [bugs_upl [#56139](https://github.com/uppler/uppler/issues/56139)] Produit dropshippé non visible sur le shop  ([e645aba](https://github.com/uppler/uppler/commit/e645abac7a13fdf4d1ec321c73cb8e937e6222bc)), closes [#20189](https://github.com/uppler/uppler/issues/20189) [#20189](https://github.com/uppler/uppler/issues/20189) [#20189](https://github.com/uppler/uppler/issues/20189)

## [4.95.3](https://github.com/uppler/uppler/compare/v4.95.2...v4.95.3) (2024-12-31)


### Bug Fixes

* 💥 Erreur 500 vendeur  ([3b6f588](https://github.com/uppler/uppler/commit/3b6f588c9f6b922aaba38d04222031d10cd7d2ed)), closes [#20192](https://github.com/uppler/uppler/issues/20192)

## [4.95.2](https://github.com/uppler/uppler/compare/v4.95.1...v4.95.2) (2024-12-23)


### Bug Fixes

* 💥 [bugs_upl [#55960](https://github.com/uppler/uppler/issues/55960)] Virement bancaire entrant non détecté  ([db9c3c7](https://github.com/uppler/uppler/commit/db9c3c7562b36e72008c9c3cfe894714f38caa68)), closes [#20180](https://github.com/uppler/uppler/issues/20180)
* 💥 [bugs_upl [#56019](https://github.com/uppler/uppler/issues/56019)] Affichage sur les pages catégories KO  ([bb47aa0](https://github.com/uppler/uppler/commit/bb47aa0f3cd8c98ec1cdb788120d9f9c7e40cae6)), closes [#20162](https://github.com/uppler/uppler/issues/20162) [#20172](https://github.com/uppler/uppler/issues/20172)

## [4.95.1](https://github.com/uppler/uppler/compare/v4.95.0...v4.95.1) (2024-12-23)


### Bug Fixes

* 💥 [bugs_upl [#55123](https://github.com/uppler/uppler/issues/55123)] Partage de liste de souhait  ([390f1c8](https://github.com/uppler/uppler/commit/390f1c8a2fee05b8d136d18b367dad992dfdf816)), closes [#20155](https://github.com/uppler/uppler/issues/20155) [#20161](https://github.com/uppler/uppler/issues/20161) [#20161](https://github.com/uppler/uppler/issues/20161)
* 💥 [bugs_upl [#56000](https://github.com/uppler/uppler/issues/56000)] Vendeur ayant réussi à créer un panier  ([a24a6f6](https://github.com/uppler/uppler/commit/a24a6f6b7abca983a2d5e8432a8e27b8459b9627)), closes [#20162](https://github.com/uppler/uppler/issues/20162) [#20172](https://github.com/uppler/uppler/issues/20172)

# [4.95.0](https://github.com/uppler/uppler/compare/v4.94.8...v4.95.0) (2024-12-23)


### Features

* ⭐ EVO UPPLER CI  ([48a32a7](https://github.com/uppler/uppler/commit/48a32a7812c99b81130ffc2636bfe092757f1467)), closes [#20186](https://github.com/uppler/uppler/issues/20186)

## [4.94.8](https://github.com/uppler/uppler/compare/v4.94.7...v4.94.8) (2024-12-18)


### Bug Fixes

* 💥 [bugs_upl [#55779](https://github.com/uppler/uppler/issues/55779)] Filtre "expand" sur API Buyer  ([f4dc90b](https://github.com/uppler/uppler/commit/f4dc90bfbf48a9841686a1521aa89a27b663bdf5)), closes [#20174](https://github.com/uppler/uppler/issues/20174)

## [4.94.7](https://github.com/uppler/uppler/compare/v4.94.6...v4.94.7) (2024-12-16)


### Bug Fixes

* 💥 Commission VIVA appliqué quand fond insuffisant  ([2f68e00](https://github.com/uppler/uppler/commit/2f68e0037d571e8dff6617a5aba721d88e53115d)), closes [#20132](https://github.com/uppler/uppler/issues/20132) [#20132](https://github.com/uppler/uppler/issues/20132)

## [4.94.6](https://github.com/uppler/uppler/compare/v4.94.5...v4.94.6) (2024-12-11)


### Bug Fixes

* 💥 Erreur 500 shop preprod  ([2168cb6](https://github.com/uppler/uppler/commit/2168cb6f888896a35ee0921f8c22abd9cb5c5cf2)), closes [#20169](https://github.com/uppler/uppler/issues/20169) [#20169](https://github.com/uppler/uppler/issues/20169)

## [4.94.5](https://github.com/uppler/uppler/compare/v4.94.4...v4.94.5) (2024-12-10)


### Bug Fixes

* 💥 [edi_upl [#53590](https://github.com/uppler/uppler/issues/53590)] Connecteur Sage, méthode de livraison supprimée  ([bf10fdd](https://github.com/uppler/uppler/commit/bf10fdd6e9b58c7c45443979cae5cce65d1009e9)), closes [#20133](https://github.com/uppler/uppler/issues/20133)

## [4.94.4](https://github.com/uppler/uppler/compare/v4.94.3...v4.94.4) (2024-12-10)


### Bug Fixes

* 💥 Page maintenance messenger à améliorer  ([20391b0](https://github.com/uppler/uppler/commit/20391b0acda2d88dd91576191d3a2c8cf880c2fe)), closes [#20130](https://github.com/uppler/uppler/issues/20130) [#20130](https://github.com/uppler/uppler/issues/20130) [#20130](https://github.com/uppler/uppler/issues/20130) [#20130](https://github.com/uppler/uppler/issues/20130) [#20130](https://github.com/uppler/uppler/issues/20130) [#20130](https://github.com/uppler/uppler/issues/20130)

## [4.94.3](https://github.com/uppler/uppler/compare/v4.94.2...v4.94.3) (2024-12-04)


### Bug Fixes

* 💥 Afficher les informations produits de la v2  ([9dd4d71](https://github.com/uppler/uppler/commit/9dd4d710dfe3f2fefc70a45962a1057c568f4c58)), closes [#20162](https://github.com/uppler/uppler/issues/20162) [#20162](https://github.com/uppler/uppler/issues/20162) [#20162](https://github.com/uppler/uppler/issues/20162) [#20162](https://github.com/uppler/uppler/issues/20162)

## [4.94.2](https://github.com/uppler/uppler/compare/v4.94.1...v4.94.2) (2024-12-03)


### Bug Fixes

* 💥 [edi_upl [#54442](https://github.com/uppler/uppler/issues/54442)] CSV vides sont envoyés  ([50d5528](https://github.com/uppler/uppler/commit/50d5528947a8712e5311e19d6f168ef927342a74)), closes [#20129](https://github.com/uppler/uppler/issues/20129)

## [4.94.1](https://github.com/uppler/uppler/compare/v4.94.0...v4.94.1) (2024-12-02)


### Bug Fixes

* 💥 [bugs_upl [#55063](https://github.com/uppler/uppler/issues/55063)] Revoir l'affichage/gestion filtre catégorie  ([df8e3e9](https://github.com/uppler/uppler/commit/df8e3e9cd0f5449419fdd4465d4ac40a461fa9e6)), closes [#20151](https://github.com/uppler/uppler/issues/20151)
* 💥 [bugs_upl [#55193](https://github.com/uppler/uppler/issues/55193)] Paiement supérieur au montant de la commande  ([908eb6d](https://github.com/uppler/uppler/commit/908eb6d5e2d529908a19fdacbada1b3ae1a3c198)), closes [#20156](https://github.com/uppler/uppler/issues/20156)

# [4.94.0](https://github.com/uppler/uppler/compare/v4.93.7...v4.94.0) (2024-12-02)


### Bug Fixes

* 💥 [bugs_upl [#54872](https://github.com/uppler/uppler/issues/54872)] Noms des valeurs en minuscule  ([a9aa9e5](https://github.com/uppler/uppler/commit/a9aa9e5d9366f415a5eaf0a0609364093c836444)), closes [#20145](https://github.com/uppler/uppler/issues/20145)
* 💥 [bugs_upl [#54917](https://github.com/uppler/uppler/issues/54917)] Commission ne se génère pas sur un paiement hors plateforme  ([2e595a6](https://github.com/uppler/uppler/commit/2e595a650509cedafdcd0aa66d23d722479dbd9c)), closes [#20146](https://github.com/uppler/uppler/issues/20146)
* 💥 [bugs_upl [#55087](https://github.com/uppler/uppler/issues/55087)] Pattern Category header n'est pris en compte sur la 1ere page de la catégories  ([4470aa1](https://github.com/uppler/uppler/commit/4470aa1a5797c0e31304df46455a6770a802e114)), closes [#20153](https://github.com/uppler/uppler/issues/20153)
* 💥 [inters_upl [#51975](https://github.com/uppler/uppler/issues/51975)] Passer les logs en level 'Notice'  ([4bc24e7](https://github.com/uppler/uppler/commit/4bc24e754ce23da5a0260864f33f75cd1310e4bf)), closes [#20157](https://github.com/uppler/uppler/issues/20157)


### Features

* ⭐ Recherche texte multiple  ([5f6898a](https://github.com/uppler/uppler/commit/5f6898ac8467dd20d2f7eb0564a8a1c840b6b98c)), closes [#20152](https://github.com/uppler/uppler/issues/20152)

## [4.93.7](https://github.com/uppler/uppler/compare/v4.93.6...v4.93.7) (2024-11-27)


### Bug Fixes

* 💥 [bugs_upl [#54663](https://github.com/uppler/uppler/issues/54663)] Problème de commande par virement  ([e58a6da](https://github.com/uppler/uppler/commit/e58a6da85dcf612ed17eff0cad950aef17c9b28c)), closes [#20139](https://github.com/uppler/uppler/issues/20139)

## [4.93.6](https://github.com/uppler/uppler/compare/v4.93.5...v4.93.6) (2024-11-26)


### Bug Fixes

* 💥 [bugs_upl [#55018](https://github.com/uppler/uppler/issues/55018)] BO - Listing sous commande ne reprends pas le numéro de commande d'origine  ([69d5154](https://github.com/uppler/uppler/commit/69d51544fd00b2a4ef0620bb682db2d6eb5d76a0)), closes [#20150](https://github.com/uppler/uppler/issues/20150)

## [4.93.5](https://github.com/uppler/uppler/compare/v4.93.4...v4.93.5) (2024-11-25)


### Bug Fixes

* 💥 Fonctionnement de "position" dans les attributs des articles  ([289155a](https://github.com/uppler/uppler/commit/289155acad96a887e786536a4c54d64d782a40f9)), closes [#20093](https://github.com/uppler/uppler/issues/20093) [#20093](https://github.com/uppler/uppler/issues/20093) [#20122](https://github.com/uppler/uppler/issues/20122) [#20122](https://github.com/uppler/uppler/issues/20122)

## [4.93.4](https://github.com/uppler/uppler/compare/v4.93.3...v4.93.4) (2024-11-19)


### Bug Fixes

* 💥 [bugs_upl [#54263](https://github.com/uppler/uppler/issues/54263)] Historique de modification de pattern n'est pas à jour.  ([359dc37](https://github.com/uppler/uppler/commit/359dc375a9f7767770cc468d24c07dc600590ae6)), closes [#20124](https://github.com/uppler/uppler/issues/20124) [#20124](https://github.com/uppler/uppler/issues/20124)
* 💥 [bugs_upl [#54810](https://github.com/uppler/uppler/issues/54810)] Recherche V2 désactivée en BO mais toujours active en front  ([6eb5727](https://github.com/uppler/uppler/commit/6eb5727eb5e7e48c4cc9eef98cfe85e5ccc14cb7)), closes [#20142](https://github.com/uppler/uppler/issues/20142)
* 💥 Email order-add-file ne s'envoie jamais  ([d2bce34](https://github.com/uppler/uppler/commit/d2bce344bde36aec49f5762d64d57328c897d2c1)), closes [#20136](https://github.com/uppler/uppler/issues/20136) [#20136](https://github.com/uppler/uppler/issues/20136)
* 💥 Erreur 500 sur ajout vide d'un membre d'une zone  ([c78b98c](https://github.com/uppler/uppler/commit/c78b98c100fe1385d73eb3a4f8fbd2ae0f2a5a4b)), closes [#20089](https://github.com/uppler/uppler/issues/20089) [#20089](https://github.com/uppler/uppler/issues/20089) [#20089](https://github.com/uppler/uppler/issues/20089) [#20089](https://github.com/uppler/uppler/issues/20089) [#20089](https://github.com/uppler/uppler/issues/20089) [#20089](https://github.com/uppler/uppler/issues/20089) [#20108](https://github.com/uppler/uppler/issues/20108) [#20108](https://github.com/uppler/uppler/issues/20108)
* 💥 Suppression d'un filtre ne fait rien  ([fea2023](https://github.com/uppler/uppler/commit/fea202301311af9af7dbc38566f8baf200d13ce7)), closes [#20097](https://github.com/uppler/uppler/issues/20097) [#10097](https://github.com/uppler/uppler/issues/10097) [#20117](https://github.com/uppler/uppler/issues/20117) [#20117](https://github.com/uppler/uppler/issues/20117) [#20117](https://github.com/uppler/uppler/issues/20117) [#20117](https://github.com/uppler/uppler/issues/20117) [#20117](https://github.com/uppler/uppler/issues/20117)

## [4.93.3](https://github.com/uppler/uppler/compare/v4.93.2...v4.93.3) (2024-11-18)


### Bug Fixes

* 💥 [bugs_upl [#54243](https://github.com/uppler/uppler/issues/54243)] Un acheteur par API peut télécharger toutes les factures de tous les acheteurs  ([5d9908e](https://github.com/uppler/uppler/commit/5d9908e7912740e38410891d4158f9a028e4787e)), closes [#20123](https://github.com/uppler/uppler/issues/20123)
* 💥 bugs_upl [#54666](https://github.com/uppler/uppler/issues/54666) Sous-compte acheteur ne peut pas annuler une demande de devis accepté par le vendeur  ([c38eb1c](https://github.com/uppler/uppler/commit/c38eb1c1a4192f1e7ed208a15d726e58d69a9316)), closes [#20138](https://github.com/uppler/uppler/issues/20138) [#20138](https://github.com/uppler/uppler/issues/20138)

## [4.93.2](https://github.com/uppler/uppler/compare/v4.93.1...v4.93.2) (2024-11-18)


### Bug Fixes

* 💥 [bugs_upl [#54093](https://github.com/uppler/uppler/issues/54093)] Image WEPB ne fonctionne plus  ([248aede](https://github.com/uppler/uppler/commit/248aede2af58bfbb5a56f5f60d6a36cfe82ad3c7)), closes [#20119](https://github.com/uppler/uppler/issues/20119)
* 💥 [bugs_upl [#54229](https://github.com/uppler/uppler/issues/54229)] Acheteur ne voit pas les produits d'un vendeur  ([731c56f](https://github.com/uppler/uppler/commit/731c56f1870415be03adc06f67105c6b91721067)), closes [#20120](https://github.com/uppler/uppler/issues/20120)
* 💥 bugs_upl [#53138](https://github.com/uppler/uppler/issues/53138) Erreur 500 ajout de produits wishlist->panier  ([b6a2d34](https://github.com/uppler/uppler/commit/b6a2d341dfa0f1f1c7c152fc393b8e28cf3c2c20)), closes [#20099](https://github.com/uppler/uppler/issues/20099)

## [4.93.1](https://github.com/uppler/uppler/compare/v4.93.0...v4.93.1) (2024-11-14)


### Bug Fixes

* 💥 [bugs_upl [#53346](https://github.com/uppler/uppler/issues/53346)] Double retour en arrière nécessaire sur certains navigateurs  ([905dcd1](https://github.com/uppler/uppler/commit/905dcd10a4371832ab5d8063703579e4b940b846)), closes [#20114](https://github.com/uppler/uppler/issues/20114)
* 💥 [edi_upl [#53725](https://github.com/uppler/uppler/issues/53725)] Problème connecteur  ([b3a2280](https://github.com/uppler/uppler/commit/b3a22806928eb06c098931b4ac42e30534cd45d1)), closes [#20103](https://github.com/uppler/uppler/issues/20103)

# [4.93.0](https://github.com/uppler/uppler/compare/v4.92.2...v4.93.0) (2024-11-13)


### Features

* ⭐ [MAD] Champs personnalisation obligatoire  ([e85d7c6](https://github.com/uppler/uppler/commit/e85d7c66a984191e4ecbd4504f992d55eefd8fe6)), closes [#20034](https://github.com/uppler/uppler/issues/20034) [#20107](https://github.com/uppler/uppler/issues/20107) [#20107](https://github.com/uppler/uppler/issues/20107)

## [4.92.2](https://github.com/uppler/uppler/compare/v4.92.1...v4.92.2) (2024-11-12)


### Bug Fixes

* 💥 [bugs_upl [#53940](https://github.com/uppler/uppler/issues/53940)] Créer la possibilité d'un export différé de catégories  ([1a896f9](https://github.com/uppler/uppler/commit/1a896f9687d751f87c47a2667dd77f243a139242)), closes [#20118](https://github.com/uppler/uppler/issues/20118)
* 💥 [bugs_upl [#54412](https://github.com/uppler/uppler/issues/54412)] Devis - Ajout du prix par le vendeur de livraison n'est pas pris en compte  ([9930f86](https://github.com/uppler/uppler/commit/9930f868f56638e24235a2a9c1939880f6476cd1)), closes [#20126](https://github.com/uppler/uppler/issues/20126)

## [4.92.1](https://github.com/uppler/uppler/compare/v4.92.0...v4.92.1) (2024-11-06)


### Bug Fixes

* 💥 [bugs_upl [#53677](https://github.com/uppler/uppler/issues/53677)] Catégorie renvoyée même si la recherche est décochée  ([3d33a90](https://github.com/uppler/uppler/commit/3d33a90c07dc565ea0a7f3062d7ba2999d872e20)), closes [#20100](https://github.com/uppler/uppler/issues/20100)

# [4.92.0](https://github.com/uppler/uppler/compare/v4.91.2...v4.92.0) (2024-11-04)


### Features

* ⭐ [MAD]  Infos obligatoire pour la livraison - connecteur commandes  ([962d2aa](https://github.com/uppler/uppler/commit/962d2aa21551d724587f7090aa66355cee7cf37e)), closes [#20033](https://github.com/uppler/uppler/issues/20033)

## [4.91.2](https://github.com/uppler/uppler/compare/v4.91.1...v4.91.2) (2024-10-30)


### Bug Fixes

* 💥 [bugs_upl [#53993](https://github.com/uppler/uppler/issues/53993)] Sous-compte acheteur n'a pas accès à la connexion à la maison fille vendeur  ([5fe012f](https://github.com/uppler/uppler/commit/5fe012f541ef967f37889df444bc37af78b120ff)), closes [#20113](https://github.com/uppler/uppler/issues/20113)
* 💥 bugs_upl [#53960](https://github.com/uppler/uppler/issues/53960) Erreur 404 sur notification - Une nouvelle facture est disponible  ([fc304fd](https://github.com/uppler/uppler/commit/fc304fd3399d9e20ae78077e4b0dea2921fa68b1)), closes [#20112](https://github.com/uppler/uppler/issues/20112)

## [4.91.1](https://github.com/uppler/uppler/compare/v4.91.0...v4.91.1) (2024-10-29)


### Bug Fixes

* 💥 [bugs_upl [#52342](https://github.com/uppler/uppler/issues/52342)] Acheteur débité du mauvais prix lors d'une commande  ([7b99af5](https://github.com/uppler/uppler/commit/7b99af58bb1d895f5cd06983a2ff14e513ff507f)), closes [#20065](https://github.com/uppler/uppler/issues/20065)
* 💥 [bugs_upl [#53652](https://github.com/uppler/uppler/issues/53652)] Export catalogue PDF KO quand la cace "Afficher les variantes" est cochée  ([f450824](https://github.com/uppler/uppler/commit/f450824cbb10b1597f1c1172ac83225bb2015447)), closes [#20110](https://github.com/uppler/uppler/issues/20110)
* 💥 bugs_upl [#51976](https://github.com/uppler/uppler/issues/51976) Taxe spécifique + TVA mal recalculé ([f7de443](https://github.com/uppler/uppler/commit/f7de44363cf84e1822721a97c96da30ccf5700cb)), closes [#19973](https://github.com/uppler/uppler/issues/19973) [#19973](https://github.com/uppler/uppler/issues/19973) [#19973](https://github.com/uppler/uppler/issues/19973)
* 💥 Montant TTC dans email de commande acheteur erroné  ([b1a99df](https://github.com/uppler/uppler/commit/b1a99dfb42ec931be97ecb09422bf3aeb41655f0)), closes [#20096](https://github.com/uppler/uppler/issues/20096) [#20096](https://github.com/uppler/uppler/issues/20096)

# [4.91.0](https://github.com/uppler/uppler/compare/v4.90.7...v4.91.0) (2024-10-29)


### Bug Fixes

* 💥 bugs_upl [#53095](https://github.com/uppler/uppler/issues/53095) Produits d'une catégorie invisibles  ([578071d](https://github.com/uppler/uppler/commit/578071d1ad78b2efb053f76d5c42c6b7674734f1)), closes [#20087](https://github.com/uppler/uppler/issues/20087)


### Features

* ⭐ [MAD10] Litiges et remboursements  ([3c267c7](https://github.com/uppler/uppler/commit/3c267c7e3078ed9ac4117687aad2b585b129e397)), closes [#19542](https://github.com/uppler/uppler/issues/19542) [#19484](https://github.com/uppler/uppler/issues/19484) [#19485](https://github.com/uppler/uppler/issues/19485) [#19637](https://github.com/uppler/uppler/issues/19637)

## [4.90.7](https://github.com/uppler/uppler/compare/v4.90.6...v4.90.7) (2024-10-24)


### Bug Fixes

* 💥 [bugs_upl [#52654](https://github.com/uppler/uppler/issues/52654)] [#20082](https://github.com/uppler/uppler/issues/20082) KO - Impossible de transformer un panier en devis  ([664ea2c](https://github.com/uppler/uppler/commit/664ea2c1f45255d3cbe69b2ba5761fb431111fd2)), closes [#20102](https://github.com/uppler/uppler/issues/20102)

## [4.90.6](https://github.com/uppler/uppler/compare/v4.90.5...v4.90.6) (2024-10-23)


### Bug Fixes

* 💥 [bugs_upl [#52614](https://github.com/uppler/uppler/issues/52614)] Erreurs de délai d'expiration des devis  ([501f8af](https://github.com/uppler/uppler/commit/501f8af0a28531439015c86ad596830770dc8677)), closes [#20077](https://github.com/uppler/uppler/issues/20077)
* 💥 [bugs_upl [#52899](https://github.com/uppler/uppler/issues/52899)] Widget qui ne s'affiche pas  ([7cdd197](https://github.com/uppler/uppler/commit/7cdd19784a7d78130b009099c35510a52d76f190)), closes [#20076](https://github.com/uppler/uppler/issues/20076)

## [4.90.5](https://github.com/uppler/uppler/compare/v4.90.4...v4.90.5) (2024-10-21)


### Bug Fixes

* 💥 bugs_upl [#53199](https://github.com/uppler/uppler/issues/53199) Erreur 500 sur /company-matcher/  ([842496e](https://github.com/uppler/uppler/commit/842496ead8793c70b2678c8c7c5c17581f727e6d)), closes [#20090](https://github.com/uppler/uppler/issues/20090)

## [4.90.4](https://github.com/uppler/uppler/compare/v4.90.3...v4.90.4) (2024-10-16)


### Bug Fixes

* 💥 [bugs_upl [#52825](https://github.com/uppler/uppler/issues/52825)] Filtre recherche V2 mode mobile KO après 1ere application d'un filtre.  ([780ae1c](https://github.com/uppler/uppler/commit/780ae1ce3caec3b0c9418a363b4632764fa86409)), closes [#20072](https://github.com/uppler/uppler/issues/20072)
* 💥 Import produit - Valeur de propriété de type choix non fonctionnel.  ([ddde5a1](https://github.com/uppler/uppler/commit/ddde5a15c4c176ca3c1c5f67690d608813a7af29)), closes [#19863](https://github.com/uppler/uppler/issues/19863) [#19863](https://github.com/uppler/uppler/issues/19863) [#19916](https://github.com/uppler/uppler/issues/19916) [#19916](https://github.com/uppler/uppler/issues/19916) [#19916](https://github.com/uppler/uppler/issues/19916) [#19916](https://github.com/uppler/uppler/issues/19916) [#19916](https://github.com/uppler/uppler/issues/19916) [#19916](https://github.com/uppler/uppler/issues/19916)

## [4.90.3](https://github.com/uppler/uppler/compare/v4.90.2...v4.90.3) (2024-10-14)


### Bug Fixes

* 💥 [bugs_upl [#52478](https://github.com/uppler/uppler/issues/52478)] Suppression catégorie non fonctionnelle  ([d34e8cc](https://github.com/uppler/uppler/commit/d34e8cc61d253e15c3e53e999efae5d864c93eed)), closes [#20066](https://github.com/uppler/uppler/issues/20066)
* 💥 [bugs_upl [#52654](https://github.com/uppler/uppler/issues/52654)] Perte de points de fidélité après transformation d'un panier en demande de devis  ([1781e06](https://github.com/uppler/uppler/commit/1781e0604d81771c92dfa4001dee97b76a624af9)), closes [#20082](https://github.com/uppler/uppler/issues/20082)

## [4.90.2](https://github.com/uppler/uppler/compare/v4.90.1...v4.90.2) (2024-10-09)


### Bug Fixes

* 💥 "Categories" dans la recherche ne fonctionne pas  ([164e831](https://github.com/uppler/uppler/commit/164e8313ccfa491903e0c08b176915438382cb5f)), closes [#20086](https://github.com/uppler/uppler/issues/20086) [#20086](https://github.com/uppler/uppler/issues/20086) [#20086](https://github.com/uppler/uppler/issues/20086)

## [4.90.1](https://github.com/uppler/uppler/compare/v4.90.0...v4.90.1) (2024-10-07)


### Bug Fixes

* 💥 Bug rétention cart_delay_remove  ([5cddfe7](https://github.com/uppler/uppler/commit/5cddfe7732a4aa8e8df96299b0766981bd7210bb)), closes [#20067](https://github.com/uppler/uppler/issues/20067)

# [4.90.0](https://github.com/uppler/uppler/compare/v4.89.6...v4.90.0) (2024-10-07)


### Bug Fixes

* 💥 Table ext_log_entries explose en DB  ([537c76b](https://github.com/uppler/uppler/commit/537c76b3676396d3338173cfb90aed10c8f34d8f)), closes [#20051](https://github.com/uppler/uppler/issues/20051)


### Features

* ⭐ [QAN24] API - Récupérer les entités dynamiques éligibles pour un acheteur  ([628553a](https://github.com/uppler/uppler/commit/628553a1a643fb60fd122ea4fad4b1a3f363a7ad)), closes [#18052](https://github.com/uppler/uppler/issues/18052) [#19284](https://github.com/uppler/uppler/issues/19284)

## [4.89.6](https://github.com/uppler/uppler/compare/v4.89.5...v4.89.6) (2024-10-03)


### Bug Fixes

* 💥 [bugs_upl [#51532](https://github.com/uppler/uppler/issues/51532)] Inscription redirige sur le formulaire d'inscription sans aucun message de succès  ([3399ebd](https://github.com/uppler/uppler/commit/3399ebd59a9c2b83e07b64edf199727a96329319)), closes [#20001](https://github.com/uppler/uppler/issues/20001)
* 💥 [bugs_upl [#52422](https://github.com/uppler/uppler/issues/52422)] Suppression de propriété de produit ne supprime pas ses liens avec produits  ([4eb9820](https://github.com/uppler/uppler/commit/4eb9820156001259401da08b64117cd6507a226c)), closes [#20060](https://github.com/uppler/uppler/issues/20060)
* 💥 [edi_upl [#52750](https://github.com/uppler/uppler/issues/52750)] Connecteur ne relance pas les commandes quand il y a une erreur  ([ed9be3a](https://github.com/uppler/uppler/commit/ed9be3a4f15e77c416e786def247fff854d514bb)), closes [#20081](https://github.com/uppler/uppler/issues/20081)

## [4.89.5](https://github.com/uppler/uppler/compare/v4.89.4...v4.89.5) (2024-09-30)


### Bug Fixes

* 💥 Company zone matcher pas recalculé lorsque zone member supprimé ([13c72f8](https://github.com/uppler/uppler/commit/13c72f8e1cefa4719a2414e19409bb4cceaa0519)), closes [#20042](https://github.com/uppler/uppler/issues/20042)
* 💥 Filtre de recherche en OU - Après sélection d'un choix, les autres choix disparaissent  ([f02f4ea](https://github.com/uppler/uppler/commit/f02f4eab6f3ff91dc3b46247ea715141f59b8859)), closes [#20052](https://github.com/uppler/uppler/issues/20052) [#20052](https://github.com/uppler/uppler/issues/20052)

## [4.89.4](https://github.com/uppler/uppler/compare/v4.89.3...v4.89.4) (2024-09-30)


### Bug Fixes

* 💥 Image de produit en erreur malgré le changement d'image  ([03eb162](https://github.com/uppler/uppler/commit/03eb1624bcc7edbe2ce6f61973530db8d6acfbc3)), closes [#20048](https://github.com/uppler/uppler/issues/20048) [#20048](https://github.com/uppler/uppler/issues/20048)
* 💥 Moteur de recherche - Résultat avec le plus haut score n'est pas en 1er  ([4465eb4](https://github.com/uppler/uppler/commit/4465eb42bf9f8a4c0e20ad17b157775d3f88f9ea)), closes [#20043](https://github.com/uppler/uppler/issues/20043) [#20043](https://github.com/uppler/uppler/issues/20043) [#20043](https://github.com/uppler/uppler/issues/20043)

## [4.89.3](https://github.com/uppler/uppler/compare/v4.89.2...v4.89.3) (2024-09-26)


### Bug Fixes

* 💥 Addon values non clonées lors de partage de wishlist avec item custom  ([a9401dd](https://github.com/uppler/uppler/commit/a9401dde8b25d234edade452ab5d94cc5ebcbbb3)), closes [#20068](https://github.com/uppler/uppler/issues/20068)

## [4.89.2](https://github.com/uppler/uppler/compare/v4.89.1...v4.89.2) (2024-09-25)


### Bug Fixes

* 💥 [edi_upl [#52452](https://github.com/uppler/uppler/issues/52452)] Commandes dropshippées partiellement annulées pas remontées dans Sage  ([a35f477](https://github.com/uppler/uppler/commit/a35f47747c0d8cecd3779a990a8b706cf698e7ef)), closes [#20057](https://github.com/uppler/uppler/issues/20057)

## [4.89.1](https://github.com/uppler/uppler/compare/v4.89.0...v4.89.1) (2024-09-24)


### Bug Fixes

* 💥 [bugs_upl [#52325](https://github.com/uppler/uppler/issues/52325)] Notifications ne sont pas marquées comme lues  ([056dbb1](https://github.com/uppler/uppler/commit/056dbb155046441414420c8e46bb77ecc0cafae8)), closes [#20055](https://github.com/uppler/uppler/issues/20055)

# [4.89.0](https://github.com/uppler/uppler/compare/v4.88.1...v4.89.0) (2024-09-11)


### Bug Fixes

* 💥 Produits invisibles en dropshipping malgré les bons réglages  ([4a42bb5](https://github.com/uppler/uppler/commit/4a42bb51fd93fa75a9744389396b0435671ccc7b)), closes [#20040](https://github.com/uppler/uppler/issues/20040)


### Features

* ⭐ recherche V2 : optimisation de la recherche par catégorie  ([4d3f667](https://github.com/uppler/uppler/commit/4d3f667b1b1cd51c2f7f6f852a8f1919220d2c5f)), closes [#19807](https://github.com/uppler/uppler/issues/19807) [#20023](https://github.com/uppler/uppler/issues/20023) [#20023](https://github.com/uppler/uppler/issues/20023)

## [4.88.1](https://github.com/uppler/uppler/compare/v4.88.0...v4.88.1) (2024-09-10)


### Bug Fixes

* 💥 [bugs_upl [#52545](https://github.com/uppler/uppler/issues/52545)] Pas de notification en anonymous -> erreur 500  ([0abcf74](https://github.com/uppler/uppler/commit/0abcf741e7d6672910b43251d442774f7517f8d9)), closes [#20061](https://github.com/uppler/uppler/issues/20061)

# [4.88.0](https://github.com/uppler/uppler/compare/v4.87.8...v4.88.0) (2024-09-09)


### Bug Fixes

* 💥 Images de certification (facture) invisibles  ([4c5d98f](https://github.com/uppler/uppler/commit/4c5d98f255ecf80eeb0945f926aa5a8fbd44a997)), closes [#20036](https://github.com/uppler/uppler/issues/20036) [#20036](https://github.com/uppler/uppler/issues/20036) [#20036](https://github.com/uppler/uppler/issues/20036)
* 💥 Pas de contrainte de taille sur mention facture  ([10a4e0b](https://github.com/uppler/uppler/commit/10a4e0bac1f310a83bd5ec331402aef60bc2793c)), closes [#20037](https://github.com/uppler/uppler/issues/20037)


### Features

* ⭐  [MAD] Partage de wishlist  ([2092447](https://github.com/uppler/uppler/commit/209244770fbc5a446c873259586e6d0d760b83a7)), closes [#20032](https://github.com/uppler/uppler/issues/20032)

## [4.87.8](https://github.com/uppler/uppler/compare/v4.87.7...v4.87.8) (2024-09-03)


### Bug Fixes

* 💥 Mail de rappel envoyé au vendeur alors qu'il a annulé la commande  ([acc1422](https://github.com/uppler/uppler/commit/acc1422d598f410356a3c0dafca90a1ba59f5c3c)), closes [#20009](https://github.com/uppler/uppler/issues/20009) [#20009](https://github.com/uppler/uppler/issues/20009) [#20009](https://github.com/uppler/uppler/issues/20009) [#20009](https://github.com/uppler/uppler/issues/20009)

## [4.87.7](https://github.com/uppler/uppler/compare/v4.87.6...v4.87.7) (2024-08-26)


### Bug Fixes

* 💥 CSV mal généré, champs décalés  ([9e8fbdc](https://github.com/uppler/uppler/commit/9e8fbdcb9e454f45eb2c2ce4bca9f9b33d915e8d)), closes [#20030](https://github.com/uppler/uppler/issues/20030)
* 💥 Import produit - propriété type texte non multiple ajoute des valeurs au lieu de les remplacer  ([13b50a1](https://github.com/uppler/uppler/commit/13b50a1fe102e458ff10e56dd190bd78233ecfb4)), closes [#20041](https://github.com/uppler/uppler/issues/20041) [#20041](https://github.com/uppler/uppler/issues/20041)
* 💥 Impossible d'ajouter une image en BO & MO sur les produits.  ([7b34abe](https://github.com/uppler/uppler/commit/7b34abe65f329008e0cc6df5da9174be008a4288)), closes [#19999](https://github.com/uppler/uppler/issues/19999)
* 💥 Pattern 'cart-payment' manque deux variables  ([8556015](https://github.com/uppler/uppler/commit/85560155b995637d2c706a7f6fd7b88460c1eaeb)), closes [#20035](https://github.com/uppler/uppler/issues/20035)

## [4.87.6](https://github.com/uppler/uppler/compare/v4.87.5...v4.87.6) (2024-08-20)


### Bug Fixes

* 💥 Prénom & Nom ne remontent pas sur Sage  ([b1956b4](https://github.com/uppler/uppler/commit/b1956b4534f122cce75d7b2c2aba87eb28b88ffc)), closes [#20029](https://github.com/uppler/uppler/issues/20029)
* 💥 Zone + TVA Mali n'existe pas  ([722fca3](https://github.com/uppler/uppler/commit/722fca357a2ff4160bba08a0ddd61b2d3c0d0772)), closes [#19890](https://github.com/uppler/uppler/issues/19890) [#20020](https://github.com/uppler/uppler/issues/20020)

## [4.87.5](https://github.com/uppler/uppler/compare/v4.87.4...v4.87.5) (2024-08-14)


### Bug Fixes

* 💥 Erreur 500 édition d'un vendeur  ([d85b527](https://github.com/uppler/uppler/commit/d85b5274104da9755af3fc36ea6b8d5334c47e03)), closes [#19834](https://github.com/uppler/uppler/issues/19834)
* 💥 Recette V2, ajout d'article quantité KO  ([48ec642](https://github.com/uppler/uppler/commit/48ec642d41e0c9bebb199eff641986ec87c4cf28)), closes [#19996](https://github.com/uppler/uppler/issues/19996)

## [4.87.4](https://github.com/uppler/uppler/compare/v4.87.3...v4.87.4) (2024-08-05)


### Bug Fixes

* 💥 Tel et mail ne remontent pas sur Sage  ([5151bfa](https://github.com/uppler/uppler/commit/5151bfacd5945d33e4c396bba8604e56e877a1f5)), closes [#20019](https://github.com/uppler/uppler/issues/20019)
* 💥 Zone + TVA Mali n'existe pas.  ([7aa6de0](https://github.com/uppler/uppler/commit/7aa6de00e3c63e995f156d75877e93bc0c015373)), closes [#19890](https://github.com/uppler/uppler/issues/19890)


### Reverts

* :boom: Zone + TVA Mali n'existe pas. ([9901dbe](https://github.com/uppler/uppler/commit/9901dbee6efab8ade0d81da76f0c5e1bbe9e4089))

## [4.87.3](https://github.com/uppler/uppler/compare/v4.87.2...v4.87.3) (2024-07-30)


### Bug Fixes

* 💥 [MAD11] Visibilité des colonnes "utilisateur" "entreprise" sur liste de souhaits  ([d001cae](https://github.com/uppler/uppler/commit/d001cae0d3f668b02bfbfbdfe43ffe103def3d98)), closes [#19976](https://github.com/uppler/uppler/issues/19976)

## [4.87.2](https://github.com/uppler/uppler/compare/v4.87.1...v4.87.2) (2024-07-24)


### Bug Fixes

* 💥 Erreur 500 suite à création d'un contrat  ([052f0f9](https://github.com/uppler/uppler/commit/052f0f985721537a4c532658508000373609f3ac)), closes [#19980](https://github.com/uppler/uppler/issues/19980)
* 💥 recherche v2 - affichage slider prix  ([917dd95](https://github.com/uppler/uppler/commit/917dd95d6a51782c12f91839114acb855c85ca9e)), closes [#19965](https://github.com/uppler/uppler/issues/19965)

## [4.87.1](https://github.com/uppler/uppler/compare/v4.87.0...v4.87.1) (2024-07-22)


### Bug Fixes

* 💥 Accessibilité - Reprise [#19641](https://github.com/uppler/uppler/issues/19641)  ([1868a8e](https://github.com/uppler/uppler/commit/1868a8e687190ba5e56e7f3d46a159ca75fa960f)), closes [#19989](https://github.com/uppler/uppler/issues/19989)
* 💥 Commande non envoyé sur FTP / Connecteur  ([660c4a3](https://github.com/uppler/uppler/commit/660c4a3426f9866dbdabdfd6c15e33996dd21895)), closes [#19985](https://github.com/uppler/uppler/issues/19985)
* 💥 Ligne frais de livraison duppliquée pour chaque item sur facture / bon de commande / devis pdf  ([8e13793](https://github.com/uppler/uppler/commit/8e137939b2224ce376e29abac792b81c6b089d1c)), closes [#19970](https://github.com/uppler/uppler/issues/19970)
* 💥 Newsletter : problème d'affichage   ([06196fe](https://github.com/uppler/uppler/commit/06196fe813392645a3b103c7bd2099b61327a4c8)), closes [#19992](https://github.com/uppler/uppler/issues/19992)

# [4.87.0](https://github.com/uppler/uppler/compare/v4.86.3...v4.87.0) (2024-07-18)


### Bug Fixes

* 💥 [NMP9] Création d'un compte acheteur/vendeur  ([126aa05](https://github.com/uppler/uppler/commit/126aa0536a77ff9fd79f3c64d206c407b2b9c63e)), closes [#19910](https://github.com/uppler/uppler/issues/19910)
* 💥 Ajout au panier dupliqué   ([6aae4cf](https://github.com/uppler/uppler/commit/6aae4cf88f85ad99f3f751aa4970036c34ae2b0d)), closes [#19922](https://github.com/uppler/uppler/issues/19922)
* 💥 Filtre OU entre les différentes propriétés - Recherche V2  ([85b313f](https://github.com/uppler/uppler/commit/85b313fcf1627eee84cbfd9203de6bd188977f3e)), closes [#20004](https://github.com/uppler/uppler/issues/20004)


### Features

* :star: Moteur de recherche - Compléter les tests d'acceptance  ([4b61c7d](https://github.com/uppler/uppler/commit/4b61c7d55abeadf9a511624d7ba3b9ee045f931f)), closes [#19052](https://github.com/uppler/uppler/issues/19052) [#19177](https://github.com/uppler/uppler/issues/19177)
* ⭐ MAD9 : Flux import acheteur PRIO[#7](https://github.com/uppler/uppler/issues/7)  ([51922fc](https://github.com/uppler/uppler/commit/51922fc046d5b2c9ba16ae11e4cdaf0c8fc9bfc8)), closes [#19755](https://github.com/uppler/uppler/issues/19755) [#19817](https://github.com/uppler/uppler/issues/19817)
* ⭐Messenger - Monitoring   ([3ad158c](https://github.com/uppler/uppler/commit/3ad158cc7059ce942c9e57415bf76df718582867)), closes [#19707](https://github.com/uppler/uppler/issues/19707) [#19868](https://github.com/uppler/uppler/issues/19868)

## [4.86.3](https://github.com/uppler/uppler/compare/v4.86.2...v4.86.3) (2024-07-15)


### Bug Fixes

* :boom: 500 elastic sur /service-available si recherche v2 activée ([a6372ef](https://github.com/uppler/uppler/commit/a6372ef24b20eb09336e7e07ead391807b223317))
* 💥 STRIPE - Livraison partielle sur commande dropshippé ne transfert pas   ([c377cea](https://github.com/uppler/uppler/commit/c377cea39d598ea9bad6a81ad809fcce8af4c2dc)), closes [#19886](https://github.com/uppler/uppler/issues/19886) [#19904](https://github.com/uppler/uppler/issues/19904)

## [4.86.2](https://github.com/uppler/uppler/compare/v4.86.1...v4.86.2) (2024-07-15)


### Bug Fixes

* 💥 Filtres ET entre les différentes propriétés  ([b33a2ae](https://github.com/uppler/uppler/commit/b33a2ae3702ed3c0bf8f01c2013aadf12664fcca)), closes [#19987](https://github.com/uppler/uppler/issues/19987)

## [4.86.1](https://github.com/uppler/uppler/compare/v4.86.0...v4.86.1) (2024-07-10)


### Bug Fixes

* 💥 Inscription newsletter, flash message ne s'affiche pas après envoi du formulaire, obligé de rafraîchir la page.  ([a7f6146](https://github.com/uppler/uppler/commit/a7f6146bab74c9db4064d24198fe2f41621710c8)), closes [#19933](https://github.com/uppler/uppler/issues/19933)

# [4.86.0](https://github.com/uppler/uppler/compare/v4.85.1...v4.86.0) (2024-07-08)


### Bug Fixes

* 💥 Annulation partielle de commande ne clôture pas celle-ci  ([21fcc71](https://github.com/uppler/uppler/commit/21fcc71cb08c1af52a721bae762a607b67363166)), closes [#19904](https://github.com/uppler/uppler/issues/19904)
* 💥 Erreur 500 API - Route POST - v1/user/access-token  ([ad2f1ab](https://github.com/uppler/uppler/commit/ad2f1ab13564a453df06f8cfcae14cd14d5a43a1)), closes [#19952](https://github.com/uppler/uppler/issues/19952)
* 💥 Erreur 500 lors de filtrage des commande en MO  ([31bb627](https://github.com/uppler/uppler/commit/31bb627f4c4fcaa28250cc91dabff1c94b50ce4e)), closes [#19953](https://github.com/uppler/uppler/issues/19953)


### Features

* ⭐ [MAD11-b] Envoyer l'information du user principal de la liste de souhait dans l'export commande SAGE  ([d1a1d53](https://github.com/uppler/uppler/commit/d1a1d53c3b5470c922068b2a4ab2403376764db1)), closes [#19972](https://github.com/uppler/uppler/issues/19972)

## [4.85.1](https://github.com/uppler/uppler/compare/v4.85.0...v4.85.1) (2024-07-04)


### Bug Fixes

* 💥 Affichage bon de commande MO + ajouter nom du produit pack   ([0fea9f9](https://github.com/uppler/uppler/commit/0fea9f97bf8b1819156686962a059725228782a6)), closes [#19880](https://github.com/uppler/uppler/issues/19880) [#19880](https://github.com/uppler/uppler/issues/19880)
* 💥 Lemonway - Taxe spécifique modifie le prix de la commande  ([612a52b](https://github.com/uppler/uppler/commit/612a52b56d5e0cfcd13d76a017bc623cd0bea1f2)), closes [#19866](https://github.com/uppler/uppler/issues/19866)
* 💥 MO - Erreur 502 lors du clic sur bouton "ajouter un contact"  ([ab8f8e9](https://github.com/uppler/uppler/commit/ab8f8e9fab33e164f21c071da15041e028335164)), closes [#19930](https://github.com/uppler/uppler/issues/19930) [#19930](https://github.com/uppler/uppler/issues/19930) [#19930](https://github.com/uppler/uppler/issues/19930) [#19930](https://github.com/uppler/uppler/issues/19930) [#19930](https://github.com/uppler/uppler/issues/19930)


### Reverts

* Lemonway - Taxe spécifique modifie le prix de la commande ([7a9600b](https://github.com/uppler/uppler/commit/7a9600bf254880f08dee42955773e110ea6425de))

# [4.85.0](https://github.com/uppler/uppler/compare/v4.84.0...v4.85.0) (2024-07-03)


### Bug Fixes

* 💥 Erreur enregistrement statistiques recherche V2  ([ca136b0](https://github.com/uppler/uppler/commit/ca136b093ab3d2d0cb581e487510598da33cf1dd)), closes [#19941](https://github.com/uppler/uppler/issues/19941)


### Features

* ⭐ [MAD11] Paiement d'une commande par la maison mère  ([dcf5eaf](https://github.com/uppler/uppler/commit/dcf5eafd38ead13a012a6a8551816255a30bd6ef)), closes [#19621](https://github.com/uppler/uppler/issues/19621) [#19931](https://github.com/uppler/uppler/issues/19931)
* ⭐ En BO ajouter un filtre date export page commande  ([445cf61](https://github.com/uppler/uppler/commit/445cf61932191bad8d9518c2f104c651b71f9a92)), closes [#19944](https://github.com/uppler/uppler/issues/19944)

# [4.84.0](https://github.com/uppler/uppler/compare/v4.83.3...v4.84.0) (2024-07-01)


### Bug Fixes

* 💥 Erreur 500 soumission des KYC avec WPS  ([af87138](https://github.com/uppler/uppler/commit/af871381d9b1400c1dadbe7b7a2ab17ed3f4a787)), closes [#19911](https://github.com/uppler/uppler/issues/19911)
* 💥 Erreur mise à jour des champs dynamiques d'un vendeur par API BO  ([d937f45](https://github.com/uppler/uppler/commit/d937f458ec72d9fa31104879120e8de1292cd989)), closes [#19912](https://github.com/uppler/uppler/issues/19912)
* 💥 Export vendeur "locked"  ([0f6f5d4](https://github.com/uppler/uppler/commit/0f6f5d46dd1b09d4a91fa7e8add83831101eee3d)), closes [#19940](https://github.com/uppler/uppler/issues/19940)


### Features

* ⭐ Optimisation du calcul des prix en recherche v2  ([dadac68](https://github.com/uppler/uppler/commit/dadac68e71a41d9ba2595ba080ee996542772da5)), closes [#19885](https://github.com/uppler/uppler/issues/19885)

## [4.83.3](https://github.com/uppler/uppler/compare/v4.83.2...v4.83.3) (2024-06-26)


### Bug Fixes

* 💥 Bug sur le calcul de la commission acheteur avec promotion  ([d5f5196](https://github.com/uppler/uppler/commit/d5f51969c868af42a2679bb69d5ec0d3dc2060b5)), closes [#19906](https://github.com/uppler/uppler/issues/19906) [#19906](https://github.com/uppler/uppler/issues/19906) [#19906](https://github.com/uppler/uppler/issues/19906)
* 💥 Champ SSO mal mappé.  ([d12960f](https://github.com/uppler/uppler/commit/d12960f1f5d719c79bcbb5bfe52a83a10b8a3a5f)), closes [#19760](https://github.com/uppler/uppler/issues/19760)

## [4.83.2](https://github.com/uppler/uppler/compare/v4.83.1...v4.83.2) (2024-06-25)


### Bug Fixes

* 💥 Anomalie Évolution OZ65 : "Titre message dropshippé reprend le numéro de la commande mère et non de la commande fournisseur)  ([5c2b130](https://github.com/uppler/uppler/commit/5c2b1308188839f84dd580e9384fc0119f240c38)), closes [#19920](https://github.com/uppler/uppler/issues/19920) [#19920](https://github.com/uppler/uppler/issues/19920)
* 💥 Lemonway - Payout non initié   ([e1853fd](https://github.com/uppler/uppler/commit/e1853fdcc4b4c2ed56e83ad2e705602560eb41da)), closes [#19840](https://github.com/uppler/uppler/issues/19840)

## [4.83.1](https://github.com/uppler/uppler/compare/v4.83.0...v4.83.1) (2024-06-24)


### Bug Fixes

* 💥 Recherche V2 - Filtre et design page événement modifié  ([6caaf06](https://github.com/uppler/uppler/commit/6caaf064cc30f67133b662b463c8bc1ab39a171a)), closes [#19685](https://github.com/uppler/uppler/issues/19685)

# [4.83.0](https://github.com/uppler/uppler/compare/v4.82.0...v4.83.0) (2024-06-20)


### Bug Fixes

* 💥 Admin non Uppler - Page "Configuration des filtres par entité" et "configuration des filtres par catégorie de produit" non éditable  ([6b68ef6](https://github.com/uppler/uppler/commit/6b68ef6308019785f4a31fecb8a97c35a11e1bb8)), closes [#19898](https://github.com/uppler/uppler/issues/19898) [#19898](https://github.com/uppler/uppler/issues/19898) [#19898](https://github.com/uppler/uppler/issues/19898)
* 💥 Erreur lors de la validation du panier : Redirigé un trop grand nombre de fois  ([9685314](https://github.com/uppler/uppler/commit/96853144f4d90c14820ea5f7cbfb21815d630f0c)), closes [#19659](https://github.com/uppler/uppler/issues/19659)
* 💥 Pas d'export de commande dans SFTP  ([57c3bc5](https://github.com/uppler/uppler/commit/57c3bc53e262dcf64106d1d8c00d1a487e787c6b)), closes [#19902](https://github.com/uppler/uppler/issues/19902)
* 💥 TTC estimé dans le panier erroné si MAJ quantité produit dans le panier  ([954e66d](https://github.com/uppler/uppler/commit/954e66d9cfd918d0af662f5654ae8117f41f8789)), closes [#19887](https://github.com/uppler/uppler/issues/19887) [#19887](https://github.com/uppler/uppler/issues/19887)


### Features

* ⭐ Ajout d'un marqueur sur les adjustement par API   ([26ffe06](https://github.com/uppler/uppler/commit/26ffe06723f5a238fec7f45c6442088538c9e015)), closes [#19892](https://github.com/uppler/uppler/issues/19892)

# [4.82.0](https://github.com/uppler/uppler/compare/v4.81.5...v4.82.0) (2024-06-19)


### Bug Fixes

* 💥 Vendeur avec abonnement au status "initialize" et au statut "non traité" reçoit les emails  ([af33443](https://github.com/uppler/uppler/commit/af33443dd125d86d860bd1c9817b4b4e1fbc863a)), closes [#19878](https://github.com/uppler/uppler/issues/19878) [#19878](https://github.com/uppler/uppler/issues/19878) [#19878](https://github.com/uppler/uppler/issues/19878) [#19878](https://github.com/uppler/uppler/issues/19878)


### Features

* ⭐ MAD9 : Flux de propriété PRIO[#5](https://github.com/uppler/uppler/issues/5)  ([50e2fcf](https://github.com/uppler/uppler/commit/50e2fcf0959b110c79511beeed7d8891166aa216)), closes [#19732](https://github.com/uppler/uppler/issues/19732)

## [4.81.5](https://github.com/uppler/uppler/compare/v4.81.4...v4.81.5) (2024-06-18)


### Bug Fixes

* 💥 Mauvais montant frais de port ajout variantes devis/commande créés par le vendeur en MO  ([e153b74](https://github.com/uppler/uppler/commit/e153b74db9529fd85cff3e067d1b1af3e9a83dda)), closes [#19764](https://github.com/uppler/uppler/issues/19764) [#19764](https://github.com/uppler/uppler/issues/19764) [#19764](https://github.com/uppler/uppler/issues/19764) [#19813](https://github.com/uppler/uppler/issues/19813) [#19813](https://github.com/uppler/uppler/issues/19813)

## [4.81.4](https://github.com/uppler/uppler/compare/v4.81.3...v4.81.4) (2024-06-17)


### Bug Fixes

* 💥 Admin non Uppler - Page "Configuration de la recherche" plus accessible, notamment pour la partie filtre & tri  ([81a6a7f](https://github.com/uppler/uppler/commit/81a6a7f80716ac9261074bcfa9410bdf0efd61c0)), closes [#19875](https://github.com/uppler/uppler/issues/19875) [#19875](https://github.com/uppler/uppler/issues/19875)
* 💥 Recherche par le nom d'un type d'entreprise en BO non possible  ([dcf6408](https://github.com/uppler/uppler/commit/dcf64083df906f9a2dc5ce1e8da0c1f9ae6dfff2)), closes [#19865](https://github.com/uppler/uppler/issues/19865) [#19865](https://github.com/uppler/uppler/issues/19865) [#19865](https://github.com/uppler/uppler/issues/19865)

## [4.81.3](https://github.com/uppler/uppler/compare/v4.81.2...v4.81.3) (2024-06-13)


### Bug Fixes

* 💥 Paiement autorisé sur Lemonway mais échoué sur Uppler.  ([ddd21cd](https://github.com/uppler/uppler/commit/ddd21cd2037b79ad4c4a393a3a97075ad8bd4ad4)), closes [#19720](https://github.com/uppler/uppler/issues/19720)
* cache utilisateur en recherche v2 ([781abde](https://github.com/uppler/uppler/commit/781abdea45889a7a720fc72b594190fd4ca0e0db))

## [4.81.2](https://github.com/uppler/uppler/compare/v4.81.1...v4.81.2) (2024-06-12)


### Bug Fixes

* 💥 Erreur remplissage widget Grille des derniers produits liés à une catégorie avec recherche V2  ([b7f68cf](https://github.com/uppler/uppler/commit/b7f68cf5c3e9bd4d863c03cfbc6e01da6f3f47bd)), closes [#19694](https://github.com/uppler/uppler/issues/19694) [#19694](https://github.com/uppler/uppler/issues/19694)
* 💥 FO - cart/approving/mines, panier de 0£GB validé mais page vide  ([7e2f277](https://github.com/uppler/uppler/commit/7e2f277caa3889d8432e85902b055a5ce903a225)), closes [#19836](https://github.com/uppler/uppler/issues/19836) [#19836](https://github.com/uppler/uppler/issues/19836) [#19836](https://github.com/uppler/uppler/issues/19836) [#19836](https://github.com/uppler/uppler/issues/19836) [#19836](https://github.com/uppler/uppler/issues/19836) [#19836](https://github.com/uppler/uppler/issues/19836)

## [4.81.1](https://github.com/uppler/uppler/compare/v4.81.0...v4.81.1) (2024-06-11)


### Bug Fixes

* 💥 Erreur API - patch buyer/XX - envoie du champ "username" avec la valeur existante - Erreur 400 Validation Failed  ([4979876](https://github.com/uppler/uppler/commit/4979876af4ce5df098190edd6bb06e61be41c7d7)), closes [#19852](https://github.com/uppler/uppler/issues/19852) [#19852](https://github.com/uppler/uppler/issues/19852) [#19526](https://github.com/uppler/uppler/issues/19526)

# [4.81.0](https://github.com/uppler/uppler/compare/v4.80.0...v4.81.0) (2024-06-10)


### Bug Fixes

* 💥 Absence du prix des personnalisations mail détaillé commande  ([e81faa4](https://github.com/uppler/uppler/commit/e81faa4ecfe9b73afb8e33a2a638e0b03dc3b33d)), closes [#19848](https://github.com/uppler/uppler/issues/19848)
* 💥 Bouton "Configuration de la recherche" non accessible pour les admins non Uppler.  ([cdeb29d](https://github.com/uppler/uppler/commit/cdeb29dc84a9c4cbb66d39d7a5f45c4118eba566)), closes [#19812](https://github.com/uppler/uppler/issues/19812) [#19812](https://github.com/uppler/uppler/issues/19812) [#19812](https://github.com/uppler/uppler/issues/19812) [#19812](https://github.com/uppler/uppler/issues/19812) [#19812](https://github.com/uppler/uppler/issues/19812)


### Features

* ⭐ Retours connecteur " Export commande" [#5](https://github.com/uppler/uppler/issues/5)  ([220cc8f](https://github.com/uppler/uppler/commit/220cc8fcb04e5edfee135f9cef64e028413d7268)), closes [#19850](https://github.com/uppler/uppler/issues/19850)

# [4.80.0](https://github.com/uppler/uppler/compare/v4.79.0...v4.80.0) (2024-06-10)


### Features

* ⭐ Synchronisation valeur d'option et valeur personnalisation FO  ([6abdc62](https://github.com/uppler/uppler/commit/6abdc626dbabdb222c4ecf85c260e74dcf0226f7)), closes [#19814](https://github.com/uppler/uppler/issues/19814)

# [4.79.0](https://github.com/uppler/uppler/compare/v4.78.1...v4.79.0) (2024-06-07)


### Bug Fixes

* 💥 Anomalie Évolution OZ65 : "Titre message dropshippé renvoie vers /order au lieu de /order/IDXXXX dans la messagerie"  ([db4f65c](https://github.com/uppler/uppler/commit/db4f65c2967374d9640a146ac998108641fdca7c)), closes [#19707](https://github.com/uppler/uppler/issues/19707)
* 💥 Configuraton widget - Valeur de propriété non prise en compte  ([b95e6ea](https://github.com/uppler/uppler/commit/b95e6ea653ee8eee9fbfb1543ed30124f21ec5ce)), closes [#19723](https://github.com/uppler/uppler/issues/19723) [#19723](https://github.com/uppler/uppler/issues/19723) [#19723](https://github.com/uppler/uppler/issues/19723) [#19723](https://github.com/uppler/uppler/issues/19723) [#19723](https://github.com/uppler/uppler/issues/19723) [#19723](https://github.com/uppler/uppler/issues/19723) [#19723](https://github.com/uppler/uppler/issues/19723)
* 💥 Gestion cas limite Viva  ([070e8e8](https://github.com/uppler/uppler/commit/070e8e874624b88a389df3e81af6ceda5608b69c)), closes [#19810](https://github.com/uppler/uppler/issues/19810)
* 💥 Pages BO vendeur / acheteur / commandes, clic ne fait aucune action ([7b80a9d](https://github.com/uppler/uppler/commit/7b80a9d8976748262c94b721081a3cb65c524487))
* erreur 500 API client ([d7c8d40](https://github.com/uppler/uppler/commit/d7c8d404b3a25f766d03ac45488a0ceb3ba5cb5c))


### Features

* ⭐ Retours connecteur " Export commande" + "Etat des commandes" [#4](https://github.com/uppler/uppler/issues/4)  ([ab3cc75](https://github.com/uppler/uppler/commit/ab3cc751bbd93975d58a0d28a649aa2ca4033d56))

## [4.78.1](https://github.com/uppler/uppler/compare/v4.78.0...v4.78.1) (2024-06-04)


### Bug Fixes

* :boom: Messages de 2021 réapparus dans la boite de réception  ([f0c6c89](https://github.com/uppler/uppler/commit/f0c6c89bb786bc8ee66dc77b8bb8924a4af91c43)), closes [#19341](https://github.com/uppler/uppler/issues/19341)
* 💥 Bouton personnalisation non disponible  ([09f4645](https://github.com/uppler/uppler/commit/09f4645c63b04f728ae80ca7fdcea16d41645070)), closes [#19821](https://github.com/uppler/uppler/issues/19821)

# [4.78.0](https://github.com/uppler/uppler/compare/v4.77.0...v4.78.0) (2024-06-03)


### Bug Fixes

* 💥  [VIVA] Référence marchant incorrect dans Dashboard VIVA  ([3fc5f6a](https://github.com/uppler/uppler/commit/3fc5f6af7ce6c76d214d33b9429506b08371043d)), closes [#19542](https://github.com/uppler/uppler/issues/19542) [#19484](https://github.com/uppler/uppler/issues/19484) [#19485](https://github.com/uppler/uppler/issues/19485) [#19637](https://github.com/uppler/uppler/issues/19637)
* 💥 Détail des prix personnalisation commande bo   ([df07ecb](https://github.com/uppler/uppler/commit/df07ecbf6896423270197458e62fcb0e47bcc9e2)), closes [#19820](https://github.com/uppler/uppler/issues/19820)
* 💥 Filtre catalogue pas ouvert à la recherche  ([c68c809](https://github.com/uppler/uppler/commit/c68c80917594cbdb09df2926ec2cf4085474e41d)), closes [#19790](https://github.com/uppler/uppler/issues/19790) [#19790](https://github.com/uppler/uppler/issues/19790) [#19790](https://github.com/uppler/uppler/issues/19790)
* 💥 Séparer les méthodes relatives aux locales dans app.js  ([63c66fd](https://github.com/uppler/uppler/commit/63c66fdd89f516cf838b008765ff1b239e104a5f)), closes [#19663](https://github.com/uppler/uppler/issues/19663)


### Features

* ⭐ [MAD1] Personnalisation produits - SUITE  ([de51f9e](https://github.com/uppler/uppler/commit/de51f9eab39de5153dfb1bdadac68a4fd7e626fc)), closes [#19678](https://github.com/uppler/uppler/issues/19678) [#19768](https://github.com/uppler/uppler/issues/19768)
* ⭐ MAD9 : Flux de articles composés PRIO[#6](https://github.com/uppler/uppler/issues/6)  ([ca2056c](https://github.com/uppler/uppler/commit/ca2056c6fc27a2dde96da6dc8bb504b653263c80)), closes [#19733](https://github.com/uppler/uppler/issues/19733)
* ⭐ Retours connecteur " Export commande" [#2](https://github.com/uppler/uppler/issues/2)  ([0e07a36](https://github.com/uppler/uppler/commit/0e07a36a0d4a70b941b1f152fde04ef733778f85)), closes [#19815](https://github.com/uppler/uppler/issues/19815) [#19822](https://github.com/uppler/uppler/issues/19822)

# [4.77.0](https://github.com/uppler/uppler/compare/v4.76.0...v4.77.0) (2024-05-30)


### Bug Fixes

* 💥 Import catalogue madewis - erreur création valeur d'option  ([1bd3279](https://github.com/uppler/uppler/commit/1bd327956776281304fde7f891fc6c605e75a198)), closes [#19757](https://github.com/uppler/uppler/issues/19757) [#19757](https://github.com/uppler/uppler/issues/19757) [#19757](https://github.com/uppler/uppler/issues/19757)
* 💥 Mail sur appel d'offre redirige vers 404  ([1a6d06e](https://github.com/uppler/uppler/commit/1a6d06eee8872c1ce0b11b78d794ad4afe3ad4ea)), closes [#19769](https://github.com/uppler/uppler/issues/19769)


### Features

* :star: [MAD10] Intégration du PSP Viva  ([413562c](https://github.com/uppler/uppler/commit/413562cf2071560a133254fc4b3c7dbffa5075d5)), closes [#19542](https://github.com/uppler/uppler/issues/19542) [#19484](https://github.com/uppler/uppler/issues/19484) [#19485](https://github.com/uppler/uppler/issues/19485) [#19637](https://github.com/uppler/uppler/issues/19637)
* ⭐ MAD9 : Flux de catalogue produit PRIO[#4](https://github.com/uppler/uppler/issues/4)  ([6a7f0dc](https://github.com/uppler/uppler/commit/6a7f0dc2d3a6d109a258ce6efd5bbc62a18ddaab)), closes [#19731](https://github.com/uppler/uppler/issues/19731)
* ⭐ Retour connecteur " Export commande"  ([cad6564](https://github.com/uppler/uppler/commit/cad6564301dfbfdc49dce8163607ee0bc16db7cf)), closes [#19789](https://github.com/uppler/uppler/issues/19789) [#19797](https://github.com/uppler/uppler/issues/19797)

# [4.76.0](https://github.com/uppler/uppler/compare/v4.75.0...v4.76.0) (2024-05-29)


### Bug Fixes

* :boom: Impossible d'avoir deux fois le même nom en français et anglais pour un pays  ([f4fb1a7](https://github.com/uppler/uppler/commit/f4fb1a7b212110b9cc1b38a80116e180593c82d5)), closes [#19551](https://github.com/uppler/uppler/issues/19551)
* 💥 Import FTP non fonctionnel.  ([4336dda](https://github.com/uppler/uppler/commit/4336dda433418d8b6de5ce4e9ead7ac003b39fce)), closes [#19670](https://github.com/uppler/uppler/issues/19670)
* 💥 MAD9 - Ajustement format export commande  ([13d1671](https://github.com/uppler/uppler/commit/13d167191aceda8a13033f8bed050ae59b5b8272)), closes [#19791](https://github.com/uppler/uppler/issues/19791)


### Features

* ⭐ MAD9 - Personnalisation par kit component  ([eb8e92e](https://github.com/uppler/uppler/commit/eb8e92e1b74787fff8df0d305c52d2f177173a42)), closes [#19787](https://github.com/uppler/uppler/issues/19787)

# [4.75.0](https://github.com/uppler/uppler/compare/v4.74.0...v4.75.0) (2024-05-27)


### Bug Fixes

* 💥 [MAD5] Affichage des prix TTC sur les variantes avec modèles personnalisés non géré  ([c8760ee](https://github.com/uppler/uppler/commit/c8760ee65263dd2fbda31323b910b895dfa45c7f)), closes [#19781](https://github.com/uppler/uppler/issues/19781)
* 💥 MAD9 - ajustement connecteur personnalisation export commande  ([1c12111](https://github.com/uppler/uppler/commit/****************************************)), closes [#19777](https://github.com/uppler/uppler/issues/19777)
* 💥 Shop - Filtre catégorie non "ouvert"  ([5476e03](https://github.com/uppler/uppler/commit/5476e03bd3d16e0a39309d001ff589aa268769ca)), closes [#19753](https://github.com/uppler/uppler/issues/19753) [#19753](https://github.com/uppler/uppler/issues/19753)
* 💥 Stripe - Livraison partielle - Pas de mouvement de transfert de wallet opérateur au wallet vendeur.  ([51a09f9](https://github.com/uppler/uppler/commit/51a09f9849b56276c7b6bf98675a090bd3eba3b8)), closes [#19597](https://github.com/uppler/uppler/issues/19597)


### Features

* ⭐ Suite des retours MAD1  ([f56167f](https://github.com/uppler/uppler/commit/f56167f855d8008e1a4a2623e04c616854d0c8d3)), closes [#19001](https://github.com/uppler/uppler/issues/19001) [#19480](https://github.com/uppler/uppler/issues/19480)

# [4.74.0](https://github.com/uppler/uppler/compare/v4.73.0...v4.74.0) (2024-05-21)


### Bug Fixes

* 💥 Devise du vendeur non pris en compte dans la création de devis  ([05164cd](https://github.com/uppler/uppler/commit/05164cd564e23b3db095564562942dff7e8014db)), closes [#19734](https://github.com/uppler/uppler/issues/19734) [#19734](https://github.com/uppler/uppler/issues/19734) [#19734](https://github.com/uppler/uppler/issues/19734) [#19734](https://github.com/uppler/uppler/issues/19734) [#19734](https://github.com/uppler/uppler/issues/19734) [#19734](https://github.com/uppler/uppler/issues/19734)
* 💥 erreur prix TTC utilisateur anonyme  ([aff28b0](https://github.com/uppler/uppler/commit/aff28b01963a10b9cdd7572cc00b7f3ba4dee8cf)), closes [#19771](https://github.com/uppler/uppler/issues/19771)
* 💥 Problème traduction Madewis  ([1f23d6f](https://github.com/uppler/uppler/commit/1f23d6fe5be689908bf9a2ffad4c50a7e714817b)), closes [#19700](https://github.com/uppler/uppler/issues/19700) [#19700](https://github.com/uppler/uppler/issues/19700)
* 💥 Zone de disponibilité et case à cocher  ([d70b717](https://github.com/uppler/uppler/commit/d70b717fcfd386b5a7d2d1db679c9305bfc6ee63)), closes [#19745](https://github.com/uppler/uppler/issues/19745) [#19745](https://github.com/uppler/uppler/issues/19745)


### Features

* ⭐ MAD9 : Flux de facture PRIO[#3](https://github.com/uppler/uppler/issues/3)  ([08dc9d2](https://github.com/uppler/uppler/commit/08dc9d2d225cf135d789613b7b02c15ab4d7a148)), closes [#19730](https://github.com/uppler/uppler/issues/19730)

# [4.73.0](https://github.com/uppler/uppler/compare/v4.72.0...v4.73.0) (2024-05-20)


### Bug Fixes

* 💥 Ajout de logs sur le connecteur d'export commande  ([ccea649](https://github.com/uppler/uppler/commit/ccea6498fe8eaab7d9825933bc467df16c2ea227)), closes [#19762](https://github.com/uppler/uppler/issues/19762)
* 💥 Export produit - Valeurs erronées dans la colonne unit-quantity  ([e228cd8](https://github.com/uppler/uppler/commit/e228cd831a764f7b57c57db19b77cd2e83ac287c)), closes [#19725](https://github.com/uppler/uppler/issues/19725) [#19725](https://github.com/uppler/uppler/issues/19725) [#19725](https://github.com/uppler/uppler/issues/19725)
* 💥 Messages non visibles par sous-comptes  ([d1f9d7c](https://github.com/uppler/uppler/commit/d1f9d7c37c5b403ebb60b0f7b60394709889c4cf)), closes [#19698](https://github.com/uppler/uppler/issues/19698) [#19698](https://github.com/uppler/uppler/issues/19698) [#19698](https://github.com/uppler/uppler/issues/19698)


### Features

* :star: [MAD5] Affichage prix TTC  ([c926aed](https://github.com/uppler/uppler/commit/c926aed0b827b3df978a2635e07bb93786344499)), closes [#19151](https://github.com/uppler/uppler/issues/19151) [#19715](https://github.com/uppler/uppler/issues/19715)
* ⭐ MAD9 : Flux de stock PRIO[#1](https://github.com/uppler/uppler/issues/1)  ([ee4474a](https://github.com/uppler/uppler/commit/ee4474a4a8f883c18e6afbb713a5fec12584a511)), closes [#19728](https://github.com/uppler/uppler/issues/19728) [#19729](https://github.com/uppler/uppler/issues/19729)
* ⭐ Retours MAD1  ([8741c97](https://github.com/uppler/uppler/commit/8741c976c82b1c30dd827110c3d5bfadf49331e4)), closes [#19001](https://github.com/uppler/uppler/issues/19001) [#19480](https://github.com/uppler/uppler/issues/19480)

# [4.72.0](https://github.com/uppler/uppler/compare/v4.71.8...v4.72.0) (2024-05-16)


### Bug Fixes

* :boom: Message d'erreur lors de la modification d'une quantité sur une liste de souhait  ([ed956e5](https://github.com/uppler/uppler/commit/ed956e5c8f1749382bf160732fd159a9cc850978)), closes [#19202](https://github.com/uppler/uppler/issues/19202) [#19202](https://github.com/uppler/uppler/issues/19202) [#19202](https://github.com/uppler/uppler/issues/19202) [#19202](https://github.com/uppler/uppler/issues/19202)
* 💥 Widget automatique ne prend pas en compte la configuration "Restriction de sélection des éléments"  ([fa73215](https://github.com/uppler/uppler/commit/fa732150fede92ac5924964eec4f8e4c0b276379)), closes [#19657](https://github.com/uppler/uppler/issues/19657) [#19657](https://github.com/uppler/uppler/issues/19657) [#19657](https://github.com/uppler/uppler/issues/19657)


### Features

* ⭐ [MAD1] Personnalisation de produits.  ([c48d86b](https://github.com/uppler/uppler/commit/c48d86b81469d1e5d25178596dfeb532f24461f2)), closes [#19001](https://github.com/uppler/uppler/issues/19001) [#19480](https://github.com/uppler/uppler/issues/19480)
* ⭐ MAD9 : Flux d'import d'état des commandes PRIO[#0](https://github.com/uppler/uppler/issues/0)  ([0664fe8](https://github.com/uppler/uppler/commit/0664fe8b1cdc094e2b22e92c626e4da36e568732)), closes [#19737](https://github.com/uppler/uppler/issues/19737) [#19712](https://github.com/uppler/uppler/issues/19712) [#19198](https://github.com/uppler/uppler/issues/19198) [#19585](https://github.com/uppler/uppler/issues/19585)

## [4.71.8](https://github.com/uppler/uppler/compare/v4.71.7...v4.71.8) (2024-05-15)


### Bug Fixes

* :boom: Curseur sur barre de défilement  ([aa50587](https://github.com/uppler/uppler/commit/aa50587c834e8d746f9a2385e7f749cd07b02c95)), closes [#19200](https://github.com/uppler/uppler/issues/19200)
* :boom: Sous-menus inaccessible sur profil vendeur en langue chinoise  ([42706bc](https://github.com/uppler/uppler/commit/42706bcb6978f65216d97d5fe7c4c855ece823b9)), closes [#19491](https://github.com/uppler/uppler/issues/19491)

## [4.71.7](https://github.com/uppler/uppler/compare/v4.71.6...v4.71.7) (2024-05-14)


### Bug Fixes

* :boom: Mauvais intitulé du mode de livraison sur devis  ([85ce340](https://github.com/uppler/uppler/commit/85ce3407f8f1956450f38b8ba5bf395ccfab7989)), closes [#19304](https://github.com/uppler/uppler/issues/19304) [#19304](https://github.com/uppler/uppler/issues/19304)
* 💥 Erreur de rercherche produits V2 connecté avec un vendeur ?  ([e1f29c8](https://github.com/uppler/uppler/commit/e1f29c8197093086036f03cfbbc2ecdb4e5e3de3)), closes [#19583](https://github.com/uppler/uppler/issues/19583)
* 💥 Erreur nom de variante quand lié à un produit dont le nom retourne NULL dans recherche produits en FO  ([b9d670f](https://github.com/uppler/uppler/commit/b9d670fd6db87c78ecd5c77f0d3751ed371a4ed8)), closes [#19693](https://github.com/uppler/uppler/issues/19693)
* 💥 Shop - Filtre catégorie en double.  ([4b5bffe](https://github.com/uppler/uppler/commit/4b5bffec0450371403a5165fde01529cf4a76b49)), closes [#19696](https://github.com/uppler/uppler/issues/19696) [#19696](https://github.com/uppler/uppler/issues/19696)

## [4.71.6](https://github.com/uppler/uppler/compare/v4.71.5...v4.71.6) (2024-05-13)


### Bug Fixes

* 💥 Valeur d'option vendeur à substituer pour une option opérateur - recherche non possible pour un caractère  ([4168294](https://github.com/uppler/uppler/commit/4168294dbbbfc1c690a26778fd2e156234a0d53e)), closes [#19642](https://github.com/uppler/uppler/issues/19642) [#19642](https://github.com/uppler/uppler/issues/19642)

## [4.71.5](https://github.com/uppler/uppler/compare/v4.71.4...v4.71.5) (2024-05-07)


### Bug Fixes

* 💥 Doublon de commande lors ajout 2 variantes ou plus à nouvelle liste de souhaits depuis fiche produit  ([bab6b41](https://github.com/uppler/uppler/commit/bab6b413afcd356c8279a345592383d8df53d60d)), closes [#19656](https://github.com/uppler/uppler/issues/19656) [#19656](https://github.com/uppler/uppler/issues/19656)
* 💥 Mauvaise devise reprise sur le devis  ([011b575](https://github.com/uppler/uppler/commit/011b57587ec33327b5a2d9930a597c2de4568037)), closes [#19640](https://github.com/uppler/uppler/issues/19640) [#19640](https://github.com/uppler/uppler/issues/19640) [#19640](https://github.com/uppler/uppler/issues/19640)

## [4.71.4](https://github.com/uppler/uppler/compare/v4.71.3...v4.71.4) (2024-05-06)


### Bug Fixes

* :boom: Liens incorrects dans PDF de liste de souhait exporté  ([6724d5a](https://github.com/uppler/uppler/commit/6724d5a8fc3b8d415f202e61a56df551154d5cd2)), closes [#19199](https://github.com/uppler/uppler/issues/19199)
* :boom: Valeurs de propriété d'un produit générique en BO ne sont pas sauvegardées  ([4f2e14b](https://github.com/uppler/uppler/commit/4f2e14b85844c40eb14ead6b52d62d0209d9c987)), closes [#19614](https://github.com/uppler/uppler/issues/19614) [#19614](https://github.com/uppler/uppler/issues/19614) [#19614](https://github.com/uppler/uppler/issues/19614) [#19614](https://github.com/uppler/uppler/issues/19614)
* 💥 Import produit MO - Matching des données - Propriété type date - Bouton "appliquer la proposition ..." KO   ([f5531be](https://github.com/uppler/uppler/commit/f5531bed2ee1dda37d083960fc88237ae2f52271)), closes [#19650](https://github.com/uppler/uppler/issues/19650) [#19650](https://github.com/uppler/uppler/issues/19650)
* 💥 PDF facture de commande incomplet  ([48fa8e7](https://github.com/uppler/uppler/commit/48fa8e7815f16b678a3cd815f1fac764ed4ef59d)), closes [#19588](https://github.com/uppler/uppler/issues/19588) [#19588](https://github.com/uppler/uppler/issues/19588)

## [4.71.3](https://github.com/uppler/uppler/compare/v4.71.2...v4.71.3) (2024-05-02)


### Bug Fixes

* 💥 Lemonway - Prélèvement bancaire non initié sur la preprod  ([44679d8](https://github.com/uppler/uppler/commit/44679d806e3f4348f7945d478336b9da276c7b30)), closes [#19615](https://github.com/uppler/uppler/issues/19615)

## [4.71.2](https://github.com/uppler/uppler/compare/v4.71.1...v4.71.2) (2024-04-29)


### Bug Fixes

* :boom: Le devis ne se transforme pas en commande une fois validé  ([b6806c4](https://github.com/uppler/uppler/commit/b6806c41c9f81432e1011ef8cd6f08487336a027)), closes [#19388](https://github.com/uppler/uppler/issues/19388)
* 💥 Erreur API lors de la récupération de données devant afficher un INTEGER  ([93804ad](https://github.com/uppler/uppler/commit/93804ad10065376889ca6c6897fe1ead345cb97a)), closes [#19579](https://github.com/uppler/uppler/issues/19579)

## [4.71.1](https://github.com/uppler/uppler/compare/v4.71.0...v4.71.1) (2024-04-24)


### Bug Fixes

* :boom: Erreur 500 quand j'essaie d'importer des produits génériques   ([01b0bfc](https://github.com/uppler/uppler/commit/01b0bfc0bcf111ec03d482fa104e065235a5fcf2)), closes [#19559](https://github.com/uppler/uppler/issues/19559)

# [4.71.0](https://github.com/uppler/uppler/compare/v4.70.0...v4.71.0) (2024-04-23)


### Features

* :star: Messenger - Address synchronization   ([a19d003](https://github.com/uppler/uppler/commit/a19d0035a7adfbeed88cd3d8306ff176547a2229)), closes [#19357](https://github.com/uppler/uppler/issues/19357)
* :star: Messenger - Catalog state  ([8a20850](https://github.com/uppler/uppler/commit/8a20850ed0fe0e294413750a0f414b1a6b37e0f5)), closes [#19358](https://github.com/uppler/uppler/issues/19358)
* :star: Messenger - Parent company update  ([d3851be](https://github.com/uppler/uppler/commit/d3851bee8dbaadeafb1db8be518536ea493b77a0)), closes [#19330](https://github.com/uppler/uppler/issues/19330)

# [4.70.0](https://github.com/uppler/uppler/compare/v4.69.2...v4.70.0) (2024-04-22)


### Bug Fixes

* :boom: Erreur lors de l'exécution du cron qui calcul les scores des entreprises  ([9370dd7](https://github.com/uppler/uppler/commit/9370dd7415cdcb581e0085b6f96bf13f9b5b4201)), closes [#19546](https://github.com/uppler/uppler/issues/19546)


### Features

* :star: Lemonway - Gérer une commission de 0%  ([a2cd98b](https://github.com/uppler/uppler/commit/a2cd98bfce3b71b7427f67bb820ea5a217dfe6d6)), closes [#18295](https://github.com/uppler/uppler/issues/18295)
* :star: Pré-requis MAD9  ([7b947c4](https://github.com/uppler/uppler/commit/7b947c403af092677fda5fd6ff287c9a33154260)), closes [#19562](https://github.com/uppler/uppler/issues/19562)

## [4.69.2](https://github.com/uppler/uppler/compare/v4.69.1...v4.69.2) (2024-04-18)


### Bug Fixes

* :boom: Import vendeur produit non lié au générique   ([cf83537](https://github.com/uppler/uppler/commit/cf83537a12c4bea73c302e0b5eff066c3d6f6d9a)), closes [#19561](https://github.com/uppler/uppler/issues/19561)

## [4.69.1](https://github.com/uppler/uppler/compare/v4.69.0...v4.69.1) (2024-04-17)


### Bug Fixes

* :boom: Création en doublon d'un paiement de commission opérateur avec le PSP Stripe  ([717841e](https://github.com/uppler/uppler/commit/717841eded39896955f0aff013c515e8c89a590f)), closes [#19557](https://github.com/uppler/uppler/issues/19557)

# [4.69.0](https://github.com/uppler/uppler/compare/v4.68.1...v4.69.0) (2024-04-16)


### Bug Fixes

* :boom: Prix à 0 pour une liste de prix   ([3628a77](https://github.com/uppler/uppler/commit/3628a776f5e4d7a5eba6c165f500f7f195380e3e)), closes [#19495](https://github.com/uppler/uppler/issues/19495)


### Features

* :star: Messenger - Dropshipped products update  ([a063f75](https://github.com/uppler/uppler/commit/a063f754c8b4b67de13bb1ddfb01b518782b8162)), closes [#19331](https://github.com/uppler/uppler/issues/19331)
* :star: Messenger - Enable company subscription  ([dfab05e](https://github.com/uppler/uppler/commit/dfab05e1f279c0bd4b7f58a85b49d5ef63ed25ce)), closes [#19359](https://github.com/uppler/uppler/issues/19359)

## [4.68.1](https://github.com/uppler/uppler/compare/v4.68.0...v4.68.1) (2024-04-15)


### Bug Fixes

* :boom:  PATCH seller sur les champs dynamiques avec deux valeurs à mettre à jour  ([326e424](https://github.com/uppler/uppler/commit/326e4241d7530a3bc6e7d66314f2b5855bf951e0)), closes [#19509](https://github.com/uppler/uppler/issues/19509)
* :boom: Méthode de livraison non compatible sur edit de devis  ([5a092f3](https://github.com/uppler/uppler/commit/5a092f3a315c07b6436b1da7a59be804042e6f98)), closes [#19518](https://github.com/uppler/uppler/issues/19518)
* :boom: Widget promotion non affiché  ([4b34ba4](https://github.com/uppler/uppler/commit/4b34ba4a22b35d8d976beda2e524eb365f803176)), closes [#19507](https://github.com/uppler/uppler/issues/19507)

# [4.68.0](https://github.com/uppler/uppler/compare/v4.67.0...v4.68.0) (2024-04-10)


### Bug Fixes

* :boom: Acheteur voit en résultat de recherche un produit auquel il n'a pas accès normalement  ([90adc1b](https://github.com/uppler/uppler/commit/90adc1b1d209822ab37d5541e4086436fddcb6cf)), closes [#19455](https://github.com/uppler/uppler/issues/19455)


### Features

* :star: Import des traductions des pays  ([396cd7b](https://github.com/uppler/uppler/commit/396cd7bfe77a2dfcc681ae747685695cc3414dd4)), closes [#19020](https://github.com/uppler/uppler/issues/19020)

# [4.67.0](https://github.com/uppler/uppler/compare/v4.66.0...v4.67.0) (2024-04-08)


### Bug Fixes

* :boom:  L'acheteur ne peut pas voir produits dropshippés  ([718dcad](https://github.com/uppler/uppler/commit/718dcadce0f4f64d3f4ef383d62eb63e3f1afbc2)), closes [#19274](https://github.com/uppler/uppler/issues/19274) [#19417](https://github.com/uppler/uppler/issues/19417)
* :boom: (Dernière prio) Problème d'envoie de commentaire commande  ([8de39e9](https://github.com/uppler/uppler/commit/8de39e9de0dd0c0e91c29693fc4c8abcabb3ea70)), closes [#19318](https://github.com/uppler/uppler/issues/19318) [#19318](https://github.com/uppler/uppler/issues/19318)
* :boom: Acheteur pas dans contrat mais appliqué quand même  ([2796e0b](https://github.com/uppler/uppler/commit/2796e0b805048ca271cc2ed66f8c9a16db90e86c)), closes [#19483](https://github.com/uppler/uppler/issues/19483)
* :boom: Doublon des produits lorsqu'on les ajoute au panier  ([0c4aa3c](https://github.com/uppler/uppler/commit/0c4aa3cf234268da6f0a3cbcac87c5b11009eb74)), closes [#19478](https://github.com/uppler/uppler/issues/19478)
* :boom: édition produit en BO supprime mise en forme de la description après import (patch rétroactif)  ([f22fc7c](https://github.com/uppler/uppler/commit/f22fc7c6e7799013909197b81a6506eadfea3f64)), closes [#18937](https://github.com/uppler/uppler/issues/18937)
* :boom: Moteur de recherche - 500 quand aggregation vide  ([3f0dd39](https://github.com/uppler/uppler/commit/3f0dd3970b3f4d668c27a1d46ac6edd5fc819aa8)), closes [#19517](https://github.com/uppler/uppler/issues/19517)
* :boom: Moteur de recherche - Amélioration des performances  ([dd65cd0](https://github.com/uppler/uppler/commit/dd65cd0f65ac122c852b1b02fe7bde82193e98e2)), closes [#19537](https://github.com/uppler/uppler/issues/19537)
* :boom: Prélèvement bancaire - Mandat de prélèvement sauvegarde - Document not found + paiement impossible.  ([be4470a](https://github.com/uppler/uppler/commit/be4470ace802f7ea34f75c37f8ffa75435a89b9c)), closes [#19271](https://github.com/uppler/uppler/issues/19271)


### Features

* :star: Messenger - Switch user company  ([9eb00a1](https://github.com/uppler/uppler/commit/9eb00a1d6259c0f84d7947a15214bdf38f221efe)), closes [#19328](https://github.com/uppler/uppler/issues/19328)
* ⭐ [MAD2] Inscription de sous-comptes acheteurs  ([dd52222](https://github.com/uppler/uppler/commit/dd522223ad7d9f175715bf445bd4fc380dd37d99)), closes [#18477](https://github.com/uppler/uppler/issues/18477) [#19159](https://github.com/uppler/uppler/issues/19159)

# [4.66.0](https://github.com/uppler/uppler/compare/v4.65.0...v4.66.0) (2024-04-04)


### Features

* :star: [NMP11]Affichage des zones de dispo  ([2754ea2](https://github.com/uppler/uppler/commit/2754ea2b9db2eaa5be6617b269ebab3271894fb3)), closes [#18305](https://github.com/uppler/uppler/issues/18305) [#19184](https://github.com/uppler/uppler/issues/19184)

# [4.65.0](https://github.com/uppler/uppler/compare/v4.64.1...v4.65.0) (2024-04-03)


### Bug Fixes

* :boom: Erreur d'accès quand on se connecte via BO  ([5c39aa6](https://github.com/uppler/uppler/commit/5c39aa68d922f0684ba5f9be141430dcddb1bf4a)), closes [#19213](https://github.com/uppler/uppler/issues/19213)
* :boom: Modifications apportées sur la homepage non visible en front  ([d0d2818](https://github.com/uppler/uppler/commit/d0d28186b1da552c472cd71eefaf534cf948815a)), closes [#19487](https://github.com/uppler/uppler/issues/19487)


### Features

* :star: [CMX106]Accès espace vendeur : abonnement initialize et compte certifié  ([26d8998](https://github.com/uppler/uppler/commit/26d899874713b7a5a290ac73e65c77bd4c56bc78)), closes [#18633](https://github.com/uppler/uppler/issues/18633) [#19261](https://github.com/uppler/uppler/issues/19261)
* :star: [OZ77] Back office et Middle Office lenteur de la plateforme  ([ac9d3f8](https://github.com/uppler/uppler/commit/ac9d3f8e2c4d7d09cc7eedeecd704c0207fea81b)), closes [#18860](https://github.com/uppler/uppler/issues/18860) [#19421](https://github.com/uppler/uppler/issues/19421)
* :star: Messenger - Option value substitution   ([e84ec97](https://github.com/uppler/uppler/commit/e84ec97a34d3d192a5bebe2697d732aa54caf1d1)), closes [#19319](https://github.com/uppler/uppler/issues/19319)
* :star: Moteur de recherche - Performances mises en avant de résultats  ([eb65f70](https://github.com/uppler/uppler/commit/eb65f704247fe4a7a15976b56514bdf3fde0a39e)), closes [#19047](https://github.com/uppler/uppler/issues/19047) [#19051](https://github.com/uppler/uppler/issues/19051) [#19492](https://github.com/uppler/uppler/issues/19492)
* :star: optimisation plateforme BO - Liste des vendeurs et acheteurs  ([bbc0e72](https://github.com/uppler/uppler/commit/bbc0e72ce2b237460d19c0fbfb3bb03d450fbb47)), closes [#19425](https://github.com/uppler/uppler/issues/19425)
* ⭐ [NMP9]Création d'un compte acheteur/vendeur suite à une inscription  ([d4fe902](https://github.com/uppler/uppler/commit/d4fe9023f3490a35e0a4f4ed648515488ba1b18d)), closes [#18477](https://github.com/uppler/uppler/issues/18477) [#19159](https://github.com/uppler/uppler/issues/19159)
* ⭐ [OZ73] Envoi de la commission acheteur à sage via le connecteur dropshipping.  ([156012f](https://github.com/uppler/uppler/commit/156012f136302f177b437ebb2f667925220fca7f)), closes [#18406](https://github.com/uppler/uppler/issues/18406) [#18814](https://github.com/uppler/uppler/issues/18814)

## [4.64.1](https://github.com/uppler/uppler/compare/v4.64.0...v4.64.1) (2024-03-27)


### Bug Fixes

* :boom: Disparition des valeurs de certains champs dynamiques sur les fiches vendeurs  ([96fcdf1](https://github.com/uppler/uppler/commit/96fcdf12f565a1c1498269b7b0bfe8b93e43a06e)), closes [#19380](https://github.com/uppler/uppler/issues/19380)
* :boom: Erreur 500 si on laisse un champ vide dans les paramètres de modes de livraison  ([74fb83b](https://github.com/uppler/uppler/commit/74fb83bd17ca7baadb2f601e8d20395e9fc90301)), closes [#19385](https://github.com/uppler/uppler/issues/19385) [#19385](https://github.com/uppler/uppler/issues/19385)
* :boom: Lenteur plateforme preprod inutilisable  ([902ab72](https://github.com/uppler/uppler/commit/902ab7239a94950eb10c5b6a24407f9fc67be69d)), closes [#19471](https://github.com/uppler/uppler/issues/19471)

# [4.64.0](https://github.com/uppler/uppler/compare/v4.63.2...v4.64.0) (2024-03-26)


### Features

* ⭐ [OZ62] Définir une "commission" dropshipping défini par le dropshippeur et imputé à l'acheteur  ([0b42ecd](https://github.com/uppler/uppler/commit/0b42ecdc4ebc05561a3e8a30acb9076cc0439c19)), closes [#18406](https://github.com/uppler/uppler/issues/18406) [#18814](https://github.com/uppler/uppler/issues/18814)

## [4.63.2](https://github.com/uppler/uppler/compare/v4.63.1...v4.63.2) (2024-03-21)


### Bug Fixes

* :boom:  Page acheteur ne fonctionne pas   ([ef4de9b](https://github.com/uppler/uppler/commit/ef4de9bdc947b29dadfb7968af5551b0d99d985e)), closes [#19280](https://github.com/uppler/uppler/issues/19280)

## [4.63.1](https://github.com/uppler/uppler/compare/v4.63.0...v4.63.1) (2024-03-20)


### Bug Fixes

* :boom:  Ajout de propriété par API   ([28139b7](https://github.com/uppler/uppler/commit/28139b72fdeceb10f4b261a0fe169c29d74694c8)), closes [#19414](https://github.com/uppler/uppler/issues/19414)
* :boom: 500 sur delete buyer/order-item  ([63751af](https://github.com/uppler/uppler/commit/63751aff6635df8aaebbe84cc0de5d17697b84e4)), closes [#19119](https://github.com/uppler/uppler/issues/19119)

# [4.63.0](https://github.com/uppler/uppler/compare/v4.62.0...v4.63.0) (2024-03-19)


### Features

* :star: Messenger - Suppression de propriété - réécriture du slug   ([b3d647d](https://github.com/uppler/uppler/commit/b3d647dd46f9ed02667391415070d9f73eadfedf)), closes [#19327](https://github.com/uppler/uppler/issues/19327)
* :star: Moteur de recherche - Ajouter un libellé traductible aux tris   ([516c8b9](https://github.com/uppler/uppler/commit/516c8b97a6bc2fe1752654c0c91cd355b99f0565)), closes [#19047](https://github.com/uppler/uppler/issues/19047)

# [4.62.0](https://github.com/uppler/uppler/compare/v4.61.0...v4.62.0) (2024-03-14)


### Bug Fixes

* :boom: Erreur 500 lors de la modification d'un vendeur  ([a34ab15](https://github.com/uppler/uppler/commit/a34ab15e5bafad372eef5d2e961a3c796bbba9f7)), closes [#19110](https://github.com/uppler/uppler/issues/19110)


### Features

* :star: Messenger - Intégrer l'exécutions des messages dans les tests d'acceptance  ([da8a5f7](https://github.com/uppler/uppler/commit/da8a5f737503c68c8a4f639208e921ff97e6ae5e)), closes [#19407](https://github.com/uppler/uppler/issues/19407)

# [4.61.0](https://github.com/uppler/uppler/compare/v4.60.1...v4.61.0) (2024-03-13)


### Features

* :star: [PHA5] UX affichage de la promo dégréssivité  ([4895a50](https://github.com/uppler/uppler/commit/4895a5085f6a4e4efadb4222971ea438dd82c985)), closes [#18496](https://github.com/uppler/uppler/issues/18496) [#19226](https://github.com/uppler/uppler/issues/19226)
* :star: Moteur de recherche - Groupes de filtres  ([65482b1](https://github.com/uppler/uppler/commit/65482b1d56ab7b7fa48ed362e5fe14a3984092d3)), closes [#19048](https://github.com/uppler/uppler/issues/19048) [#19117](https://github.com/uppler/uppler/issues/19117)

## [4.60.1](https://github.com/uppler/uppler/compare/v4.60.0...v4.60.1) (2024-03-11)


### Bug Fixes

* :boom: Erreur 500 création de commande récurrente  ([1d7d20d](https://github.com/uppler/uppler/commit/1d7d20d18fba0170d0987357cd895ec2860d5b7f)), closes [#19170](https://github.com/uppler/uppler/issues/19170) [#19329](https://github.com/uppler/uppler/issues/19329)
* :boom: Impossible d'ajouter un article au devis  ([7deee24](https://github.com/uppler/uppler/commit/7deee244cac87a26b14c4a4af58ccc324aea1262)), closes [#19375](https://github.com/uppler/uppler/issues/19375)
* :boom: L'import contrat ne tient pas compte du statut   ([4f4e55e](https://github.com/uppler/uppler/commit/4f4e55e78b4e900872f683992340cb120d4a4c57)), closes [#19295](https://github.com/uppler/uppler/issues/19295)

# [4.60.0](https://github.com/uppler/uppler/compare/v4.59.3...v4.60.0) (2024-02-29)


### Bug Fixes

* :boom: Création de zones dans une zone non possible  ([d888222](https://github.com/uppler/uppler/commit/d8882229c6d76a1c6a97b3def69f85d125055b12)), closes [#19222](https://github.com/uppler/uppler/issues/19222)
* :boom: Différence prix récap panier / listing produit  ([9f20ae7](https://github.com/uppler/uppler/commit/9f20ae71a2b70f86485d9c15647d481f765c59d6)), closes [#19166](https://github.com/uppler/uppler/issues/19166)


### Features

* :star: [OZ65] Message entre acheteur et dropshippé.  ([af02972](https://github.com/uppler/uppler/commit/af02972a37f56c62d911a33620ce9ed58c3d3938)), closes [#18096](https://github.com/uppler/uppler/issues/18096) [#19010](https://github.com/uppler/uppler/issues/19010)

## [4.59.3](https://github.com/uppler/uppler/compare/v4.59.2...v4.59.3) (2024-02-29)


### Bug Fixes

* :boom: La fonction recherche ne fonctionne pas ni en recherche V1, ni en recherche V2  ([#18951](https://github.com/uppler/uppler/issues/18951)) ([e18703a](https://github.com/uppler/uppler/commit/e18703a327e10297390bd1948d041f8ba0e33211))
* :boom: Moteur de recherche V2 - Outils de test analyseur KO pour admin non uppler  ([293f82c](https://github.com/uppler/uppler/commit/293f82cd4615b0592041ab0e76d7377dd137a32b)), closes [#19212](https://github.com/uppler/uppler/issues/19212) [#19279](https://github.com/uppler/uppler/issues/19279)

## [4.59.2](https://github.com/uppler/uppler/compare/v4.59.1...v4.59.2) (2024-02-26)


### Bug Fixes

* :boom: Erreur 500 - description de plus de 255 caractères pour un item d'appel d'offre.  ([32b3efc](https://github.com/uppler/uppler/commit/32b3efc4d0fdbd638a3ad999754f9fed99017a5f)), closes [#19276](https://github.com/uppler/uppler/issues/19276)

## [4.59.1](https://github.com/uppler/uppler/compare/v4.59.0...v4.59.1) (2024-02-22)


### Bug Fixes

* :boom: Date de fin de contrat absente du nouveau contrat  ([a8aa601](https://github.com/uppler/uppler/commit/a8aa6010c0ddbc5b028f970e9ae6b4c4776bc7c2)), closes [#19254](https://github.com/uppler/uppler/issues/19254)
* :boom: Impossible d'accéder à la page des appels d'offres  ([e7e0394](https://github.com/uppler/uppler/commit/e7e0394a856136fb84244e9d2d28383a516dd2ce)), closes [#19259](https://github.com/uppler/uppler/issues/19259)
* :boom: Impossible d'ajouter d'articles au panier pour un acheteur  ([396d5c2](https://github.com/uppler/uppler/commit/396d5c279e2d56dce121fe4446cbb7a7e47dfc07)), closes [#19249](https://github.com/uppler/uppler/issues/19249)
* :boom: Stripe kYC echoué   ([a2fbe61](https://github.com/uppler/uppler/commit/a2fbe61a4014a4179ae115b558711d96da7a6d93)), closes [#19240](https://github.com/uppler/uppler/issues/19240)

# [4.59.0](https://github.com/uppler/uppler/compare/v4.58.1...v4.59.0) (2024-02-21)


### Bug Fixes

* :boom: Modification des valeur de l'attribut quantitystep  ([48acd0e](https://github.com/uppler/uppler/commit/48acd0e7f2a69df2dbdcd1265ff4e34009b99866)), closes [#19210](https://github.com/uppler/uppler/issues/19210)
* :boom: Moteur de recherche V2 - Produits dropshippés ne remontent pas.  ([33ba835](https://github.com/uppler/uppler/commit/33ba8355d0a2af96d6447415a3faa841eb9492fe)), closes [#19211](https://github.com/uppler/uppler/issues/19211)
* :boom: Recherche V2 - Erreur 500 - "Japonais"  ([0901902](https://github.com/uppler/uppler/commit/0901902e037a170d2c7dc39bece55c243b555ff2)), closes [#19196](https://github.com/uppler/uppler/issues/19196)


### Features

* :star: [CMX105] Evolution Sitemap.xml  ([7b273ef](https://github.com/uppler/uppler/commit/7b273efafabc985a5125dfbae52d9a3fa765ecb5)), closes [#18495](https://github.com/uppler/uppler/issues/18495) [#18930](https://github.com/uppler/uppler/issues/18930)

## [4.58.1](https://github.com/uppler/uppler/compare/v4.58.0...v4.58.1) (2024-02-20)


### Bug Fixes

* :boom: Les articles du récapitulatif panier ne s'affichent pas.  ([c7da7d2](https://github.com/uppler/uppler/commit/c7da7d2ffe1fb499f666a36a64d41103b5b01a39)), closes [#19163](https://github.com/uppler/uppler/issues/19163)

# [4.58.0](https://github.com/uppler/uppler/compare/v4.57.4...v4.58.0) (2024-02-19)


### Bug Fixes

* :boom: Erreur 500 consultation historique de commande avec mauvais format de date  ([9e5565c](https://github.com/uppler/uppler/commit/9e5565cf0b66939786f1628ae2074efa0a8fd305)), closes [#19180](https://github.com/uppler/uppler/issues/19180)


### Features

* :star: Stripe V2   ([104cc4f](https://github.com/uppler/uppler/commit/104cc4f1abc41557655273c31ac46aa73400fc6a)), closes [#18974](https://github.com/uppler/uppler/issues/18974)

## [4.57.4](https://github.com/uppler/uppler/compare/v4.57.3...v4.57.4) (2024-02-15)


### Bug Fixes

* :boom: Erreur 500 sur la fiche d'un devis en MO  ([0cc21af](https://github.com/uppler/uppler/commit/0cc21af0491d9dd64ad8bd23ae2cd14932c144c3)), closes [#19182](https://github.com/uppler/uppler/issues/19182)

## [4.57.3](https://github.com/uppler/uppler/compare/v4.57.2...v4.57.3) (2024-02-14)


### Bug Fixes

* :boom: Erreur 500 visualisation de commande   ([2142401](https://github.com/uppler/uppler/commit/2142401e886bbd059681422db713abe303025a3c)), closes [#19105](https://github.com/uppler/uppler/issues/19105)

## [4.57.2](https://github.com/uppler/uppler/compare/v4.57.1...v4.57.2) (2024-02-13)


### Bug Fixes

* :boom: Import : champ limité à 255 caractères  ([9cbdb3f](https://github.com/uppler/uppler/commit/9cbdb3f91aa3c4ef5c6253dbd03db2bfbab7f410)), closes [#19154](https://github.com/uppler/uppler/issues/19154)
* :boom: Label du filtre érroné  ([2ba76e3](https://github.com/uppler/uppler/commit/2ba76e33aa22bfad9525374aad2075d8584736e9)), closes [#19142](https://github.com/uppler/uppler/issues/19142)
* :boom: Page vide après l'envoi d'une invitation  ([5c78f56](https://github.com/uppler/uppler/commit/5c78f564c2b0fcd5b3d6b9e67850d617db28d8e1)), closes [#19131](https://github.com/uppler/uppler/issues/19131)
* :boom: Panier : variable visible au lieu de l'affichage de sa valeur  ([4810cf5](https://github.com/uppler/uppler/commit/4810cf5a24b788de7bdcc5eb7230c8d1c76aa6f4)), closes [#19157](https://github.com/uppler/uppler/issues/19157)

## [4.57.1](https://github.com/uppler/uppler/compare/v4.57.0...v4.57.1) (2024-02-08)


### Bug Fixes

* :boom: Connecteur produits ne traite pas les produits réindexé après le 05/01.  ([24ff74f](https://github.com/uppler/uppler/commit/24ff74f57213d7962b28f4e4c5e55aa9c39a3860)), closes [#19122](https://github.com/uppler/uppler/issues/19122)
* :boom: Invitaiton acheteur date de réception du mail incorect   ([d0d4eca](https://github.com/uppler/uppler/commit/d0d4eca09b08722da81a61c4029cec8793ab570b)), closes [#19076](https://github.com/uppler/uppler/issues/19076)

# [4.57.0](https://github.com/uppler/uppler/compare/v4.56.3...v4.57.0) (2024-02-08)


### Bug Fixes

* :boom: [OZ38] > Admin non uppler ne voit pas les modèles d'export vendeur qu'il a créé  ([3231b0a](https://github.com/uppler/uppler/commit/3231b0a1754b8341b525ebabae27800fddf7f63a)), closes [#19017](https://github.com/uppler/uppler/issues/19017)
* :boom: Moteur recherche V2 > Configuration Analyseur texte KO (ne se sauvegarde pas)  ([3bfffb7](https://github.com/uppler/uppler/commit/3bfffb742c5b9d47e3fed797be9146ed43e24e49)), closes [#19128](https://github.com/uppler/uppler/issues/19128)


### Features

* :star: Moteur de recherche - Adaptation de l'api pour le connecteur produit  ([91d6550](https://github.com/uppler/uppler/commit/91d65502269e3810d6ca0f4a37262675cd63c4f7)), closes [#19050](https://github.com/uppler/uppler/issues/19050)

## [4.56.3](https://github.com/uppler/uppler/compare/v4.56.2...v4.56.3) (2024-02-05)


### Bug Fixes

* :boom: Affichage prix de vente conseillé dans le panier  ([ff3dad4](https://github.com/uppler/uppler/commit/ff3dad4f4bc33c1fa31625065f5dcbb5ce68b401)), closes [#19041](https://github.com/uppler/uppler/issues/19041)
* :boom: BO > KYC > "Certains champs n'ont pas été correctement remplis" : aucun champ souligné en rouge pour indiquer l'erreur  ([f758db6](https://github.com/uppler/uppler/commit/f758db689cfb9fef4030e0400111e9e5007cc216)), closes [#19085](https://github.com/uppler/uppler/issues/19085)
* :boom: Commande supprimé dans Sage   ([2d36203](https://github.com/uppler/uppler/commit/2d3620376ba94db95bc49d22585d43c343e1ff14)), closes [#18991](https://github.com/uppler/uppler/issues/18991)
* :boom: Erreur 500 lors de la confirmation pour envoi d'une commande  ([0052d36](https://github.com/uppler/uppler/commit/0052d36de5c03e12c497542584e4de8b11907986)), closes [#18959](https://github.com/uppler/uppler/issues/18959)
* :boom: Moteur de recherche - erreur 500 activation softdelete à l'indexation  ([63265d1](https://github.com/uppler/uppler/commit/63265d1abaa752e9e22239ea41810a5a617c4166)), closes [#19096](https://github.com/uppler/uppler/issues/19096)

## [4.56.2](https://github.com/uppler/uppler/compare/v4.56.1...v4.56.2) (2024-02-01)


### Bug Fixes

* :boom: Date de réindexation du dernier produit traité par le connecteur produit  ([5469108](https://github.com/uppler/uppler/commit/5469108cf360637cff9b4b9a02dc45405aae941a)), closes [#19074](https://github.com/uppler/uppler/issues/19074)

## [4.56.1](https://github.com/uppler/uppler/compare/v4.56.0...v4.56.1) (2024-01-30)


### Bug Fixes

* :boom: Moteur de recherche - les 2 indexes produits sont réindexés  ([d941a3f](https://github.com/uppler/uppler/commit/d941a3f974d2de96e291313e4258e96867d0282a)), closes [#19086](https://github.com/uppler/uppler/issues/19086)

# [4.56.0](https://github.com/uppler/uppler/compare/v4.55.0...v4.56.0) (2024-01-25)


### Bug Fixes

* :boom: Erreur 500 lors de l'ajout au panier   ([9e602fe](https://github.com/uppler/uppler/commit/9e602fea7bb08b2d19cb0cc7097ea95b6e1b01c7)), closes [#19064](https://github.com/uppler/uppler/issues/19064)
* :boom: Produit exclus par contrat accessible  ([f29af2d](https://github.com/uppler/uppler/commit/f29af2d6e3aab59aa6dd641a27f7d25068b8c3e6)), closes [#19032](https://github.com/uppler/uppler/issues/19032)


### Features

* :star: Recherche v2  ([88eb013](https://github.com/uppler/uppler/commit/88eb013836b6c363321b51820443db96ea638b89)), closes [#18413](https://github.com/uppler/uppler/issues/18413) [#17463](https://github.com/uppler/uppler/issues/17463) [#17463](https://github.com/uppler/uppler/issues/17463) [#18000](https://github.com/uppler/uppler/issues/18000)

# [4.55.0](https://github.com/uppler/uppler/compare/v4.54.3...v4.55.0) (2024-01-24)


### Bug Fixes

* :boom: Ajustement connecteur produits  ([c4e18f0](https://github.com/uppler/uppler/commit/c4e18f08d86a0084cb2d75f23302dfc261949f4a)), closes [#19022](https://github.com/uppler/uppler/issues/19022)
* :boom: Erreur 500 sur tri listing contrat  ([7cbbcf0](https://github.com/uppler/uppler/commit/7cbbcf094df921a6c13113fa26951b7660abcbac)), closes [#19033](https://github.com/uppler/uppler/issues/19033)
* :boom: Label catégorie affichée "taxon" dans les filtres  ([1c1d6d9](https://github.com/uppler/uppler/commit/1c1d6d9e6eab393b97e30b1554d8fee44b94d226)), closes [#18925](https://github.com/uppler/uppler/issues/18925)


### Features

* ⭐ [UPL1] Email sous-compte acheteur  ([fa53f0d](https://github.com/uppler/uppler/commit/fa53f0df6794adb1c4cb18b55df2664a69afa794)), closes [#16967](https://github.com/uppler/uppler/issues/16967)

## [4.54.3](https://github.com/uppler/uppler/compare/v4.54.2...v4.54.3) (2024-01-22)


### Bug Fixes

* :boom: Facture pas dispo dans la commande   ([31cdf08](https://github.com/uppler/uppler/commit/31cdf0851604d97aa0cd8220991c0c3449eb4cef)), closes [#19007](https://github.com/uppler/uppler/issues/19007)

## [4.54.2](https://github.com/uppler/uppler/compare/v4.54.1...v4.54.2) (2024-01-19)


### Bug Fixes

* :boom: Disparition de la méthode de livraison sur un devis acheteur lorsqu'on édite le devis côté vendeur  ([3e5957e](https://github.com/uppler/uppler/commit/3e5957efff641be6d571cf22ae06bec2b032c212)), closes [#18975](https://github.com/uppler/uppler/issues/18975)

## [4.54.1](https://github.com/uppler/uppler/compare/v4.54.0...v4.54.1) (2024-01-17)


### Bug Fixes

* :boom: API - PATCH /v1/administrator/product/id en erreur sur le paramètres "status"  ([a40f90d](https://github.com/uppler/uppler/commit/a40f90d8c530204775ccc393bf4e02d165be5ed6)), closes [#18986](https://github.com/uppler/uppler/issues/18986)
* :boom: Impossible de mettre un prix de plus de 2147483647 sur un produit  ([0318b4f](https://github.com/uppler/uppler/commit/0318b4fab56f21afc811dee1c8734ed5c33d9f83)), closes [#18992](https://github.com/uppler/uppler/issues/18992)
* :boom: Prix non à jour dans Sage suite à la bascule de prix.  ([d332620](https://github.com/uppler/uppler/commit/d3326201c6b748e74f35f1f5b52f49b35252c373)), closes [#18979](https://github.com/uppler/uppler/issues/18979)

# [4.54.0](https://github.com/uppler/uppler/compare/v4.53.1...v4.54.0) (2024-01-15)


### Bug Fixes

* :boom: Page not found quand on supprime une page CMS  ([b86aee0](https://github.com/uppler/uppler/commit/b86aee02ba1f9847b4d39fff506495a16c140e02)), closes [#18973](https://github.com/uppler/uppler/issues/18973)


### Features

* :star: [[#621](https://github.com/uppler/uppler/issues/621)] Filtre API sub-account  ([d428578](https://github.com/uppler/uppler/commit/d428578e0b03476ad8e15bb8ff49750629c5f1cc)), closes [#17231](https://github.com/uppler/uppler/issues/17231)
* ⭐ [OZ72] Ajout fonctionnalités sur le contrat spécifique  ([01eaaba](https://github.com/uppler/uppler/commit/01eaaba52098544ff18d3fb0f1bb0f5e028220d2)), closes [#18401](https://github.com/uppler/uppler/issues/18401)

## [4.53.1](https://github.com/uppler/uppler/compare/v4.53.0...v4.53.1) (2024-01-12)


### Bug Fixes

* :boom: Connecteur commandes dropshippés bloqué  ([f6a0aa3](https://github.com/uppler/uppler/commit/f6a0aa30cf97b1b0cb533f85bc3545d39fb71eca)), closes [#18972](https://github.com/uppler/uppler/issues/18972)
* :boom: Erreur de requête elastic pour un email de rétention  ([954aedd](https://github.com/uppler/uppler/commit/954aeddc6fd40efe913ae0704c2be8982f50d91d)), closes [#18944](https://github.com/uppler/uppler/issues/18944)
* :boom: La fonction tri asc ou desc des produits en MO ne fonctionne plus  ([5b35cd1](https://github.com/uppler/uppler/commit/5b35cd1aee04a1dfc92c744a376d601973b74691)), closes [#18970](https://github.com/uppler/uppler/issues/18970)
* :boom: Proposition tarifaire 000001201 appliquée à tous les clients  ([884b1ad](https://github.com/uppler/uppler/commit/884b1adbef443ea13baae945c71e79515f17843a)), closes [#18968](https://github.com/uppler/uppler/issues/18968)

# [4.53.0](https://github.com/uppler/uppler/compare/v4.52.0...v4.53.0) (2024-01-11)


### Features

* :star: [NMP8] Horaires de location  ([9347591](https://github.com/uppler/uppler/commit/9347591628b527bb6c00060bb779be0c64399691)), closes [#18475](https://github.com/uppler/uppler/issues/18475) [#18715](https://github.com/uppler/uppler/issues/18715)

# [4.52.0](https://github.com/uppler/uppler/compare/v4.51.2...v4.52.0) (2024-01-10)


### Bug Fixes

* :boom: (suite Github [#18872](https://github.com/uppler/uppler/issues/18872)) Import de produit : la valeur "all" retourne une erreur pour les champs "orderable-type" et "quotable-type"  ([1b7e2d8](https://github.com/uppler/uppler/commit/1b7e2d800b79b611425b3df4bc58c5d75710cbcd)), closes [#18947](https://github.com/uppler/uppler/issues/18947)
* :boom: Import en succès mais les données ne sont pas à jour  ([bf5dc39](https://github.com/uppler/uppler/commit/bf5dc39457e633d7df67057f2fbee3e4a19db3aa)), closes [#18936](https://github.com/uppler/uppler/issues/18936)
* :boom: Statut commande non confirmée sur Uppler  ([726d2f9](https://github.com/uppler/uppler/commit/726d2f913bc98660b46dd99cb49465860176781e)), closes [#18939](https://github.com/uppler/uppler/issues/18939)


### Features

* ⭐ [CMX108] Langue de l'émail auto  ([16aee3b](https://github.com/uppler/uppler/commit/16aee3b48ce4fb3986bb45db63dcdac15bc0e40c)), closes [#18518](https://github.com/uppler/uppler/issues/18518)

## [4.51.2](https://github.com/uppler/uppler/compare/v4.51.1...v4.51.2) (2024-01-08)


### Bug Fixes

* :boom: Incohérence entre les résultats d'un export et les données dans les résultats des filtres.  ([e9f86b2](https://github.com/uppler/uppler/commit/e9f86b2bbc7fa334d94d7ed76fe5e9f8eb9f5d6e)), closes [#18942](https://github.com/uppler/uppler/issues/18942)

## [4.51.1](https://github.com/uppler/uppler/compare/v4.51.0...v4.51.1) (2024-01-05)


### Bug Fixes

*  💥 Page inexistante qui ne renvoie pas vers la page shop (bis)  ([38dfa55](https://github.com/uppler/uppler/commit/38dfa557a8937e30955161e16f8689a607fb9d95)), closes [#18730](https://github.com/uppler/uppler/issues/18730)

# [4.51.0](https://github.com/uppler/uppler/compare/v4.50.2...v4.51.0) (2024-01-02)


### Bug Fixes

* :boom: Google Analytics - Tracking retiré lors de la redirection.  ([c5e1451](https://github.com/uppler/uppler/commit/c5e14515d94aedd790eeb0c5125f21d71b2f81a7)), closes [#18794](https://github.com/uppler/uppler/issues/18794)


### Features

* :star: [QAN28] - API Acheteur - Indiquer si une variante n'est pas active.  ([0c3a7e6](https://github.com/uppler/uppler/commit/0c3a7e6bb43ad381ac179214530636f0b8d8699d)), closes [#18488](https://github.com/uppler/uppler/issues/18488)

## [4.50.2](https://github.com/uppler/uppler/compare/v4.50.1...v4.50.2) (2023-12-29)


### Bug Fixes

* :boom: Afficher les nouvelles macros dans la liste déroulante des macros disponibles  ([0fb0d02](https://github.com/uppler/uppler/commit/0fb0d02f33ab5490f1531f80189db58bcf6adfdb)), closes [#18887](https://github.com/uppler/uppler/issues/18887) [#18911](https://github.com/uppler/uppler/issues/18911)
* :boom: L'edition d'un produit vendeur par l'opérateur supprime la mise en forme de la description  ([0f1fbc0](https://github.com/uppler/uppler/commit/0f1fbc0f4bf997d81040da74cb56ea5510903384)), closes [#18869](https://github.com/uppler/uppler/issues/18869)
* :boom: Le bouton 'Réinitialiser les filtres' des sous menus d'un menu ne renvoie pas à la bonne adresse dans le BO  ([768cd4e](https://github.com/uppler/uppler/commit/768cd4eac13888e9ecd43a0944a1ab478254ea46)), closes [#18899](https://github.com/uppler/uppler/issues/18899)

## [4.50.1](https://github.com/uppler/uppler/compare/v4.50.0...v4.50.1) (2023-12-28)


### Bug Fixes

* :boom: Erreur 500 à l'édition d'un produit en MO  ([e27b28a](https://github.com/uppler/uppler/commit/e27b28afc27f9675833e6bd916ba6878c61c3ecb)), closes [#18858](https://github.com/uppler/uppler/issues/18858)
* :boom: Modification d'une propriété non obligatoire modifie automatiquement certaines visibilités du produit  ([a72b507](https://github.com/uppler/uppler/commit/a72b5071e26fb09fc11d918f33de7e102a0083f1)), closes [#18872](https://github.com/uppler/uppler/issues/18872)

# [4.50.0](https://github.com/uppler/uppler/compare/v4.49.1...v4.50.0) (2023-12-22)


### Bug Fixes

* :boom: Erreur 500 suppression des commandes type panier expirées  ([ef67d98](https://github.com/uppler/uppler/commit/ef67d988728e25780e20aeeb00d4f32f5357ff11)), closes [#18842](https://github.com/uppler/uppler/issues/18842)


### Features

* :star: Suite QAN21 - Lemonway -  Sauvegarde des informations bancaire lors d'un prélèvement.  ([9de4c2b](https://github.com/uppler/uppler/commit/9de4c2ba73df99da4816ac7fbf9a1f3245ac9034)), closes [#17858](https://github.com/uppler/uppler/issues/17858) [#18611](https://github.com/uppler/uppler/issues/18611)

## [4.49.1](https://github.com/uppler/uppler/compare/v4.49.0...v4.49.1) (2023-12-21)


### Bug Fixes

* :boom: Erreur 500 en PROD lors de la suppresion d'un produit depuis le BO et le MO mais le produit es supprimé.  ([5770eff](https://github.com/uppler/uppler/commit/5770effa26949cc4e4406c565774f16c29e948e1)), closes [#18873](https://github.com/uppler/uppler/issues/18873)

# [4.49.0](https://github.com/uppler/uppler/compare/v4.48.3...v4.49.0) (2023-12-19)


### Bug Fixes

* :boom: Commande prod - Annulation - Erreur 500  ([b242dc8](https://github.com/uppler/uppler/commit/b242dc89e70ccc473b48e90b3eaf5e771c9c7bc9)), closes [#18837](https://github.com/uppler/uppler/issues/18837)


### Features

* :star: [QAN22] Prélèvement bancaire - Délais de paiement différents par acheteurs  ([384e4ce](https://github.com/uppler/uppler/commit/384e4ce843585be965dcd3afda8af7cdc248bf58)), closes [#17942](https://github.com/uppler/uppler/issues/17942) [#18763](https://github.com/uppler/uppler/issues/18763)

## [4.48.3](https://github.com/uppler/uppler/compare/v4.48.2...v4.48.3) (2023-12-18)


### Bug Fixes

* :boom: Erreur 500 lors d'un export de produits en BO  ([57e3b9d](https://github.com/uppler/uppler/commit/57e3b9d0d3fcd63f082e36bc6c0aef6d176e1833)), closes [#18840](https://github.com/uppler/uppler/issues/18840)

## [4.48.2](https://github.com/uppler/uppler/compare/v4.48.1...v4.48.2) (2023-12-14)


### Bug Fixes

* :boom: Certains produits ne sont pas visible en langue EN mais visible en langue FR  ([49cad72](https://github.com/uppler/uppler/commit/49cad72d03269c861ef1321c4f0be1caf244355a)), closes [#18805](https://github.com/uppler/uppler/issues/18805)
* :boom: Commandes > Ligne de paiement remboursement erronée sur la colonne MONTANT  ([541a801](https://github.com/uppler/uppler/commit/541a8017bf1771de6a5c36e9ab9008b9e434dbc5)), closes [#18634](https://github.com/uppler/uppler/issues/18634)
* :boom: Tender & Tender proposal : macro "[text:{object:user,field:username}]" en renvoie pas la bone valeur  ([3cfc4bc](https://github.com/uppler/uppler/commit/3cfc4bcaeac9d35edc341b03f80d1c055c1e806b)), closes [#18810](https://github.com/uppler/uppler/issues/18810)

## [4.48.1](https://github.com/uppler/uppler/compare/v4.48.0...v4.48.1) (2023-12-14)


### Bug Fixes

* :boom: Lenteurs lors de l'accès a certaines pages de modèles de données  ([283003b](https://github.com/uppler/uppler/commit/283003b92ccd22a1eb06f9566616dbcf283ef341)), closes [#18626](https://github.com/uppler/uppler/issues/18626)

# [4.48.0](https://github.com/uppler/uppler/compare/v4.47.1...v4.48.0) (2023-12-12)


### Bug Fixes

* :boom: Informations manquantes dans le dataLayer  ([887b476](https://github.com/uppler/uppler/commit/887b476e9db6698d7765c7578fa9048962ed63d0)), closes [#18795](https://github.com/uppler/uppler/issues/18795)


### Features

* :star: [OZ38] Export produit - Partage de modèle de données  ([af653f5](https://github.com/uppler/uppler/commit/af653f5e77a0451b1bc0a8a435b14688794cb66c)), closes [#18157](https://github.com/uppler/uppler/issues/18157) [#18739](https://github.com/uppler/uppler/issues/18739)

## [4.47.1](https://github.com/uppler/uppler/compare/v4.47.0...v4.47.1) (2023-12-11)


### Bug Fixes

* :boom: Annuler la signature électronique / quitter la page confirme la commande  ([a3110e7](https://github.com/uppler/uppler/commit/a3110e758288cc8333c883bad3a0e3327c512b1d)), closes [#18600](https://github.com/uppler/uppler/issues/18600)
* :boom: Contenu widget "Grille des meilleures ventes en volume" anormal  ([2190a5e](https://github.com/uppler/uppler/commit/2190a5eddcbdec543deb4a1a15b1d714b9f94b85)), closes [#18392](https://github.com/uppler/uppler/issues/18392) [#18691](https://github.com/uppler/uppler/issues/18691)

# [4.47.0](https://github.com/uppler/uppler/compare/v4.46.3...v4.47.0) (2023-12-07)


### Bug Fixes

* :boom: BUG - Ajustement connecteur internes commandes & commandes dropshippées  ([af05c59](https://github.com/uppler/uppler/commit/af05c59b5df2acf6e09ee9af3448f8f33ad767f8)), closes [#18410](https://github.com/uppler/uppler/issues/18410) [#18100](https://github.com/uppler/uppler/issues/18100) [#18100](https://github.com/uppler/uppler/issues/18100) [#18220](https://github.com/uppler/uppler/issues/18220)


### Features

* :star: [CS6] Minimum de commande par dropshipping   ([7e45b66](https://github.com/uppler/uppler/commit/7e45b66c3e9c8b6a3e8b24217008d2cb42e08942)), closes [#17570](https://github.com/uppler/uppler/issues/17570) [#18682](https://github.com/uppler/uppler/issues/18682)

## [4.46.3](https://github.com/uppler/uppler/compare/v4.46.2...v4.46.3) (2023-12-05)


### Bug Fixes

* :boom: Valeur d'option en cours de suppression depuis plusieurs jours  ([644b083](https://github.com/uppler/uppler/commit/644b08341f939176e07775055389fc80bd177cc7)), closes [#18713](https://github.com/uppler/uppler/issues/18713)

## [4.46.2](https://github.com/uppler/uppler/compare/v4.46.1...v4.46.2) (2023-12-04)


### Bug Fixes

* :boom: Commande avec paiement échoué   ([8ebae95](https://github.com/uppler/uppler/commit/8ebae953a3fed652025648ce2d8ffe8f889eb2c8)), closes [#18679](https://github.com/uppler/uppler/issues/18679)

## [4.46.1](https://github.com/uppler/uppler/compare/v4.46.0...v4.46.1) (2023-12-01)


### Bug Fixes

* :boom: Absence de message d'erreur lors de l'ajout d'un fichier dans un format non accepté  ([96a5206](https://github.com/uppler/uppler/commit/96a52064d401d323f8776028f1a051eda4145a63)), closes [#18608](https://github.com/uppler/uppler/issues/18608) [#18703](https://github.com/uppler/uppler/issues/18703)
* 💥 Date de fin d'appel d'offre  ([2ba4c0d](https://github.com/uppler/uppler/commit/2ba4c0d436878a59c8d93f81cb0085dbe9c817e5)), closes [#18601](https://github.com/uppler/uppler/issues/18601) [#18738](https://github.com/uppler/uppler/issues/18738)

# [4.46.0](https://github.com/uppler/uppler/compare/v4.45.7...v4.46.0) (2023-11-28)


### Bug Fixes

* :boom: Erreur 500 suppression des commandes type panier expirées  ([352c932](https://github.com/uppler/uppler/commit/352c9327343007e1cf4cd59fdac738797c6b7a9d)), closes [#18697](https://github.com/uppler/uppler/issues/18697)


### Features

* :star: [BF16] Export entités dynamiques - Ajout de l'ID des participants en plus des noms  ([b8868f5](https://github.com/uppler/uppler/commit/b8868f506f9d58a5669aeabdaad1880823128e3a)), closes [#18542](https://github.com/uppler/uppler/issues/18542)

## [4.45.7](https://github.com/uppler/uppler/compare/v4.45.6...v4.45.7) (2023-11-28)


### Bug Fixes

* :boom: On ne peut pas ajouter deux fois de suite une pièce jointe dans un appel d'offre  ([b46303d](https://github.com/uppler/uppler/commit/b46303dac3b5f9d8b63312b686eac2a41de6bdbd)), closes [#18610](https://github.com/uppler/uppler/issues/18610)

## [4.45.6](https://github.com/uppler/uppler/compare/v4.45.5...v4.45.6) (2023-11-24)


### Bug Fixes

* :boom: Proposition vendeur appel d'offre - Retirer le symbole de la devise - Suite  ([c9308fa](https://github.com/uppler/uppler/commit/c9308fa029b5281180e5c26e42481f8c9c6ca98b)), closes [#18623](https://github.com/uppler/uppler/issues/18623)

## [4.45.5](https://github.com/uppler/uppler/compare/v4.45.4...v4.45.5) (2023-11-23)


### Bug Fixes

* :boom: Montant de frais de ports comptabilisés dans le total du minimum d'achats  ([664a6aa](https://github.com/uppler/uppler/commit/664a6aa676ab53476378946f12b45bba5c686493)), closes [#18569](https://github.com/uppler/uppler/issues/18569)

## [4.45.4](https://github.com/uppler/uppler/compare/v4.45.3...v4.45.4) (2023-11-21)


### Bug Fixes

* :boom: Erreur de division par 0 dans filtre prix du shop en FO  ([4cb995e](https://github.com/uppler/uppler/commit/4cb995e403cc724b014a18a0e7a5479e7eb80b02)), closes [#18695](https://github.com/uppler/uppler/issues/18695)
* :boom: Sitemap pas beau   ([b559dba](https://github.com/uppler/uppler/commit/b559dba7404dae20d7af9973582405bca2fd53b5)), closes [#18685](https://github.com/uppler/uppler/issues/18685)
* Image .webp plus lourde après upload  ([d1839c2](https://github.com/uppler/uppler/commit/d1839c233bece5c4b4930a00ced5a7830030cb72)), closes [#18677](https://github.com/uppler/uppler/issues/18677)
* Pourcentage remise KO sur listing d'offre (-4% au lieu de -40%)  ([452064e](https://github.com/uppler/uppler/commit/452064ee7a279c5b32eb889189e2e15a3ea8747d)), closes [#18674](https://github.com/uppler/uppler/issues/18674)

## [4.45.3](https://github.com/uppler/uppler/compare/v4.45.2...v4.45.3) (2023-11-16)


### Bug Fixes

* :boom: Erreur 500 dans le panier   ([b2e1ecd](https://github.com/uppler/uppler/commit/b2e1ecd33c68fd8a8fcf3fc2c4db1f041c8677f0)), closes [#18654](https://github.com/uppler/uppler/issues/18654)
* :boom: Erreur lors du parsing des donné de la lib sso  ([4b93568](https://github.com/uppler/uppler/commit/4b9356834444f8d4121723de4d96f91db1a62129)), closes [#18664](https://github.com/uppler/uppler/issues/18664)
* :boom: Message d'erreur lors de la validation  ([3662e16](https://github.com/uppler/uppler/commit/3662e16e9fcdb6da6705855b17532017aa41b7a0)), closes [#18644](https://github.com/uppler/uppler/issues/18644)

## [4.45.2](https://github.com/uppler/uppler/compare/v4.45.1...v4.45.2) (2023-11-14)


### Bug Fixes

* :boom: [NMP13] Intégration options avancées  ([1d87e31](https://github.com/uppler/uppler/commit/1d87e31e039b041edcbd37910e2c7c5dcc299e12)), closes [#18645](https://github.com/uppler/uppler/issues/18645)
* :boom: Message d'erreur non parlant sur un import d'image  ([236f687](https://github.com/uppler/uppler/commit/236f6875e3652fe564ff3f3d11d6f71f7ec299c5)), closes [#18621](https://github.com/uppler/uppler/issues/18621)

## [4.45.1](https://github.com/uppler/uppler/compare/v4.45.0...v4.45.1) (2023-11-10)


### Bug Fixes

* :boom: "Text Macros" absent du template de mail  ([9356be2](https://github.com/uppler/uppler/commit/9356be206c79ba8cd7f6107500bfc6044ef1fb23)), closes [#18619](https://github.com/uppler/uppler/issues/18619)
* :boom: Erreur 500 inscription/connexion SSO à cause d'un doublon  ([cb42d9c](https://github.com/uppler/uppler/commit/cb42d9c1186019a4fee0efaf29ad6f25dab4f7f9)), closes [#18602](https://github.com/uppler/uppler/issues/18602)
* :boom: Erreur lors du retry d'imports de données qui date d'avant 2023  ([8ffbc4f](https://github.com/uppler/uppler/commit/8ffbc4f0c1ff9d37362b245656522a4f8ddf7bb1)), closes [#18599](https://github.com/uppler/uppler/issues/18599)
* :boom: Image alourdies par la plateforme  ([ea46c5e](https://github.com/uppler/uppler/commit/ea46c5e7ca7e16b38c40c0a38b24c12e428ff61f)), closes [#18579](https://github.com/uppler/uppler/issues/18579)
* :boom: Impossible d'enregistrer les modifications de profil vendeur  ([cc5591e](https://github.com/uppler/uppler/commit/cc5591e76daa3dc0e71f6470c768b921eca6fc57)), closes [#18587](https://github.com/uppler/uppler/issues/18587)
* :boom: Newsletter ne s'envoie pas en recette  ([fbfb6e8](https://github.com/uppler/uppler/commit/fbfb6e8ba9e102302d5557406d1b72b6ea390a17)), closes [#18573](https://github.com/uppler/uppler/issues/18573)
* :boom: Sous-compte acheteur désactivé, mais appels API fonctionnels  ([f2c7ba9](https://github.com/uppler/uppler/commit/f2c7ba960a4635318dbd2b1f9f9dc6ee39b766cb)), closes [#18593](https://github.com/uppler/uppler/issues/18593)

# [4.45.0](https://github.com/uppler/uppler/compare/v4.44.0...v4.45.0) (2023-11-08)


### Bug Fixes

* :boom: Filtre catégorie non pris en compte dans l'export produit en BO   ([c6fd0b8](https://github.com/uppler/uppler/commit/c6fd0b8dbe95d902bd016d2765f6f5b308083451)), closes [#18560](https://github.com/uppler/uppler/issues/18560)
* :boom: Impossible de rattacher une offre à un produit master invisible  ([ec341a1](https://github.com/uppler/uppler/commit/ec341a1a1e02cdbac522db7f8b37ae7eb53ef85d)), closes [#18581](https://github.com/uppler/uppler/issues/18581)
* :boom: Impossible de supprimer un Accès rapide depuis le BO  ([1555a3e](https://github.com/uppler/uppler/commit/1555a3e0777dcc2292ee6f2f60d4e6e75ccc7c63)), closes [#18568](https://github.com/uppler/uppler/issues/18568)
* :boom: Possibilité de supprimer un prix d'une liste de prix "bloqué" via contrat spécifique.  ([3582d86](https://github.com/uppler/uppler/commit/3582d86075fcdb32b411e51b9c5c7d98e04628e1)), closes [#18529](https://github.com/uppler/uppler/issues/18529)
* :boom: Proposition vendeur appel d'offre - Retirer le symbole de la devise  ([e95b66c](https://github.com/uppler/uppler/commit/e95b66c0029f1e3e6ee519bf90ae0f2c988e2cee)), closes [#18571](https://github.com/uppler/uppler/issues/18571)
* :boom: Récupération via l'API de la description vendeur  ([970f496](https://github.com/uppler/uppler/commit/970f496f27fffec1ca1777e52e9773bf6d5df61a)), closes [#18580](https://github.com/uppler/uppler/issues/18580)


### Features

* :star: [NMP13]: Intégration SSO Azure ADFS V2  ([28be722](https://github.com/uppler/uppler/commit/28be7229ab1b8a1d9f2ebb2534d8d6421634e6aa)), closes [#18555](https://github.com/uppler/uppler/issues/18555)

# [4.44.0](https://github.com/uppler/uppler/compare/v4.43.3...v4.44.0) (2023-11-07)


### Bug Fixes

* :boom: Connecteur Devis Promo CST - aucun remise dans Sage  ([fc7638a](https://github.com/uppler/uppler/commit/fc7638ab5b2b1bddce45481d355c0ec00b2bee8f)), closes [#18564](https://github.com/uppler/uppler/issues/18564)
* :boom: Erreur 500 lors de la validation d'une commande avec paiement par Virement  ([bbc122c](https://github.com/uppler/uppler/commit/bbc122c1831b813fae391c8f5a344627d84fe47c)), closes [#18589](https://github.com/uppler/uppler/issues/18589)
* :boom: Stock négatif pas déduit des futures réassort   ([f73ec08](https://github.com/uppler/uppler/commit/f73ec0828611ab6fd5d2e6b2bc2ca634ecffab6f)), closes [#18549](https://github.com/uppler/uppler/issues/18549) [#18586](https://github.com/uppler/uppler/issues/18586)


### Features

* ⭐ [QAN21] Prélèvement bancaire Lemonway  ([1c144a6](https://github.com/uppler/uppler/commit/1c144a647c6fdfa774d70305e25e1c1f961aec80)), closes [#17393](https://github.com/uppler/uppler/issues/17393) [#17988](https://github.com/uppler/uppler/issues/17988)

## [4.43.3](https://github.com/uppler/uppler/compare/v4.43.2...v4.43.3) (2023-11-06)


### Bug Fixes

* :boom: Erreur 500 sur le sitemap   ([5543e98](https://github.com/uppler/uppler/commit/5543e980ebc03a3272ac211fb19d48c885049748)), closes [#18577](https://github.com/uppler/uppler/issues/18577)
* :boom: Produit rattaché à un acheteur suite à la création MO  ([415080b](https://github.com/uppler/uppler/commit/415080beda1040c62a2f28f3eb947f33e9f51a5f)), closes [#18524](https://github.com/uppler/uppler/issues/18524)

## [4.43.2](https://github.com/uppler/uppler/compare/v4.43.1...v4.43.2) (2023-11-03)


### Bug Fixes

* :boom: Vendeur peut envoyer un message à un autre vendeur  ([95ecb07](https://github.com/uppler/uppler/commit/95ecb0719afe5b1a48e18535765a5a2237b6672e)), closes [#18472](https://github.com/uppler/uppler/issues/18472)

## [4.43.1](https://github.com/uppler/uppler/compare/v4.43.0...v4.43.1) (2023-11-02)


### Bug Fixes

* :boom: génération facture commande virement  ([fccd826](https://github.com/uppler/uppler/commit/fccd826b2cb4e09dcce53a59ff909b473faecf21)), closes [#18452](https://github.com/uppler/uppler/issues/18452)

# [4.43.0](https://github.com/uppler/uppler/compare/v4.42.3...v4.43.0) (2023-10-30)


### Bug Fixes

* :boom: Commande en accepté et pas en transféré du coup commissions en erreur   ([61439da](https://github.com/uppler/uppler/commit/61439da684928b9b831d3462b87581fb4ed5cc8a)), closes [#18534](https://github.com/uppler/uppler/issues/18534)
* :boom: Erreur 500 dans le formulaire d'un article de commande sur la quantité d'incrémentation  ([3a8989a](https://github.com/uppler/uppler/commit/3a8989ad6f51daf84dd4bda13e29c2303ea68813)), closes [#18537](https://github.com/uppler/uppler/issues/18537)
* :boom: Mauvais calcul de la TVA  ([b1c50ca](https://github.com/uppler/uppler/commit/b1c50ca17a6f050664adc3f6accb05c55a5f630a)), closes [#18548](https://github.com/uppler/uppler/issues/18548)
* :boom: Plateforme inaccessible pour un sous-compte vendeur qui ne s'est jamais connecté et qui n'a pas d'abonnement  ([eb0e9b1](https://github.com/uppler/uppler/commit/eb0e9b141ad85342a6484027f0ff06b92ba97a21)), closes [#18541](https://github.com/uppler/uppler/issues/18541)


### Features

* :star: [NMP13]: Intégration SSO Azure ADFS  ([77ba66b](https://github.com/uppler/uppler/commit/77ba66baffeac38166b02199a70d1961b6ce123a)), closes [#18316](https://github.com/uppler/uppler/issues/18316)
* :star: Ajout produit dropshippé wishlist  ([4700ad3](https://github.com/uppler/uppler/commit/4700ad393aebcb9b2aa744262a6582995774384c)), closes [#18314](https://github.com/uppler/uppler/issues/18314) [#18474](https://github.com/uppler/uppler/issues/18474) [#18474](https://github.com/uppler/uppler/issues/18474)

## [4.42.3](https://github.com/uppler/uppler/compare/v4.42.2...v4.42.3) (2023-10-27)


### Bug Fixes

* :boom: Aperçu panier : Prix affiché TTC  ([e61339f](https://github.com/uppler/uppler/commit/e61339fe5fd8fcd8e06f3db22a965460d226753d)), closes [#18519](https://github.com/uppler/uppler/issues/18519)
* :boom: Renommer fonctionnalité enchères  ([bdb88f6](https://github.com/uppler/uppler/commit/bdb88f6d6580b352456dc9c21a52937414d817ed)), closes [#18530](https://github.com/uppler/uppler/issues/18530)

## [4.42.2](https://github.com/uppler/uppler/compare/v4.42.1...v4.42.2) (2023-10-26)


### Bug Fixes

* :boom: Création adresse de facturation - champs obligatoire non indiqué (astérisque)  ([779df6c](https://github.com/uppler/uppler/commit/779df6c51b3a345bc97764f089d095ff3e110b35)), closes [#18497](https://github.com/uppler/uppler/issues/18497)
* :boom: Disponibilité des produits dans la commande   ([158197c](https://github.com/uppler/uppler/commit/158197c31518eb0897cb1fb24ae9b446937f64ad)), closes [#18449](https://github.com/uppler/uppler/issues/18449) [#18494](https://github.com/uppler/uppler/issues/18494)
* :boom: Doublon sur la recherche des produits pas api   ([3069ad8](https://github.com/uppler/uppler/commit/3069ad8d650b4185ad57580bbd300b85b8da4b48)), closes [#18486](https://github.com/uppler/uppler/issues/18486)
* :boom: Formulaire produit : champ valeur incrémentale de type texte  ([36303e5](https://github.com/uppler/uppler/commit/36303e57e45212c86c7e0020b7aa2f0853b91d6c)), closes [#18507](https://github.com/uppler/uppler/issues/18507)
* :boom: Import - Modèles d'import disparus  ([3dc379c](https://github.com/uppler/uppler/commit/3dc379c4f63ab5feb2cb46277960525c267ee34a)), closes [#18428](https://github.com/uppler/uppler/issues/18428)
* :boom: UPELA toujours connecté à la PROD otego  ([eb991b1](https://github.com/uppler/uppler/commit/eb991b1cb55a3498cce443a599e93d72977ae8f0)), closes [#18515](https://github.com/uppler/uppler/issues/18515)

## [4.42.1](https://github.com/uppler/uppler/compare/v4.42.0...v4.42.1) (2023-10-25)


### Bug Fixes

* :boom: Import produit bloqué à cause du master   ([bdcfb25](https://github.com/uppler/uppler/commit/bdcfb2543826af22d2e3c37d3a21cfe5fb3de8d1)), closes [#18499](https://github.com/uppler/uppler/issues/18499)

# [4.42.0](https://github.com/uppler/uppler/compare/v4.41.0...v4.42.0) (2023-10-24)


### Bug Fixes

* :boom: Pb href lag sur page paginées  ([5a667c4](https://github.com/uppler/uppler/commit/5a667c4a46b92c3f3720d76134eabac5d3ea7557)), closes [#18483](https://github.com/uppler/uppler/issues/18483)


### Features

* ⭐ [CMX101] Push produits catégories produits non-filtrée ([9f04c8e](https://github.com/uppler/uppler/commit/9f04c8e3a7368674ef4baffb8d1a7e08dd680ee7))

# [4.41.0](https://github.com/uppler/uppler/compare/v4.40.0...v4.41.0) (2023-10-23)


### Bug Fixes

* :boom: Shop vendeur - Filtre prix n'inclut pas le prix le plus elevé pour un produit  ([a99e49d](https://github.com/uppler/uppler/commit/a99e49d33855f974c1380d327b951cdd70cad716)), closes [#18436](https://github.com/uppler/uppler/issues/18436)


### Features

* :star: [NAV26] Gestion documentaire V2  ([6597051](https://github.com/uppler/uppler/commit/659705184a2fdf0c854d8ae961a429b8f3ebf56e)), closes [#17658](https://github.com/uppler/uppler/issues/17658) [#18159](https://github.com/uppler/uppler/issues/18159)
* :star: [QAN23] API - POST/PATCH d'un buyer sur les champs adresse  ([b9ed950](https://github.com/uppler/uppler/commit/b9ed9507e87a312cecb5d0675297a8c6ce788679)), closes [#18019](https://github.com/uppler/uppler/issues/18019)

# [4.40.0](https://github.com/uppler/uppler/compare/v4.39.2...v4.40.0) (2023-10-20)


### Bug Fixes

* :boom: Création commande par vendeur - Ajout de produit impossible  ([11adc10](https://github.com/uppler/uppler/commit/11adc10b07b14fe5bffc921f255e87448df71204)), closes [#18376](https://github.com/uppler/uppler/issues/18376)
* :boom: Import méthode de livraison  ([bc2cb09](https://github.com/uppler/uppler/commit/bc2cb09f17f372073d79cecfd2dbc03818df9fbc)), closes [#18292](https://github.com/uppler/uppler/issues/18292)
* :boom: Livraison partielle dropshipping - "erreur : Il n'est actuellement pas possible de changer le statut de la livraison."  ([99d689a](https://github.com/uppler/uppler/commit/99d689adb51d77f547a030d9c19866dea4e5da9b)), closes [#18441](https://github.com/uppler/uppler/issues/18441)
* :boom: Pb autoréférentiel Home  ([ced0cab](https://github.com/uppler/uppler/commit/ced0cabef8732544034e6e9580c28048a99a9562)), closes [#18460](https://github.com/uppler/uppler/issues/18460)


### Features

* :star: [PA30] Ajout format images  ([a2a2b5d](https://github.com/uppler/uppler/commit/a2a2b5d85e189bd437687001172f09461f2cbdd8)), closes [#18110](https://github.com/uppler/uppler/issues/18110)

## [4.39.2](https://github.com/uppler/uppler/compare/v4.39.1...v4.39.2) (2023-10-18)


### Bug Fixes

* :boom: Page shop - Filtres qui disparaissent lorsqu'on change de page  ([79768da](https://github.com/uppler/uppler/commit/79768da9f9f74a85f0ebc9afa3eea9ea66a27356)), closes [#18408](https://github.com/uppler/uppler/issues/18408)

## [4.39.1](https://github.com/uppler/uppler/compare/v4.39.0...v4.39.1) (2023-10-16)


### Bug Fixes

* :boom: Affichage contenu email export de données acheteur  ([9159eee](https://github.com/uppler/uppler/commit/9159eee8adde24d70deaca982b9ed480b43e9153)), closes [#18366](https://github.com/uppler/uppler/issues/18366)
* :boom: Aucun message d'erreur lors de l'échec d'upload d'un fichier sur un formulaire d'inscription  ([fb81acf](https://github.com/uppler/uppler/commit/fb81acf644a55b0341fc37e791552ad82ee8060a)), closes [#18427](https://github.com/uppler/uppler/issues/18427)
* :boom: Meta title seulement dans la langue par défaut   ([f97d2b0](https://github.com/uppler/uppler/commit/f97d2b0fd986fbfe115dc0930f604de329629ea9)), closes [#18446](https://github.com/uppler/uppler/issues/18446)
* :boom: Variante non exclue lors d'un import de promotion  ([3981447](https://github.com/uppler/uppler/commit/3981447e2107af46caba98f085764693d4824756)), closes [#18399](https://github.com/uppler/uppler/issues/18399)
* :boom: Visibilité acheteur contrat spécifique  ([47161ab](https://github.com/uppler/uppler/commit/47161abf8f4127162afa72b1ea008c90e5f61c42)), closes [#18412](https://github.com/uppler/uppler/issues/18412)

# [4.39.0](https://github.com/uppler/uppler/compare/v4.38.2...v4.39.0) (2023-10-12)


### Bug Fixes

* :boom: Facture d'avoir sans information   ([e713f1f](https://github.com/uppler/uppler/commit/e713f1f3a371cc97b815a8ab3861d6fdfa4de129)), closes [#18329](https://github.com/uppler/uppler/issues/18329) [#18440](https://github.com/uppler/uppler/issues/18440)


### Features

* :star: [CMX101] Push produits catégories produits non-filtrée  ([81f2cfd](https://github.com/uppler/uppler/commit/81f2cfdf2be1a2c87275553d065a94efbb35ef62)), closes [#18294](https://github.com/uppler/uppler/issues/18294)


### Reverts

* Revert "feat: :star: [CMX101] Push produits catégories produits non-filtrée" (#18454) ([386f99f](https://github.com/uppler/uppler/commit/386f99fbcd3c4150606d2bf015dcd569feb283e7)), closes [#18454](https://github.com/uppler/uppler/issues/18454)

## [4.38.2](https://github.com/uppler/uppler/compare/v4.38.1...v4.38.2) (2023-10-11)


### Bug Fixes

* :boom: Affichage par catégories widget de produits aléatoires  ([8973520](https://github.com/uppler/uppler/commit/89735206157485c84e5d7128c4561d4ea376ec8a)), closes [#18421](https://github.com/uppler/uppler/issues/18421)
* :boom: Erreur balise Hreflang  ([1a2a3c7](https://github.com/uppler/uppler/commit/1a2a3c7c20ba6f4b1e05ec88d644fc5f26a692b8)), closes [#18342](https://github.com/uppler/uppler/issues/18342)
* :boom: Modèle d'import de donnée  ([84b69f0](https://github.com/uppler/uppler/commit/84b69f0819059ea25d8698cf170d4c92dec443cd)), closes [#18411](https://github.com/uppler/uppler/issues/18411)

## [4.38.1](https://github.com/uppler/uppler/compare/v4.38.0...v4.38.1) (2023-10-10)


### Bug Fixes

* :boom: Email KYC accepté non envoyé  ([98d67b4](https://github.com/uppler/uppler/commit/98d67b4cc4f6de66060e86d1fc71bc4895484157)), closes [#18420](https://github.com/uppler/uppler/issues/18420)

# [4.38.0](https://github.com/uppler/uppler/compare/v4.37.2...v4.38.0) (2023-10-09)


### Features

* :star: [OZ61] Pouvoir appliquer des remises à virgule dans les contrats et contrats DS  ([d0d4b3f](https://github.com/uppler/uppler/commit/d0d4b3fc5c81df3fc2e1a04d6d5a4918250ac50d)), closes [#17725](https://github.com/uppler/uppler/issues/17725) [#18363](https://github.com/uppler/uppler/issues/18363)

## [4.37.2](https://github.com/uppler/uppler/compare/v4.37.1...v4.37.2) (2023-10-05)


### Bug Fixes

* :boom: rattachement d'une offre à un master product impossible  ([208cc74](https://github.com/uppler/uppler/commit/208cc74b15aab37880bb06b0830a60d2ab61a27e)), closes [#18327](https://github.com/uppler/uppler/issues/18327) [#18390](https://github.com/uppler/uppler/issues/18390)

## [4.37.1](https://github.com/uppler/uppler/compare/v4.37.0...v4.37.1) (2023-10-04)


### Bug Fixes

* :boom: Erreur 500 soumission Etat des lieux location  ([a317e59](https://github.com/uppler/uppler/commit/a317e5997fce29a0f1957942098a96af845a9221)), closes [#18302](https://github.com/uppler/uppler/issues/18302)
* :boom: Fiche produit ne s'ouvre pas  ([f7b4c5d](https://github.com/uppler/uppler/commit/f7b4c5d167dbdca9f5cd059350ba89407692aefd)), closes [#18310](https://github.com/uppler/uppler/issues/18310)
* :boom: Selection des references impossible dans le contrat automatique en MO  ([c45948e](https://github.com/uppler/uppler/commit/c45948e890ae7702e2ad15d451b9676fc10ff6d8)), closes [#18365](https://github.com/uppler/uppler/issues/18365)
* :boom: Titre du champ phone dans formulaire kyc  ([96951e8](https://github.com/uppler/uppler/commit/96951e82a5ac3781d9c8cec7efdd5aecf7bbd23b)), closes [#18387](https://github.com/uppler/uppler/issues/18387)
* :boom: type text à la place d'un type choice lors du Matching import  ([eb081da](https://github.com/uppler/uppler/commit/eb081dabe28bbb479be099392ccfa14f2506dd59)), closes [#18356](https://github.com/uppler/uppler/issues/18356)

# [4.37.0](https://github.com/uppler/uppler/compare/v4.36.11...v4.37.0) (2023-10-03)


### Bug Fixes

* :boom: Matching des valeurs de propriété à l'import  ([e2743f0](https://github.com/uppler/uppler/commit/e2743f0ab2a424e1f06c1b8ba66f685a4506e03a)), closes [#18300](https://github.com/uppler/uppler/issues/18300)
* :boom: Message d'erreur différent commande/devis   ([fc12275](https://github.com/uppler/uppler/commit/fc122754d5d798c69a0761342b0239a585944301)), closes [#18307](https://github.com/uppler/uppler/issues/18307) [#18362](https://github.com/uppler/uppler/issues/18362)
* :boom: Recette - Impossible exporter produits depuis back office  ([67a11ec](https://github.com/uppler/uppler/commit/67a11ece6415dd3faf5c82b297bdda9f89af0d11)), closes [#18343](https://github.com/uppler/uppler/issues/18343)


### Features

* :star: [CMX92] Compléter champs dynamiques dans le profil du vendeur par api   ([e5291d4](https://github.com/uppler/uppler/commit/e5291d4858091ed18cc307ec82b0d4e10f5ba55f)), closes [#17621](https://github.com/uppler/uppler/issues/17621) [#18200](https://github.com/uppler/uppler/issues/18200)

## [4.36.11](https://github.com/uppler/uppler/compare/v4.36.10...v4.36.11) (2023-09-27)


### Bug Fixes

* :boom: Contenu dupliqué sur le meta descripiton   ([f02b557](https://github.com/uppler/uppler/commit/f02b557b7faebe45944d553f0c4dbd6c33b094e4)), closes [#18333](https://github.com/uppler/uppler/issues/18333)
* :boom: notifications commande en double  ([06188a5](https://github.com/uppler/uppler/commit/06188a5d598126b0d1990bc21e78fca28647c856)), closes [#18130](https://github.com/uppler/uppler/issues/18130)

## [4.36.10](https://github.com/uppler/uppler/compare/v4.36.9...v4.36.10) (2023-09-26)


### Bug Fixes

* :boom: Colonne produits décalés sur fiche vendeur en BO  ([e5e3f39](https://github.com/uppler/uppler/commit/e5e3f396d461f5023aef71e25f35616436a0c3df)), closes [#18323](https://github.com/uppler/uppler/issues/18323)
* :boom: Création d'un devis avec méthode de livraison hors upela  ([5c720fc](https://github.com/uppler/uppler/commit/5c720fc9c7ec5cfcc80eaf8b06a89f8e90ab43fc)), closes [#18056](https://github.com/uppler/uppler/issues/18056)

## [4.36.9](https://github.com/uppler/uppler/compare/v4.36.8...v4.36.9) (2023-09-25)


### Bug Fixes

* :boom: Export produit - Type de produit : "produit générique" export les offres et non les produits génériques  ([054280b](https://github.com/uppler/uppler/commit/054280b974d51199bab577746a3d9d7f7c000746)), closes [#18291](https://github.com/uppler/uppler/issues/18291)
* :boom: Numéro de téléphone ne respecte pas sa configuration sur KYC  ([17286cb](https://github.com/uppler/uppler/commit/17286cb79c8bf148a73bc387ebcafc402294ec31)), closes [#18239](https://github.com/uppler/uppler/issues/18239)
* :boom: Sitemap.xml => homepage sans langue indexé (vs /en et /fr indexé)  ([3ea670d](https://github.com/uppler/uppler/commit/3ea670db74badfe2ddfd284ab3b9b305894f7e0c)), closes [#18334](https://github.com/uppler/uppler/issues/18334)

## [4.36.8](https://github.com/uppler/uppler/compare/v4.36.7...v4.36.8) (2023-09-21)


### Bug Fixes

* :boom: Facture sous commande pas présentes pour une commande expédiée partiellement et payée par virement   ([7a59cac](https://github.com/uppler/uppler/commit/7a59caccb735c4b842353dac6a34ee0611cfdfc6)), closes [#18080](https://github.com/uppler/uppler/issues/18080)

## [4.36.7](https://github.com/uppler/uppler/compare/v4.36.6...v4.36.7) (2023-09-20)


### Bug Fixes

* :boom: Dépréciation php8 crash après déploiement  ([edfad67](https://github.com/uppler/uppler/commit/edfad6790d57ddc6d1b2a24ef717602c5af90c88)), closes [#18318](https://github.com/uppler/uppler/issues/18318)
* :boom: Image plus lourde après upload sur la plateforme   ([f101240](https://github.com/uppler/uppler/commit/f101240aa55a949a8f0167a1226fdcbe6c83ee20)), closes [#18048](https://github.com/uppler/uppler/issues/18048)

## [4.36.6](https://github.com/uppler/uppler/compare/v4.36.5...v4.36.6) (2023-09-19)


### Bug Fixes

* :boom: Affichage calendrier datetime_range  ([0e61e6d](https://github.com/uppler/uppler/commit/0e61e6d3fbef0ea78948f9ffc05377fcaf34200e)), closes [#18248](https://github.com/uppler/uppler/issues/18248)
* :boom: Erreur valeur increment value dans l'export  ([859c230](https://github.com/uppler/uppler/commit/859c23082c937d08271469b11dd841d96bfd9aea)), closes [#18304](https://github.com/uppler/uppler/issues/18304)

## [4.36.5](https://github.com/uppler/uppler/compare/v4.36.4...v4.36.5) (2023-09-18)


### Bug Fixes

* :boom: Promo sur méthode de paiement - choix méthode de paiement  ([119f4fb](https://github.com/uppler/uppler/commit/119f4fb987729ce6b5793656313342d59eccbc5f)), closes [#17959](https://github.com/uppler/uppler/issues/17959)

## [4.36.4](https://github.com/uppler/uppler/compare/v4.36.3...v4.36.4) (2023-09-15)


### Bug Fixes

* :boom: Ajout meta sur page login impossible  ([9993433](https://github.com/uppler/uppler/commit/999343382195c76cc700886a9b3aec627047d859)), closes [#18231](https://github.com/uppler/uppler/issues/18231)
* :boom: Href lang incorrect sur shop  ([92407e3](https://github.com/uppler/uppler/commit/92407e3b2625247e3f802507d793e359871f158a)), closes [#18286](https://github.com/uppler/uppler/issues/18286)
* :boom: Payout en echec   ([63e7bd6](https://github.com/uppler/uppler/commit/63e7bd619d5c38a9ff8e1448dfeb300a7028a973)), closes [#18063](https://github.com/uppler/uppler/issues/18063)
* :boom: résultat api pas cohérent   ([83216dd](https://github.com/uppler/uppler/commit/83216dd7e73dece9dd2b6f0c8166e1e86a5a12f4)), closes [#18258](https://github.com/uppler/uppler/issues/18258)

## [4.36.3](https://github.com/uppler/uppler/compare/v4.36.2...v4.36.3) (2023-09-13)


### Bug Fixes

* :boom: Erreur 500 switch prix wholesale  ([9589efd](https://github.com/uppler/uppler/commit/9589efd509b79287237e2275890070538ddfff3b)), closes [#18136](https://github.com/uppler/uppler/issues/18136)

## [4.36.2](https://github.com/uppler/uppler/compare/v4.36.1...v4.36.2) (2023-09-12)


### Bug Fixes

* :boom: Panier expiré visible dans le retour API  GET / cart - mais erreur 404 sur un GET sur ce panier GET cart/ID  ([31a6516](https://github.com/uppler/uppler/commit/31a651693f1e732faf42a9250de0547af31a7c7d)), closes [#18262](https://github.com/uppler/uppler/issues/18262)

## [4.36.1](https://github.com/uppler/uppler/compare/v4.36.0...v4.36.1) (2023-09-11)


### Bug Fixes

* :boom: Connecteur produit - Message d'erreur "Syntax error"  ([edd7085](https://github.com/uppler/uppler/commit/edd708567b2abd3f81f63325416acf91f663db36)), closes [#18134](https://github.com/uppler/uppler/issues/18134)
* :boom: Erreur 500 exports Xslx reversements  ([6700010](https://github.com/uppler/uppler/commit/670001061f93b97ece5905718932a0ddbac59e93)), closes [#18237](https://github.com/uppler/uppler/issues/18237)

# [4.36.0](https://github.com/uppler/uppler/compare/v4.35.0...v4.36.0) (2023-09-07)


### Bug Fixes

* :boom: API BUYER - Expand taxes sur Order ne donne rien  ([a3bba95](https://github.com/uppler/uppler/commit/a3bba95c6177c46feaa14099d3c60e65ef097bb2)), closes [#18118](https://github.com/uppler/uppler/issues/18118)


### Features

* :star: [QAN26] Ajout d'un champ kbis2 mappé avec un document "autre" sur Lemonway  ([143779d](https://github.com/uppler/uppler/commit/143779da4c78ca2c69934094b781c2d1c4ca6cae)), closes [#18185](https://github.com/uppler/uppler/issues/18185)

# [4.35.0](https://github.com/uppler/uppler/compare/v4.34.0...v4.35.0) (2023-09-06)


### Bug Fixes

* :boom: API - Montant minimum de commande non pris en compte   ([47622c8](https://github.com/uppler/uppler/commit/47622c8884a82a430a6250766b4adf1281750f99)), closes [#18078](https://github.com/uppler/uppler/issues/18078)
* :boom: Clic sur master product en BO envoie sur le FO  ([b280458](https://github.com/uppler/uppler/commit/b280458e3bf2f00ad8963e15ca8948d14d006b25)), closes [#18149](https://github.com/uppler/uppler/issues/18149)
* :boom: Error 500 passage prêt à l'envoi en MO  ([ca37478](https://github.com/uppler/uppler/commit/ca374782d19b34591ede399377ba301b00846207)), closes [#18050](https://github.com/uppler/uppler/issues/18050) [#18060](https://github.com/uppler/uppler/issues/18060)


### Features

* ⭐ [UPL2] Erreur form données non sauvegardées  ([0566847](https://github.com/uppler/uppler/commit/0566847c71ef443159b1849dc920fe341be6154e)), closes [#16896](https://github.com/uppler/uppler/issues/16896)

# [4.34.0](https://github.com/uppler/uppler/compare/v4.33.0...v4.34.0) (2023-09-05)


### Bug Fixes

* :boom: Href lang incorrect sur shop   ([1bb0417](https://github.com/uppler/uppler/commit/1bb041710450af0f8e4331e5d71e5ca264820670)), closes [#18020](https://github.com/uppler/uppler/issues/18020)
* :boom: Operator OR filtre de recherche non fonctionnel  ([599111c](https://github.com/uppler/uppler/commit/599111cf4cf848352817863379aab3a59e4d2083)), closes [#18059](https://github.com/uppler/uppler/issues/18059)
* :boom: Pop up impossible à fermer et impossible de scroll la page   ([daaa78a](https://github.com/uppler/uppler/commit/daaa78ae8ed54c9509100acbffc5f2b9099b11f8)), closes [#18160](https://github.com/uppler/uppler/issues/18160)


### Features

* :star: [PA25] Widget offres en promotion  ([f326bf0](https://github.com/uppler/uppler/commit/f326bf09e4fadd38fee402c00f41f147e85ba1b3)), closes [#17332](https://github.com/uppler/uppler/issues/17332) [#17970](https://github.com/uppler/uppler/issues/17970)

# [4.33.0](https://github.com/uppler/uppler/compare/v4.32.1...v4.33.0) (2023-09-04)


### Features

* :star: [Design] - Ajout de la police NAVAL  ([9360cb1](https://github.com/uppler/uppler/commit/9360cb1ec1f5c2d73cfd3ab770fd16f2a58a1e55)), closes [#18085](https://github.com/uppler/uppler/issues/18085)
* :star: [PHA4] Prix remisé + prix total : Actualisation en temps réel sur la vue liste en FO ([7e4f34c](https://github.com/uppler/uppler/commit/7e4f34c8bd97c0572b7d72c986c3271fe97d8498)), closes [#16982](https://github.com/uppler/uppler/issues/16982) [#17646](https://github.com/uppler/uppler/issues/17646)
* :star: Optimisation de quelques éléments de sécurité  ([f0642df](https://github.com/uppler/uppler/commit/f0642df01e34934b0d82ff0c4f70101609705eed)), closes [#17762](https://github.com/uppler/uppler/issues/17762) [#18054](https://github.com/uppler/uppler/issues/18054)

## [4.32.1](https://github.com/uppler/uppler/compare/v4.32.0...v4.32.1) (2023-08-28)


### Bug Fixes

* :boom: API autocomplete sur fomulaire d'inscription  ([cb30963](https://github.com/uppler/uppler/commit/cb309635edc23632889c4627c95bdd44100b779c)), closes [#18156](https://github.com/uppler/uppler/issues/18156)
* :boom: Export réimportable des acheteurs ne fonctionne pas   ([0932850](https://github.com/uppler/uppler/commit/093285059747f213207b710a84e9b10c5f1b972e)), closes [#18153](https://github.com/uppler/uppler/issues/18153)
* :boom: nouveau produit pas dans le contrat   ([eff7177](https://github.com/uppler/uppler/commit/eff7177d0ac1d8abd0e0a1262542f0cf1c6b7f4c)), closes [#18125](https://github.com/uppler/uppler/issues/18125)
* :boom: Visibilité des fichiers par les sous-comptes acheteur  ([2b404c6](https://github.com/uppler/uppler/commit/2b404c6f94b2642c39b9003564ebef8aac1df754)), closes [#17856](https://github.com/uppler/uppler/issues/17856)

# [4.32.0](https://github.com/uppler/uppler/compare/v4.31.8...v4.32.0) (2023-08-24)


### Bug Fixes

* :boom: "Retour en arrière" confirme un paiement  ([50ec421](https://github.com/uppler/uppler/commit/50ec421c4f64a1ee925aeef2fe30e82a5ea7e027)), closes [#18143](https://github.com/uppler/uppler/issues/18143)
* :boom: Devis reste en attente de validation   ([f492ced](https://github.com/uppler/uppler/commit/f492cedb654b2a09000811010a9f22e24a00252a)), closes [#18140](https://github.com/uppler/uppler/issues/18140)


### Features

* :star: [CMX88] Affichage widget liste de produits  ([e3c0be3](https://github.com/uppler/uppler/commit/e3c0be339196bef8975cacf076a9e2f45e38e240)), closes [#17450](https://github.com/uppler/uppler/issues/17450)

## [4.31.8](https://github.com/uppler/uppler/compare/v4.31.7...v4.31.8) (2023-08-23)


### Bug Fixes

* :boom: Calcul TVA arrondis   ([83c47ac](https://github.com/uppler/uppler/commit/83c47ac59e1c8c79e91c1cec1753fe11dbc1003e)), closes [#18032](https://github.com/uppler/uppler/issues/18032)

## [4.31.7](https://github.com/uppler/uppler/compare/v4.31.6...v4.31.7) (2023-08-21)


### Bug Fixes

* :boom: Acheteur - Devise par défaut compte maître non repris par sous-compte  ([48c28cb](https://github.com/uppler/uppler/commit/48c28cbb376252d70800ccd51906a826d2ec512d)), closes [#18087](https://github.com/uppler/uppler/issues/18087)
* :boom: Erreur 500 catalogue PDF  ([02c49ce](https://github.com/uppler/uppler/commit/02c49ced6200a3a3de8cddeccc64c2cbbafeccfc)), closes [#17927](https://github.com/uppler/uppler/issues/17927) [#17940](https://github.com/uppler/uppler/issues/17940)
* :boom: Erreur 502 export invitations acheteurs  ([1071be4](https://github.com/uppler/uppler/commit/1071be4f247302324c2f9563b0b5287a44ba0f33)), closes [#18116](https://github.com/uppler/uppler/issues/18116)
* :boom: Erreur API "You do not have the necessary authorizations to consult this section"  ([eb96e6d](https://github.com/uppler/uppler/commit/eb96e6d6b22d05a7567a533f2ed3b198d595c73a)), closes [#18098](https://github.com/uppler/uppler/issues/18098)
* :boom: Message d'erreur du n° de téléphone non précis sur formulaire inscription vendeur  ([e8eb2b5](https://github.com/uppler/uppler/commit/e8eb2b59a51f9c2cc00025a1d7934370b0b17cb1)), closes [#18120](https://github.com/uppler/uppler/issues/18120)
* :boom: Promotion sur un autre produit acheté ne fonctionne pas  ([1dee952](https://github.com/uppler/uppler/commit/1dee952aa8f10290d8d12624254f9b84c27aabee)), closes [#18117](https://github.com/uppler/uppler/issues/18117)
* :boom: Visibilité acheteurs sur "ils ont visité votre catalogue"  ([e8a6ecb](https://github.com/uppler/uppler/commit/e8a6ecb81408f63c1d329b1a2e008001cc2592fd)), closes [#18109](https://github.com/uppler/uppler/issues/18109)

## [4.31.6](https://github.com/uppler/uppler/compare/v4.31.5...v4.31.6) (2023-08-17)


### Bug Fixes

* :boom: Création de 2 promotions avec le même token  ([cb95fee](https://github.com/uppler/uppler/commit/cb95fee71a1b0196add8f7093d9bda0c7987adc8)), closes [#17941](https://github.com/uppler/uppler/issues/17941)
* :boom: Document Facture / avoir Litige paiement hors plateforme  ([31a8d17](https://github.com/uppler/uppler/commit/31a8d17e9fe2a243cb7f7786fdcaf70302c57f5d)), closes [#17270](https://github.com/uppler/uppler/issues/17270)
* :boom: Export live Erreur 502 bad gateway  ([600c595](https://github.com/uppler/uppler/commit/600c59523105ebb8d3509d0bd83dbc430a189107)), closes [#18076](https://github.com/uppler/uppler/issues/18076)
* :boom: Produit noindex no follow par defaut suite  ([57edabf](https://github.com/uppler/uppler/commit/57edabfb70b3522176bff44d3e453230fe82a060)), closes [#17989](https://github.com/uppler/uppler/issues/17989)

## [4.31.5](https://github.com/uppler/uppler/compare/v4.31.4...v4.31.5) (2023-08-14)


### Bug Fixes

* :boom: Erreur 500 ajout produit promotion un autre produit  ([d3c5c50](https://github.com/uppler/uppler/commit/d3c5c50dffac56f551c6d094ede163c5bf58aa19)), closes [#18084](https://github.com/uppler/uppler/issues/18084)
* :boom: Erreur 500 de boucle infinie sur le formulaire des propriétés d'un produit en MO  ([ebe2fa5](https://github.com/uppler/uppler/commit/ebe2fa5d57b3e8d0964211168f7b1035b82f0850))
* :boom: FeedBack Upela  ([c29bbbd](https://github.com/uppler/uppler/commit/c29bbbd98d3458b601ce90dd574cc8e8610264ea)), closes [#18079](https://github.com/uppler/uppler/issues/18079)
* :boom: Page /relationship/invitation trop longue à charger  ([cada632](https://github.com/uppler/uppler/commit/cada632189946a875797d391c5ea47b89f22d1b8)), closes [#18029](https://github.com/uppler/uppler/issues/18029)

## [4.31.4](https://github.com/uppler/uppler/compare/v4.31.3...v4.31.4) (2023-08-10)


### Bug Fixes

* :boom: Anomalie sur les paiements et Upela  ([85f2aa5](https://github.com/uppler/uppler/commit/85f2aa5634ab77721bed0e767b52a94bf714e005)), closes [#18033](https://github.com/uppler/uppler/issues/18033)
* :boom: Export locked (SUITE)  ([9c0496d](https://github.com/uppler/uppler/commit/9c0496d1abf31f34969d7be47712ec3ab88e891c)), closes [#18031](https://github.com/uppler/uppler/issues/18031)
* :boom: Vendeur ne voit pas un appel d'offre  ([90576a4](https://github.com/uppler/uppler/commit/90576a41c1ed9a9b1a851393fba18c9c5d7c1d65)), closes [#17987](https://github.com/uppler/uppler/issues/17987)
* 💥 Affichage histogramme promo dégressivité   ([aa90001](https://github.com/uppler/uppler/commit/aa90001797249e1744d848ad573d0b1527ba1713)), closes [#17917](https://github.com/uppler/uppler/issues/17917)

## [4.31.3](https://github.com/uppler/uppler/compare/v4.31.2...v4.31.3) (2023-08-09)


### Bug Fixes

* :boom: Erreur 500 panier   ([2ec94b3](https://github.com/uppler/uppler/commit/2ec94b3dc307ed93d513bd6a6755023371476b9c)), closes [#18037](https://github.com/uppler/uppler/issues/18037)
* :boom: Permalink qui change quand on modifie la meta title et description   ([3d41f85](https://github.com/uppler/uppler/commit/3d41f8504c76ed740f2dcb3038a7cbf496be3ee7)), closes [#18035](https://github.com/uppler/uppler/issues/18035)

## [4.31.2](https://github.com/uppler/uppler/compare/v4.31.1...v4.31.2) (2023-08-08)


### Bug Fixes

* :boom: Formulaire de contact - champs commande en anonyme  ([1625393](https://github.com/uppler/uppler/commit/1625393cccc58256d4b479e8d07a6fb1aeb23f33)), closes [#17972](https://github.com/uppler/uppler/issues/17972)
* :boom: Noindex / nofollow sur URL changeLanguage  ([397563d](https://github.com/uppler/uppler/commit/397563de8ec4fc871a368bebf3aebec1ad0710a3)), closes [#18034](https://github.com/uppler/uppler/issues/18034)
* 💥 Email pas éditable   ([9fa8413](https://github.com/uppler/uppler/commit/9fa8413b94986afeb142dd9b15c70bf2aca8dac8)), closes [#18023](https://github.com/uppler/uppler/issues/18023)

## [4.31.1](https://github.com/uppler/uppler/compare/v4.31.0...v4.31.1) (2023-08-07)


### Bug Fixes

* :boom: Informations manquantes sur facture expeditions partielles  ([644bfb9](https://github.com/uppler/uppler/commit/644bfb9fd9ef2871587e224b99d61fbdfa9e3444)), closes [#17949](https://github.com/uppler/uppler/issues/17949)
* :boom: URL de redirection création compte UPELA dans le code  ([868cc54](https://github.com/uppler/uppler/commit/868cc54b1470d48b8aaaf6f5f606cda6475158c5)), closes [#18027](https://github.com/uppler/uppler/issues/18027)

# [4.31.0](https://github.com/uppler/uppler/compare/v4.30.0...v4.31.0) (2023-08-03)


### Bug Fixes

* :boom: Complétion champs : curseur se déplace tout seul  ([3329320](https://github.com/uppler/uppler/commit/3329320682fe4246ce3a80bf3eb5fe205a76aee5)), closes [#17991](https://github.com/uppler/uppler/issues/17991)
* :boom: Export locked  ([1af1590](https://github.com/uppler/uppler/commit/1af159069251e8f6f05a06c90a5a44c06001bdb2)), closes [#17975](https://github.com/uppler/uppler/issues/17975)
* :boom: facture d'abonnements à 0€  ([919729d](https://github.com/uppler/uppler/commit/919729d674ffeb340e0bf923049127bfab315df0)), closes [#17930](https://github.com/uppler/uppler/issues/17930)
* :boom: Import catégorie d'entreprise KO  ([7b9a9ed](https://github.com/uppler/uppler/commit/7b9a9edfc9e4e49f4aca23cb0d38ca501976408c)), closes [#17831](https://github.com/uppler/uppler/issues/17831)
* :boom: Import reste "en cours" depuis 2 jours  ([b24c480](https://github.com/uppler/uppler/commit/b24c480a841615e38f6a92e25024512b4c5600dc)), closes [#17967](https://github.com/uppler/uppler/issues/17967)


### Features

* :star: [OTE5] Upela V2  ([9f9a871](https://github.com/uppler/uppler/commit/9f9a8712595eb5b09da8defa4a97a06e702ea09b)), closes [#17353](https://github.com/uppler/uppler/issues/17353) [#17850](https://github.com/uppler/uppler/issues/17850)

# [4.30.0](https://github.com/uppler/uppler/compare/v4.29.3...v4.30.0) (2023-07-31)


### Bug Fixes

* :boom: Erreur 500 de type de donnée API création produit par opérateur  ([369d8a1](https://github.com/uppler/uppler/commit/369d8a1895b7201920699436af3f2087da98d96d)), closes [#17877](https://github.com/uppler/uppler/issues/17877)
* :boom: Export différé en pending   ([b9301a4](https://github.com/uppler/uppler/commit/b9301a48be6b1c47ba55ce1904b4602722942122)), closes [#17886](https://github.com/uppler/uppler/issues/17886)
* :boom: Message d'erreur KYC imprécis  ([c566812](https://github.com/uppler/uppler/commit/c566812763c29763c65cf2f5af17004d1c068a11)), closes [#17910](https://github.com/uppler/uppler/issues/17910)
* :boom: Produits en noindex no follow  ([fd9f49b](https://github.com/uppler/uppler/commit/fd9f49b928dba0bacdd7e2a140b1d43bb9e5fd23)), closes [#17906](https://github.com/uppler/uppler/issues/17906)
* :boom: RIB Opérateur visible sur commande   ([8af2aa0](https://github.com/uppler/uppler/commit/8af2aa0188abcf3b58eadddd4889890a6da60a6e)), closes [#17830](https://github.com/uppler/uppler/issues/17830)


### Features

* :star: Mise en avant des résultats recherche  ([af480c5](https://github.com/uppler/uppler/commit/af480c53650d23442c8883851762477ddc5a14ed)), closes [#17639](https://github.com/uppler/uppler/issues/17639) [#17651](https://github.com/uppler/uppler/issues/17651)

## [4.29.3](https://github.com/uppler/uppler/compare/v4.29.2...v4.29.3) (2023-07-27)


### Bug Fixes

* :boom: Impossible d'ajouter un fichier sur une commande par api  ([63cb8dc](https://github.com/uppler/uppler/commit/63cb8dc4f51c4d21aaa79334f77fba2d4bff307e)), closes [#17919](https://github.com/uppler/uppler/issues/17919)
* :boom: Tri offres indépendantes en BO  ([a7f60ea](https://github.com/uppler/uppler/commit/a7f60ea80f629eb8a840520f7a84abc93c25d6e7)), closes [#17884](https://github.com/uppler/uppler/issues/17884)

## [4.29.2](https://github.com/uppler/uppler/compare/v4.29.1...v4.29.2) (2023-07-26)


### Bug Fixes

* :boom: Erreur 500 édition d'un contrat  ([dbb6929](https://github.com/uppler/uppler/commit/dbb69295581c3045d0524109ab8e33f798d3a7a6)), closes [#17923](https://github.com/uppler/uppler/issues/17923)
* :boom: suppression fichiers depuis la liste  ([7fc295e](https://github.com/uppler/uppler/commit/7fc295eee1526b2b8a990e89617c5e2395010b25)), closes [#17857](https://github.com/uppler/uppler/issues/17857)

## [4.29.1](https://github.com/uppler/uppler/compare/v4.29.0...v4.29.1) (2023-07-25)


### Bug Fixes

* :boom: Adresse de livraison pas présente dans export reccurent   ([0b87c8b](https://github.com/uppler/uppler/commit/0b87c8bc4df367fd34e25dcf2c2f04f3a5e5ed64)), closes [#17898](https://github.com/uppler/uppler/issues/17898)
* :boom: Bannières : entreprises non-trouvable  ([f3a32b5](https://github.com/uppler/uppler/commit/f3a32b5cc8507cce91439288ce2e4cfbb591b49e)), closes [#17888](https://github.com/uppler/uppler/issues/17888)
* :boom: Erreur 500  ([d25f138](https://github.com/uppler/uppler/commit/d25f138526f3f46aa90895382e5760559d60acb0)), closes [#17893](https://github.com/uppler/uppler/issues/17893)
* :boom: Erreur 500 mise à jour d'une commande via API opérateur  ([6b3ce70](https://github.com/uppler/uppler/commit/6b3ce7028facddfe25fc0b06dfb5ed86798927d1)), closes [#17878](https://github.com/uppler/uppler/issues/17878)

# [4.29.0](https://github.com/uppler/uppler/compare/v4.28.0...v4.29.0) (2023-07-24)


### Bug Fixes

* :boom: Email de commentaire sur une commande ne s'envoie pas  ([3f26aa0](https://github.com/uppler/uppler/commit/3f26aa02a8440436603ba3d0b39b2dd8dc5619dd)), closes [#17810](https://github.com/uppler/uppler/issues/17810)
* :boom: Erreur 500 à l'exécution du cron de scoring des entreprises  ([ef8e5b1](https://github.com/uppler/uppler/commit/ef8e5b1c8c140f2ce684db33180d7bb3532ce266)), closes [#17881](https://github.com/uppler/uppler/issues/17881)
* :boom: Erreur 500 à l'exécution du cron uppler:tax:get-documents avec variante supprimée  ([d4d6000](https://github.com/uppler/uppler/commit/d4d6000ef8015b0a2a4e7f76b57e87679500a2c9)), closes [#17876](https://github.com/uppler/uppler/issues/17876)
* :boom: Erreur 500 duplicate entry lors de la sauvegarde des prix d'un produit en MO  ([506145a](https://github.com/uppler/uppler/commit/506145a285b6e004cd151f101ef9cff1ac3f02bb)), closes [#17731](https://github.com/uppler/uppler/issues/17731)
* :boom: Formulaire inscription Vendeur - Champ obligatoire du fichier DOWN  ([a3fe945](https://github.com/uppler/uppler/commit/a3fe945d4651167f665bc17e13039ff9352142ab)), closes [#17498](https://github.com/uppler/uppler/issues/17498)
* :boom: Impossible de télécharger les documents KYC depuis le BO   ([ceb0e1a](https://github.com/uppler/uppler/commit/ceb0e1a9fb73bc0d54e7a200db118e2617705015)), closes [#17897](https://github.com/uppler/uppler/issues/17897)
* :boom: Message d'erreur taxe lors de la validation du panier  ([e71d8ba](https://github.com/uppler/uppler/commit/e71d8ba486979e012ad324607a1d73d3504c5360)), closes [#17915](https://github.com/uppler/uppler/issues/17915)
* :boom: Produit d'un vendeur non visible sont visibles sur le shop   ([fab2e4a](https://github.com/uppler/uppler/commit/fab2e4a8c4716e68c234b9dd90b83edb22d67bea)), closes [#17791](https://github.com/uppler/uppler/issues/17791)
* :question: suppression d'un menu pas possible   ([c1f19ae](https://github.com/uppler/uppler/commit/c1f19aedd1e3798136935ca3ab49228ff3adbf0f)), closes [#17752](https://github.com/uppler/uppler/issues/17752)


### Features

* :star: [F21] Ajouter un document à un acheteur par API   ([322a168](https://github.com/uppler/uppler/commit/322a168b617f6c9da2ce62cf9e82268689f8e47e)), closes [#17665](https://github.com/uppler/uppler/issues/17665) [#17865](https://github.com/uppler/uppler/issues/17865)


### Reverts

* Revert "dont save variant if amount null" ([ac42c48](https://github.com/uppler/uppler/commit/ac42c48aa67a5392789d3a8f80b1861acb283334))

# [4.28.0](https://github.com/uppler/uppler/compare/v4.27.7...v4.28.0) (2023-07-18)


### Bug Fixes

* :boom: Erreur 500 à la validation de l'adresse de livraison du panier  ([644d3d8](https://github.com/uppler/uppler/commit/644d3d87f1aa4077f64a3d6d5e84d10f9a4c4926)), closes [#17844](https://github.com/uppler/uppler/issues/17844)
* :boom: Impossible de télécharger les documents KYC depuis le BO  ([ae7e7d5](https://github.com/uppler/uppler/commit/ae7e7d5a77c103cebba97fe8b385da716427637b)), closes [#17868](https://github.com/uppler/uppler/issues/17868)


### Features

* ⭐ [MAC24] nouveaux textes macro emails  ([c4d689c](https://github.com/uppler/uppler/commit/c4d689c49711497ebdc9ff089b8110ddc97cbcc6)), closes [#16970](https://github.com/uppler/uppler/issues/16970)

## [4.27.7](https://github.com/uppler/uppler/compare/v4.27.6...v4.27.7) (2023-07-18)


### Bug Fixes

* :boom: Rajouter class groupe d'accès  ([2caf5bf](https://github.com/uppler/uppler/commit/2caf5bfb50462018b50f51b8743227310bd679b7)), closes [#17640](https://github.com/uppler/uppler/issues/17640)

## [4.27.6](https://github.com/uppler/uppler/compare/v4.27.5...v4.27.6) (2023-07-17)


### Bug Fixes

* :boom: Redirection  de la bannière d'information sur les pattern   ([3595beb](https://github.com/uppler/uppler/commit/3595beb2356fc9d61a4f9598aa129d674b946206)), closes [#17815](https://github.com/uppler/uppler/issues/17815)
* 💥 Soucis de la gestion des devises proposition RFP  ([83b0ad0](https://github.com/uppler/uppler/commit/83b0ad0114b92bea65325d5838980e95285db029)), closes [#17495](https://github.com/uppler/uppler/issues/17495)

## [4.27.5](https://github.com/uppler/uppler/compare/v4.27.4...v4.27.5) (2023-07-13)


### Bug Fixes

* :boom: Recherche google contenu site sans CSS  ([f4a2625](https://github.com/uppler/uppler/commit/f4a26259aa67ca829359fd24da0ebf7ade0e0c67)), closes [#17689](https://github.com/uppler/uppler/issues/17689)

## [4.27.4](https://github.com/uppler/uppler/compare/v4.27.3...v4.27.4) (2023-07-12)


### Bug Fixes

* :boom: API - Transaction/confirm successif créé un nouveau paiement.  ([45f6b69](https://github.com/uppler/uppler/commit/45f6b69829f8551244e1e5ad5c3c083d4e51a95f)), closes [#17710](https://github.com/uppler/uppler/issues/17710)
* :boom: Erreur 500 import tarif livraison   ([920b572](https://github.com/uppler/uppler/commit/920b5727e6f041a2e42a2ad811da6780c8195b0e)), closes [#17744](https://github.com/uppler/uppler/issues/17744)

## [4.27.3](https://github.com/uppler/uppler/compare/v4.27.2...v4.27.3) (2023-07-11)


### Bug Fixes

* :boom: Champs dynamiques plus présents sur les utilisateurs   ([1bf5c0d](https://github.com/uppler/uppler/commit/1bf5c0d55557200a21175b4ab54459aba6b0028c)), closes [#17766](https://github.com/uppler/uppler/issues/17766)
* :boom: Erreur 500 à la persistance des données d'import produit  ([282b190](https://github.com/uppler/uppler/commit/282b190a86406ea0428c87f3a1d7dc265dbf0798)), closes [#17741](https://github.com/uppler/uppler/issues/17741)
* :boom: Erreur 500 à la suppression d'un type d'entreprise en BO  ([a021c68](https://github.com/uppler/uppler/commit/a021c685398d2a10e70d3853c4e66423f12716a4)), closes [#17735](https://github.com/uppler/uppler/issues/17735)
* :boom: Erreur 500 non gérée à la connexion SSO sur proachat  ([248912f](https://github.com/uppler/uppler/commit/248912f0544eac08ebdafe868d486b80c00dc04e)), closes [#17730](https://github.com/uppler/uppler/issues/17730)
* :boom: Import produit avec ref > 50 caractères  ([cae7674](https://github.com/uppler/uppler/commit/cae76749895d3d19f9f6e2ac8a00dff848016f3c)), closes [#17722](https://github.com/uppler/uppler/issues/17722)
* :boom: Reprise des produits mal identifiés   ([684abf0](https://github.com/uppler/uppler/commit/684abf020fd956019b6ba8123a279e36f610f089)), closes [#17733](https://github.com/uppler/uppler/issues/17733)
* :boom: Sélecteur quantité 50+ devient 1   ([c275816](https://github.com/uppler/uppler/commit/c275816425cc895f3a5e984c02b10ebd5f813fc4)), closes [#17789](https://github.com/uppler/uppler/issues/17789)

## [4.27.2](https://github.com/uppler/uppler/compare/v4.27.1...v4.27.2) (2023-07-06)


### Bug Fixes

* :boom: Erreur 500 widget catégories  ([fd4eac0](https://github.com/uppler/uppler/commit/fd4eac0b5f7db9ecffdf6826dea9eaf157b9e7ab)), closes [#17683](https://github.com/uppler/uppler/issues/17683)
* :boom: Pas possible de récupérer les champs de type "entreprise" par API  ([a56028e](https://github.com/uppler/uppler/commit/a56028ec6ce4e13e3b82a324f8fcc7e6dedf82fe)), closes [#17559](https://github.com/uppler/uppler/issues/17559)

## [4.27.1](https://github.com/uppler/uppler/compare/v4.27.0...v4.27.1) (2023-07-05)


### Bug Fixes

* :boom: Catégorie d'entreprise ne sauvegarde pas les modifications   ([486ae01](https://github.com/uppler/uppler/commit/486ae01dcbd308dea6e543207dbb4a10a4f5c7ce)), closes [#17614](https://github.com/uppler/uppler/issues/17614)
* :boom: Mail de rétention ne respecte pas sa configuration 2  ([8093129](https://github.com/uppler/uppler/commit/809312996cd19a3208aad7e6fdb44a63caebd37c)), closes [#17690](https://github.com/uppler/uppler/issues/17690)
* :boom: Titre de filtre qui disparait  ([1842eca](https://github.com/uppler/uppler/commit/1842eca22acef7a1e1f3538065fed34368193a97)), closes [#17310](https://github.com/uppler/uppler/issues/17310) [#17699](https://github.com/uppler/uppler/issues/17699)
* 💥  Amélioration des slug catégorie de produit   ([053414b](https://github.com/uppler/uppler/commit/053414b939f8b0f64f2766a77fe6ad55138d3587)), closes [#17427](https://github.com/uppler/uppler/issues/17427)

# [4.27.0](https://github.com/uppler/uppler/compare/v4.26.6...v4.27.0) (2023-07-05)


### Bug Fixes

* :boom:  Erreur 500 messagerie BO   ([eaf943f](https://github.com/uppler/uppler/commit/eaf943fb0be99c6b8bbb1f09bb0bf98413d9a87d)), closes [#17601](https://github.com/uppler/uppler/issues/17601)
* :boom: Export produit données pas dispo  ([df77fe7](https://github.com/uppler/uppler/commit/df77fe7ca56350b354eb9afea88edb487d6e05af)), closes [#17695](https://github.com/uppler/uppler/issues/17695)


### Features

* :star: [F19] affichage des documents  ([20faa6c](https://github.com/uppler/uppler/commit/20faa6cc561dd426e7afc3ab1c70a8f010acf59e)), closes [#16892](https://github.com/uppler/uppler/issues/16892)
* :star: [PA20] Numéro associé au panier obligatoire  ([41aadb0](https://github.com/uppler/uppler/commit/41aadb0e16044593371bf7bc54fd85b4bd6a9e2a)), closes [#16953](https://github.com/uppler/uppler/issues/16953)

## [4.26.6](https://github.com/uppler/uppler/compare/v4.26.5...v4.26.6) (2023-07-03)


### Bug Fixes

* 💥 Affichage des prix multi-devise fiche produit ([8d57762](https://github.com/uppler/uppler/commit/8d5776279c17de794f0edc7d9f9fe56fd30fedc5)), closes [#16882](https://github.com/uppler/uppler/issues/16882)

## [4.26.5](https://github.com/uppler/uppler/compare/v4.26.4...v4.26.5) (2023-06-30)


### Bug Fixes

* :boom:  Filtre impossible à retirer   ([6a21a20](https://github.com/uppler/uppler/commit/6a21a20c8c4b346291610544981dca39beaa6afe)), closes [#17489](https://github.com/uppler/uppler/issues/17489)
* :boom: Erreur 500  Modification des heures d'ouverture  ([7b03fad](https://github.com/uppler/uppler/commit/7b03fad0a619b5e840208b2e9a0c343d54de4121)), closes [#17625](https://github.com/uppler/uppler/issues/17625)
* :boom: Erreur 500 au moment de correction de l'import en BO   ([37be986](https://github.com/uppler/uppler/commit/37be98639e5e0214d46821110753d5fd94817cc3)), closes [#17674](https://github.com/uppler/uppler/issues/17674)
* :boom: Offre rattachés à un master product non visible  ([6b40602](https://github.com/uppler/uppler/commit/6b406024022b574b35bc5b480a671f8552a92a21)), closes [#17671](https://github.com/uppler/uppler/issues/17671)
* :boom: Statut panier anormal   ([50e599e](https://github.com/uppler/uppler/commit/50e599ef096e7223188614d34f7eb4d064d626b8)), closes [#17623](https://github.com/uppler/uppler/issues/17623)

## [4.26.4](https://github.com/uppler/uppler/compare/v4.26.3...v4.26.4) (2023-06-29)


### Bug Fixes

* :boom: produits visible non accessible  ([c6ce7a4](https://github.com/uppler/uppler/commit/c6ce7a4e0e1994bf4c8b474de3f9d5321b2541fd)), closes [#17502](https://github.com/uppler/uppler/issues/17502)
* :boom: Requêtes Transiteo Anormales  ([7b3b899](https://github.com/uppler/uppler/commit/7b3b8990a3970256877637c7905d4c9431997ad1)), closes [#17693](https://github.com/uppler/uppler/issues/17693)

## [4.26.3](https://github.com/uppler/uppler/compare/v4.26.2...v4.26.3) (2023-06-28)


### Bug Fixes

* :boom: Erreur 500 édition de la config en BO  ([77cb814](https://github.com/uppler/uppler/commit/77cb814b658ffbf179415e0756dc98898f7eca57)), closes [#17666](https://github.com/uppler/uppler/issues/17666)

## [4.26.2](https://github.com/uppler/uppler/compare/v4.26.1...v4.26.2) (2023-06-27)


### Bug Fixes

* :boom:  Erreur 500 selection méthode de paiement  ([05d4d55](https://github.com/uppler/uppler/commit/05d4d551290dc338f507419c266bf046bb6ca63a)), closes [#17667](https://github.com/uppler/uppler/issues/17667)
* :boom: export panier > logo en trop  ([7f7c5ad](https://github.com/uppler/uppler/commit/7f7c5ad12d2e98edd02d0a2ad15495dd6b5ca18e)), closes [#17106](https://github.com/uppler/uppler/issues/17106)

## [4.26.1](https://github.com/uppler/uppler/compare/v4.26.0...v4.26.1) (2023-06-27)


### Bug Fixes

* :boom: Impossibilité de poster un commentaire récap panier   ([d5ecc5d](https://github.com/uppler/uppler/commit/d5ecc5dfc378514b44a3f129a0d276120439c541)), closes [#17586](https://github.com/uppler/uppler/issues/17586)

# [4.26.0](https://github.com/uppler/uppler/compare/v4.25.2...v4.26.0) (2023-06-26)


### Bug Fixes

* :boom: Changement comportement moteur de recherche sur les fautes d'orthographes  ([21dfea3](https://github.com/uppler/uppler/commit/21dfea3b5b604c9f012c928e3aae755afd314e75)), closes [#17483](https://github.com/uppler/uppler/issues/17483)
* :boom: Import de Variantes en Echec  ([f5a8193](https://github.com/uppler/uppler/commit/f5a819338050836352ba17ca7ac947f4596f6b2a)), closes [#17496](https://github.com/uppler/uppler/issues/17496)
* :boom: Sauvegarde impossible feuille de style personnalisé "emoji ne sont pas acceptés  ([427d7c0](https://github.com/uppler/uppler/commit/427d7c0930dd818d307feecb08c2435ee4fbcedd)), closes [#17632](https://github.com/uppler/uppler/issues/17632)


### Features

* :star: Séparation d'email de facture d'abonnements/ email de facture de commande  ([d1f37b2](https://github.com/uppler/uppler/commit/d1f37b28a4a6506951bec4e2c2ee117aa3ef20ff)), closes [#17501](https://github.com/uppler/uppler/issues/17501)

## [4.25.2](https://github.com/uppler/uppler/compare/v4.25.1...v4.25.2) (2023-06-26)


### Bug Fixes

* :boom: Lenteur prod  ([563cb7c](https://github.com/uppler/uppler/commit/563cb7c7277c4c66d5d9a41513c681d47149207d)), closes [#17352](https://github.com/uppler/uppler/issues/17352)

## [4.25.1](https://github.com/uppler/uppler/compare/v4.25.0...v4.25.1) (2023-06-22)


### Bug Fixes

* :boom: Ajout d'un échantillon au panier = panier reste à 0  ([bd8c10f](https://github.com/uppler/uppler/commit/bd8c10f0af257400bc3a6ec485aaf6352d850111)), closes [#17566](https://github.com/uppler/uppler/issues/17566)
* :boom: Fichier visible en MO mais pas en BO   ([8565fa4](https://github.com/uppler/uppler/commit/8565fa4de487b556f39ba14771bfc89634b0aa7e)), closes [#17436](https://github.com/uppler/uppler/issues/17436)

# [4.25.0](https://github.com/uppler/uppler/compare/v4.24.0...v4.25.0) (2023-06-21)


### Bug Fixes

* :boom:  Recherche valeur de propriété blanc   ([1ba695a](https://github.com/uppler/uppler/commit/1ba695ac2ec7b6e07f4d90665b9b8ad84e3b0035)), closes [#17469](https://github.com/uppler/uppler/issues/17469)
* :boom: Compte admin accès au dashboard sans les droits   ([a6bc088](https://github.com/uppler/uppler/commit/a6bc088acb1522a3a45218f638080f529d193ac8)), closes [#17250](https://github.com/uppler/uppler/issues/17250)
* :boom: Config mail URL sur un bouton   ([20a1f8e](https://github.com/uppler/uppler/commit/20a1f8e53c1e37e5a251edcf328489b5eaa96634)), closes [#17539](https://github.com/uppler/uppler/issues/17539)
* :boom: Erreur 500 au lieu d'un message d'erreur sur profil vendeur (à cause d'émoticones dans la description)  ([a5b4033](https://github.com/uppler/uppler/commit/a5b403382d9534d1148e3b354397e438e68003ff)), closes [#17568](https://github.com/uppler/uppler/issues/17568)
* :boom: impossible d'ajouter au panier   ([4b1079e](https://github.com/uppler/uppler/commit/4b1079e5ec820d0e8aa7a3c0b66b26c2be98a767)), closes [#17571](https://github.com/uppler/uppler/issues/17571)
* :boom: incohérence matching BO methode de paiement  ([eb25e2f](https://github.com/uppler/uppler/commit/eb25e2f198aebc72c00896c088f3c22614af4df6)), closes [#17191](https://github.com/uppler/uppler/issues/17191)
* :boom: Mauvaise méthode de paiement attribuée par défaut dans le panier en FO ([fec4c0b](https://github.com/uppler/uppler/commit/fec4c0ba6acf3104e2487b02bac731c6f2a91565)), closes [#17061](https://github.com/uppler/uppler/issues/17061) [#17505](https://github.com/uppler/uppler/issues/17505) [#17505](https://github.com/uppler/uppler/issues/17505)
* :boom: Réindexation produit lors du rattachement à un master product  ([fd48250](https://github.com/uppler/uppler/commit/fd4825083988b719773b5bdbc4233d224887fbd1)), closes [#17102](https://github.com/uppler/uppler/issues/17102)
* :boom: Visibilité page API acheteur  ([d9992ef](https://github.com/uppler/uppler/commit/d9992ef74b1cd86b69ffff91ddeb815d69efde85)), closes [#17334](https://github.com/uppler/uppler/issues/17334)
* [BUG] :boom: Mail de rétention ne respecte pas sa configuration   ([dbc0d92](https://github.com/uppler/uppler/commit/dbc0d926eac17bc7b145a31788f91d4f267d7695)), closes [#17170](https://github.com/uppler/uppler/issues/17170)


### Features

* :star: [QAN20] Commandes / Factures - Ajout de routes buyer API  ([0d19d6e](https://github.com/uppler/uppler/commit/0d19d6e943ac282e14cc25ce6d44c64e8322736a)), closes [#17316](https://github.com/uppler/uppler/issues/17316)
* ⭐ [PHA3] Quantités prédéfinies produits catalogue  ([361b590](https://github.com/uppler/uppler/commit/361b59072a89041d8d63192ca1f0316e41e3960f)), closes [#16859](https://github.com/uppler/uppler/issues/16859) [#17480](https://github.com/uppler/uppler/issues/17480)

# [4.24.0](https://github.com/uppler/uppler/compare/v4.23.3...v4.24.0) (2023-06-19)


### Features

* ⭐ [OZ60] Ajout frais de livraison connecteur devis  ([65cc316](https://github.com/uppler/uppler/commit/65cc316502413a11f088c357ea7e822fd4664fe7)), closes [#16850](https://github.com/uppler/uppler/issues/16850)

## [4.23.3](https://github.com/uppler/uppler/compare/v4.23.2...v4.23.3) (2023-06-15)


### Bug Fixes

* :boom:  Export ressort les produits d'un autre vendeur   ([12ab246](https://github.com/uppler/uppler/commit/12ab246ab993a0e2145c743f3fee09d514cad7cd)), closes [#17484](https://github.com/uppler/uppler/issues/17484)
* :boom: Erreur 500 API PATCH /v1/administrator/sub-account/{id}  ([644b740](https://github.com/uppler/uppler/commit/644b74013ac52b47ce6d156e20781861c63d03b7)), closes [#17541](https://github.com/uppler/uppler/issues/17541)
* :boom: Erreur 500 MO  ([80f2209](https://github.com/uppler/uppler/commit/80f22092ee262b82599a4be4aec4a127570e3abc)), closes [#17504](https://github.com/uppler/uppler/issues/17504)
* :boom: Erreur texte message d'erreur montant minimum conversion ([2d627fd](https://github.com/uppler/uppler/commit/2d627fdbe2f448f57962d3261eb92f5e4d3233ba)), closes [#17342](https://github.com/uppler/uppler/issues/17342)

## [4.23.2](https://github.com/uppler/uppler/compare/v4.23.1...v4.23.2) (2023-06-13)


### Bug Fixes

* :boom: Accès rapide wording cassé ([44ba5c0](https://github.com/uppler/uppler/commit/44ba5c0cadcefbd444eeb95339cbb1b6f27974c1)), closes [#17457](https://github.com/uppler/uppler/issues/17457)
* :boom: Erreur 500 édition d'un widget ! ([d9e5a45](https://github.com/uppler/uppler/commit/d9e5a451db8d9d35a7b256c9956a63904126dc36)), closes [#17440](https://github.com/uppler/uppler/issues/17440)
* :boom: Widget vide au clic ([45ef1a1](https://github.com/uppler/uppler/commit/45ef1a10340a9ced48c17e253d1cfeec979a2686)), closes [#17280](https://github.com/uppler/uppler/issues/17280)
* [BUG] :boom: N de tva ne remonte pas sur la facture et en BO   ([094e8a6](https://github.com/uppler/uppler/commit/094e8a6aba6418f28de685c467baa473dabaf66b)), closes [#17384](https://github.com/uppler/uppler/issues/17384)
* [BUG] :boom: numéro d'engagement pas présent sur les commandes partiellements expédiée  ([a64371c](https://github.com/uppler/uppler/commit/a64371cc72d57b314c3eadb8599aafea94964165)), closes [#17453](https://github.com/uppler/uppler/issues/17453)
* [BUG] :boom: Tri BO KO   ([b4057d1](https://github.com/uppler/uppler/commit/b4057d188790886561ede3792a5be01aff363724)), closes [#17412](https://github.com/uppler/uppler/issues/17412)
* [BUG] :boom:Vulnérabilité Open redirect  ([d8d73c1](https://github.com/uppler/uppler/commit/d8d73c13070595e72cf039f0777c6be9c37d9c05)), closes [#17387](https://github.com/uppler/uppler/issues/17387)

## [4.23.1](https://github.com/uppler/uppler/compare/v4.23.0...v4.23.1) (2023-06-08)


### Bug Fixes

* :boom: Contrat spécifique - Validation acheteur - Erreur 500  ([6259856](https://github.com/uppler/uppler/commit/62598564797cac87557ca766f5a5a08fd2ccd43f)), closes [#17275](https://github.com/uppler/uppler/issues/17275)
* [BUG] :boom: Errreur 500 import produit ! ([c7d05a6](https://github.com/uppler/uppler/commit/c7d05a603c068eef610b671c7c6208190f36fc1a)), closes [#17372](https://github.com/uppler/uppler/issues/17372)
* [BUG] :boom: Meta description et title en double sur page company ! ([4406860](https://github.com/uppler/uppler/commit/440686007a4e24bbb13bc9f6067aae02e676afd2)), closes [#17413](https://github.com/uppler/uppler/issues/17413)
* 💥 Mauvais message d'erreur lors de l'ajout au panier d'un produit sans stock  ([8fec91d](https://github.com/uppler/uppler/commit/8fec91d7e5e03583b8adb5d5a89ad350e0730147)), closes [#16684](https://github.com/uppler/uppler/issues/16684)

# [4.23.0](https://github.com/uppler/uppler/compare/v4.22.1...v4.23.0) (2023-06-06)


### Bug Fixes

*  Champ RCS se place en dessous du bouton d'accès au compte   ([07d6efb](https://github.com/uppler/uppler/commit/07d6efb8985bd1a1e7271f9147709ea7c8937748)), closes [#17345](https://github.com/uppler/uppler/issues/17345)
*  Champs obligatoire de type file sans astérisques  ([2ea9b33](https://github.com/uppler/uppler/commit/2ea9b3306dc0fb2010454aeed1d61334ee649784)), closes [#17368](https://github.com/uppler/uppler/issues/17368)
*  Erreur 500 meta description BO  ([f513180](https://github.com/uppler/uppler/commit/f513180b3fdbaf4845190a8e735b998fba8c0f5e)), closes [#17338](https://github.com/uppler/uppler/issues/17338)
* :boom: Erreur 500 duplication méthode de livraison MO  ([8cd1d03](https://github.com/uppler/uppler/commit/8cd1d03607ebf0f9f92edeaaf6ccf621caa7bc40)), closes [#17284](https://github.com/uppler/uppler/issues/17284)
* :boom: Erreur 500 fiche produit avec un vendeur !!!!!!!! ([37a4329](https://github.com/uppler/uppler/commit/37a4329f87c51d83eedb366ea9b8e2c1b2d54d4a)), closes [#17445](https://github.com/uppler/uppler/issues/17445)
* :boom: Erreur 500 lors du filtre par statut de litige ! ([e9dbb65](https://github.com/uppler/uppler/commit/e9dbb658a3d5d1c984ed84c85239537e2959e9f2)), closes [#17357](https://github.com/uppler/uppler/issues/17357)
* :boom: Erreur 500 lors du téléchargement d'un PDF  ([1cfdce6](https://github.com/uppler/uppler/commit/1cfdce6934a78aff76216e16a04d5c7d1bc10da5)), closes [#17263](https://github.com/uppler/uppler/issues/17263)
* :boom: Pas de bouton de sortie du comparateur  ([8b22ecf](https://github.com/uppler/uppler/commit/8b22ecfdaa5d68be915a632fb8fd7ba960d90149)), closes [#17324](https://github.com/uppler/uppler/issues/17324)
* :boom: Produit ne devrait pas s'ajouter à un devis  ([9c36765](https://github.com/uppler/uppler/commit/9c36765226a3d785e5438a1eb6883527a6a043bb)), closes [#17271](https://github.com/uppler/uppler/issues/17271)
* :boom: Proposition vendeur appel d'offre - Retirer le symbole de la devise  ([4063193](https://github.com/uppler/uppler/commit/40631934e70c9817947edd9f2a6e4600824a15f1)), closes [#17268](https://github.com/uppler/uppler/issues/17268)
* :boom: Stats vendeurs KO  ([bcd72df](https://github.com/uppler/uppler/commit/bcd72df4db72cc355fa1ca74617bf4eff6613b62)), closes [#17114](https://github.com/uppler/uppler/issues/17114)
* 💥 Email de facture disponible s'envoie alors qu'il n'y a aucun trigger sur la méthode de paiement  ([e5bb35d](https://github.com/uppler/uppler/commit/e5bb35d79c61ff20deff6780ff236996d013422a)), closes [#16975](https://github.com/uppler/uppler/issues/16975)
* 💥 Erreur 500 annulation de commande  ([395117b](https://github.com/uppler/uppler/commit/395117b94f69d0d91a807091e80195a81593ad5a)), closes [#17145](https://github.com/uppler/uppler/issues/17145)


### Features

* :star: Ajout d'une route API GET:/buyer/product/   ([e0951ff](https://github.com/uppler/uppler/commit/e0951ffb6809f07417b8abc446ae25b0197c1f88)), closes [#17351](https://github.com/uppler/uppler/issues/17351)
* :star: API - préciser les champs à récupérer  ([5adfded](https://github.com/uppler/uppler/commit/5adfded5b80c1d25e690717c26690d4f61ba58de)), closes [#17344](https://github.com/uppler/uppler/issues/17344)

## [4.22.1](https://github.com/uppler/uppler/compare/v4.22.0...v4.22.1) (2023-05-23)


### Bug Fixes

* 💥 Mention "order refund" sur PDF de facture d'avoir en Français  ([3836fcf](https://github.com/uppler/uppler/commit/3836fcfd1e6585741c9a65b31c379505b451173e)), closes [#17076](https://github.com/uppler/uppler/issues/17076)

# [4.22.0](https://github.com/uppler/uppler/compare/v4.21.0...v4.22.0) (2023-05-22)


### Bug Fixes

*  Statut du paiement en cours après annulation du paiement ([b1022ac](https://github.com/uppler/uppler/commit/b1022acaca8cdb19a966622ebb65e5a1f14534a9)), closes [#17331](https://github.com/uppler/uppler/issues/17331)


### Features

* :star: New internal api route to retrieve deleted products  ([8f59964](https://github.com/uppler/uppler/commit/8f59964e11e88c0c856f89e8290cf441e79caf18)), closes [#17277](https://github.com/uppler/uppler/issues/17277)
* ⭐ [ETI4] Mise en place de taux de conversions via Fixer  ([f443b32](https://github.com/uppler/uppler/commit/f443b320817736cdcab0f34066b38e18bd733c16)), closes [#16866](https://github.com/uppler/uppler/issues/16866)

# [4.21.0](https://github.com/uppler/uppler/compare/v4.20.2...v4.21.0) (2023-05-16)


### Bug Fixes

* :boom: API - /v1/page/{id} - Erreur 500  ([03c4236](https://github.com/uppler/uppler/commit/03c4236e192552710fc4b16556090ef74ce742e5)), closes [#17238](https://github.com/uppler/uppler/issues/17238)
* 💥 Devise pas présent dans le message d'erreur minimum de commande  ([95ec3db](https://github.com/uppler/uppler/commit/95ec3db6133766f23826155247d4679a7cc4beb9)), closes [#17179](https://github.com/uppler/uppler/issues/17179)


### Features

* :star: [QAN17] Mise à jour contenu facture.  ([7438a65](https://github.com/uppler/uppler/commit/7438a658793dc9eb03bc9038d92e90343af9be2f)), closes [#17031](https://github.com/uppler/uppler/issues/17031) [#17216](https://github.com/uppler/uppler/issues/17216)

## [4.20.2](https://github.com/uppler/uppler/compare/v4.20.1...v4.20.2) (2023-05-15)


### Bug Fixes

* :boom: Erreur 500 lors d'un import entreprise  ([f37c68e](https://github.com/uppler/uppler/commit/f37c68e651b0a4aa4ef7dc0a9c6fce9d04b17cb2)), closes [#17150](https://github.com/uppler/uppler/issues/17150)
* :boom: Filtre limit_per_page incohérent  ([324bfbe](https://github.com/uppler/uppler/commit/324bfbefcfdd931beeb32fdcb8ee77a6a2054ee0)), closes [#17057](https://github.com/uppler/uppler/issues/17057)
* :boom: Filtre propriété OR ko  ([980d0b7](https://github.com/uppler/uppler/commit/980d0b7c216e2b8d043b4b200d1bb341d69b37ed)), closes [#17054](https://github.com/uppler/uppler/issues/17054)
* :boom: Fix acceptance tests sessions 🧪   ([5cd591d](https://github.com/uppler/uppler/commit/5cd591d0554e622b423758403e1428408192527d)), closes [#17189](https://github.com/uppler/uppler/issues/17189)
* :boom: impossible d'ajouter un fichier à une commande   ([7464263](https://github.com/uppler/uppler/commit/746426394df58038b50408d767edb7fcf8b236d9)), closes [#17213](https://github.com/uppler/uppler/issues/17213) [#17286](https://github.com/uppler/uppler/issues/17286)
* 💥 mauvais statut paiement "échoué" alors que le paiement est bien recu  ([c522356](https://github.com/uppler/uppler/commit/c5223567d5b5a32b97dcea9fc9628b28d8f8dfd7)), closes [#17109](https://github.com/uppler/uppler/issues/17109)

## [4.20.1](https://github.com/uppler/uppler/compare/v4.20.0...v4.20.1) (2023-05-09)


### Bug Fixes

* :boom: Mauvaise url dans le Href lang  ([4de5fc3](https://github.com/uppler/uppler/commit/4de5fc310087e73bd3f452ae88c251f5cb8806c7)), closes [#17017](https://github.com/uppler/uppler/issues/17017)

# [4.20.0](https://github.com/uppler/uppler/compare/v4.19.13...v4.20.0) (2023-05-04)


### Features

* :star: Ajout de critère sur la route indexed-product  ([8394f68](https://github.com/uppler/uppler/commit/8394f68b77e24b16daf1710905f89c0a78925e8c)), closes [#17141](https://github.com/uppler/uppler/issues/17141)

## [4.19.13](https://github.com/uppler/uppler/compare/v4.19.12...v4.19.13) (2023-05-04)


### Bug Fixes

* :boom: erreur 500 bon de commande  ([edaaa19](https://github.com/uppler/uppler/commit/edaaa19a7c1b4c6c7fbd739805fca23e27f2ed8b)), closes [#17164](https://github.com/uppler/uppler/issues/17164)
* 💥 Sous-comptes > droits liés aux imports promo KO ([f5207f0](https://github.com/uppler/uppler/commit/f5207f068d872ab8a9852ed9cfbc133f302c55e8)), closes [#16885](https://github.com/uppler/uppler/issues/16885)

## [4.19.12](https://github.com/uppler/uppler/compare/v4.19.11...v4.19.12) (2023-05-02)


### Bug Fixes

* :boom: Exports Locked   ([41269de](https://github.com/uppler/uppler/commit/41269de39120b7b9d6a7f2881d08ccc6c344605f)), closes [#17047](https://github.com/uppler/uppler/issues/17047)

## [4.19.11](https://github.com/uppler/uppler/compare/v4.19.10...v4.19.11) (2023-04-28)


### Bug Fixes

*  Erreur 500 Consultation facture d'abonnement   ([4c2a245](https://github.com/uppler/uppler/commit/4c2a245c758796807b22c17f5e4528b72fba31d6)), closes [#17167](https://github.com/uppler/uppler/issues/17167)
* :boom: GET cart/shipping-method KO  ([2eae8de](https://github.com/uppler/uppler/commit/2eae8de4495e5432b8a26a43e3e3dc7b7ef3a356)), closes [#17198](https://github.com/uppler/uppler/issues/17198)
* :boom: Nom accès rapide   ([711ec6a](https://github.com/uppler/uppler/commit/711ec6ab5b3569ba61eff2b267994e1a3d5748d3)), closes [#17066](https://github.com/uppler/uppler/issues/17066)
* :boom: uppler.import.product.cannot_create_option  ([9f10356](https://github.com/uppler/uppler/commit/9f103567720002e0744df36b6db704681d1ac324)), closes [#17069](https://github.com/uppler/uppler/issues/17069)
* 💥 Message d'erreur coupon KO ([bf5abd6](https://github.com/uppler/uppler/commit/bf5abd61e7e3afc02438376992c48dcd24032d49)), closes [#16884](https://github.com/uppler/uppler/issues/16884)

## [4.19.10](https://github.com/uppler/uppler/compare/v4.19.9...v4.19.10) (2023-04-27)


### Bug Fixes

* 💥 API: Suppression produit panier > erreur affichée  ([dd64352](https://github.com/uppler/uppler/commit/dd64352bb2c1cf98cc06fe0a2c98ddc94302966f)), closes [#17135](https://github.com/uppler/uppler/issues/17135)

## [4.19.9](https://github.com/uppler/uppler/compare/v4.19.8...v4.19.9) (2023-04-27)


### Bug Fixes

* :boom: API buyer sur panier confirmé anormal  ([4dba3ca](https://github.com/uppler/uppler/commit/4dba3cafeaa9ffe1e2ed402fce6ced6bcb85d5c4)), closes [#17115](https://github.com/uppler/uppler/issues/17115)
* :boom: Corrections des bugs remontés dans  ([61eba3f](https://github.com/uppler/uppler/commit/61eba3f1b3e1285f804f82cab5f33cf6c1d03cbc)), closes [#17116](https://github.com/uppler/uppler/issues/17116)
* :boom: Paiement CB - Confirmation commande vendeur - Erreur 500  ([af6eed3](https://github.com/uppler/uppler/commit/af6eed35356764e186225737fd6e19ba09746946)), closes [#17028](https://github.com/uppler/uppler/issues/17028)
* [BUG] 💥 Erreur 500 modification nom fichier ([f6f0bef](https://github.com/uppler/uppler/commit/f6f0bef36d80022e8484d6d7f381251269e2c0da)), closes [#16949](https://github.com/uppler/uppler/issues/16949)
* 💥  Export différé remonte les paniers  ([79f9084](https://github.com/uppler/uppler/commit/79f908493c50c47a4afcf3b3aae522831126cba1)), closes [#16947](https://github.com/uppler/uppler/issues/16947)
* 💥 Erreur 500 quand on souhaite effacer le cache d'une page  ([2fc2b95](https://github.com/uppler/uppler/commit/2fc2b95208103e743ff1799e3af8b7cbb5d7d701)), closes [#17095](https://github.com/uppler/uppler/issues/17095)

## [4.19.8](https://github.com/uppler/uppler/compare/v4.19.7...v4.19.8) (2023-04-25)


### Bug Fixes

* :boom: La version n'est pas à jour  ([5f6316c](https://github.com/uppler/uppler/commit/5f6316c5eff6c6c4211f6a6c4e9f6faba04bb2d5)), closes [#17134](https://github.com/uppler/uppler/issues/17134)

## [4.19.7](https://github.com/uppler/uppler/compare/v4.19.6...v4.19.7) (2023-04-21)


### Bug Fixes

* :boom: Erreur 500 lors du changement de la configuration de paiement  ([28c8fa3](https://github.com/uppler/uppler/commit/28c8fa3adefd545da729210e415e5e37eb96c3ce)), closes [#17101](https://github.com/uppler/uppler/issues/17101)
* :boom: Vendeur non visible > catalogue visible en FO ([2bc1421](https://github.com/uppler/uppler/commit/2bc1421e421dd9789094e9616858573b99026659)), closes [#17051](https://github.com/uppler/uppler/issues/17051)
* [BUG] 💥 : disparition placeholder  ([34b83f5](https://github.com/uppler/uppler/commit/34b83f56091c207430e0e587219356558dd8103f)), closes [#16876](https://github.com/uppler/uppler/issues/16876)

## [4.19.6](https://github.com/uppler/uppler/compare/v4.19.5...v4.19.6) (2023-04-21)


### Bug Fixes

* test ([089a538](https://github.com/uppler/uppler/commit/089a538a70813c2a974db519544f25cb53a38678))

## [4.19.5](https://github.com/uppler/uppler/compare/v4.19.4...v4.19.5) (2023-04-21)


### Bug Fixes

* debug ([a4a3593](https://github.com/uppler/uppler/commit/a4a359317a2afbf3843cfeb09aefdb8841591aac))
* debug ([0f7e29f](https://github.com/uppler/uppler/commit/0f7e29fde2b72a10a9850524c7cba7c872817c5f))
* new release adjustments ([0a2bcd5](https://github.com/uppler/uppler/commit/0a2bcd591956dde95d2ee589b8655695af98fa0f))
* release ([40df691](https://github.com/uppler/uppler/commit/40df691c9c463d713ed733de70ba4ce8efb09bfa))
* release ([35b2b9d](https://github.com/uppler/uppler/commit/35b2b9dd2c9e34366dc037a0529d9f0570158940))
* release ([a43704c](https://github.com/uppler/uppler/commit/a43704cbbb26b9223628c296f2925ea95fee53d5))
* release published ([113947f](https://github.com/uppler/uppler/commit/113947fc1eab1cdbaa46c95ec33419168422ff43))
* release publishing workflow ([60f7004](https://github.com/uppler/uppler/commit/60f70041472f2e3747335fdf5eaa1c221bb0e963))
* remove debugging code ([a952495](https://github.com/uppler/uppler/commit/a9524959f1c14ce17a5285211db2d78cfefbd03e))
* semantic release dry run ([b8ef4ac](https://github.com/uppler/uppler/commit/b8ef4acdf893dabd4e9045c0c7ebd89de1fc649b))
* test new release ([69dee48](https://github.com/uppler/uppler/commit/69dee4840879886fb4836b2bc6bbdb82ee56471f))
* update .version file according to release publishing ([d88ead2](https://github.com/uppler/uppler/commit/d88ead2622d0824f2cca0aa6623df114d8e8dd12))

## [4.19.4](https://github.com/uppler/uppler/compare/v4.19.3...v4.19.4) (2023-04-21)


### Bug Fixes

* auto releasing ([cb00caa](https://github.com/uppler/uppler/commit/cb00caa9efda64494a88992fa46c2440063a61ac))
* test releasing ([e0191eb](https://github.com/uppler/uppler/commit/e0191ebadebccfc1952f4541f0ec3166fdb8928f))

## [4.19.3](https://github.com/uppler/uppler/compare/v4.19.2...v4.19.3) (2023-04-21)


### Bug Fixes

* token permissions ([8a4dca9](https://github.com/uppler/uppler/commit/8a4dca92bf866fed008185e0f1e3611c4d319399))

## [4.19.2](https://github.com/uppler/uppler/compare/v4.19.1...v4.19.2) (2023-04-21)


### Bug Fixes

* add a file with version infos ([524bb45](https://github.com/uppler/uppler/commit/524bb455d34192a5421f561ed98bf5199ecbed63))

## [4.19.1](https://github.com/uppler/uppler/compare/v4.19.0...v4.19.1) (2023-04-20)


### Bug Fixes

* semantic-release adjustments ([15d6c31](https://github.com/uppler/uppler/commit/15d6c31ed4f936bea469e36d63fd9f426823cce1))

# [4.19.0](https://github.com/uppler/uppler/compare/v4.18.0...v4.19.0) (2023-04-20)


### Bug Fixes

* :boom: TEST COMPLETE CI/CD WORKFLOW ([496d315](https://github.com/uppler/uppler/commit/496d315a2bc11fccd79859f65911b4254d47f781)), closes [#17122](https://github.com/uppler/uppler/issues/17122)
* 💥 [API]filtre sur les catégories produit KO ([275998c](https://github.com/uppler/uppler/commit/275998c63f2e341ba2b86a387fe097e9e457d5bd)), closes [#16889](https://github.com/uppler/uppler/issues/16889)

const core = require('@actions/core');
const config = require('../config.js');
const project_id = config.project_id; // TO DO: register as an org secret
const { Octokit } = require("@octokit/core");
const octokit = new Octokit({ auth: core.getInput('token') });
const graphql = octokit.graphql.defaults({
    headers: {
        authorization: core.getInput('token'),
    },
});

async function process() {
    const newItem = await graphql(`
        mutation($project_id: ID!, $content_id: ID!) {
            addProjectV2ItemById(
                input: {
                    projectId: $project_id
                    contentId: $content_id
                }
            ) {
                item {
                    id
                }
            }
        }`,
        {
            project_id,
            content_id: core.getInput('content_id'),
        }
    );

    console.log('project_item', newItem.addProjectV2ItemById.item.id);
    core.setOutput('project_item', newItem.addProjectV2ItemById.item.id);
}

try {
    process();
} catch (error) {
    core.setFailed(error.message);
}

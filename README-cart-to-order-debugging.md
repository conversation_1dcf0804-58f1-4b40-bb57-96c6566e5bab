# Cart-to-Order Transition Bug - Debugging Implementation

## Overview

This implementation adds comprehensive logging and debugging tools to diagnose the critical cart-to-order transition bug affecting the Madewis e-commerce platform.

## Problem Summary

**Issue**: Orders paid by bank transfer remain stuck in cart status instead of transitioning to order status, preventing order fulfillment.

**Impact**: Revenue loss, degraded customer experience, orders not sent to Sage ERP connector.

## Implementation Details

### 1. Enhanced Logging in Bank Provider

**File**: `src/AppBundle/Provider/Bank/Provider.php`

Added comprehensive logging to the `updatePayment()` method (lines 582-713):
- Payment state tracking
- Order state monitoring before updates
- Entity change set detection
- Detailed flush operation monitoring
- Complete error capture with stack traces
- Entity manager unit of work state logging

### 2. Order State Transition Logging

**File**: `src/AppBundle/EventListener/Shop/OrderStateListener.php`

Added logging to the critical `cartConfirme()` transition (lines 57-99):
- Order state before/after cart confirmation
- Type transition monitoring (cart → order)
- Cart confirmation timestamp tracking
- Quote-to-order transformation logging

### 3. Dedicated Payment Logger

**Configuration**: 
- `app/config/config_dev.yml` - Monolog handler configuration
- `app/config/services/provider.yaml` - Bank Provider service injection
- `app/config/services/event_listener.yaml` - OrderStateListener service injection

**Log File**: `var/logs/payment_debug.log`

### 4. Service Dependencies

Updated service configurations to inject the payment logger:
- `AppBundle\Provider\Bank\Provider` - Added `@monolog.logger.payment`
- `AppBundle\EventListener\Shop\OrderStateListener` - Added logger dependency

## Testing & Verification

### Test Script
```bash
php scripts/test-payment-logging.php
```

Verifies:
- Log directory permissions
- Configuration files
- Service dependencies
- Log file creation

### Expected Output
```
✓ Log directory exists and is writable
✓ Payment logger configuration found
✓ Bank Provider service configured with payment logger
✓ Payment logging system configuration test completed!
```

## Usage Instructions

### 1. Clear Cache
```bash
docker exec -it php php bin/console cache:clear
```

### 2. Monitor Logs in Real-Time
```bash
# All payment debug logs
docker exec -it php tail -f var/logs/payment_debug.log

# Filter payment debug only
docker exec -it php tail -f var/logs/payment_debug.log | grep '[PAYMENT_DEBUG]'

# Filter order state debug only
docker exec -it php tail -f var/logs/payment_debug.log | grep '[ORDER_STATE_DEBUG]'
```

### 3. Reproduce the Bug
Follow the reproduction steps from `docs/cart-to-order-transition-bug.md` while monitoring logs.

### 4. Analyze Logs
Look for this sequence:
```
[PAYMENT_DEBUG] Starting updatePayment
[ORDER_STATE_DEBUG] Order transitioning to PENDING state
[ORDER_STATE_DEBUG] cartConfirme() method called successfully  
[PAYMENT_DEBUG] CRITICAL: Entity manager flush failed  # ← The failure point
```

## Key Log Patterns

### Successful Flow
```
[PAYMENT_DEBUG] Starting updatePayment (payment_id: 123, referer_id: 456)
[ORDER_STATE_DEBUG] Order transitioning to PENDING state (order_id: 456, current_type: cart)
[ORDER_STATE_DEBUG] cartConfirme() method called successfully (new_type: order)
[PAYMENT_DEBUG] Entity manager flush completed successfully
```

### Failed Flow (Bug)
```
[PAYMENT_DEBUG] Starting updatePayment (payment_id: 123, referer_id: 456)
[ORDER_STATE_DEBUG] Order transitioning to PENDING state (order_id: 456, current_type: cart)
[ORDER_STATE_DEBUG] cartConfirme() method called successfully (new_type: order)
[PAYMENT_DEBUG] CRITICAL: Entity manager flush failed (error: ...)
```

## Log Analysis Commands

```bash
# Find failed flush operations
docker exec -it php grep "CRITICAL: Entity manager flush failed" var/logs/payment_debug.log

# Monitor specific order ID
docker exec -it php grep "order_id.*123" var/logs/payment_debug.log

# Check entity change sets
docker exec -it php grep "entity_changes" var/logs/payment_debug.log

# View unit of work state during failures
docker exec -it php grep "unit of work state" var/logs/payment_debug.log
```

## Documentation

- **Technical Analysis**: `docs/cart-to-order-transition-bug.md`
- **Debugging Guide**: `docs/debugging-cart-to-order-bug.md`
- **This README**: Implementation overview and usage

## Next Steps

1. **Reproduce Bug**: Follow reproduction steps while monitoring logs
2. **Identify Root Cause**: Analyze flush failure details and entity states
3. **Implement Fix**: Based on findings (entity refresh, transaction boundaries, etc.)
4. **Test Solution**: Verify fix resolves the issue without breaking other functionality

## Manual Workaround

For immediate order fixes:
```sql
UPDATE uppler_order 
SET type='order', state='confirmed', last_state='pending',
    completed_at=updated_at, cart_confirmed_at=updated_at, 
    shipping_state='preparation'
WHERE id = {order_id};
```

## Environment

- **Development**: uppler.local (dev mode)
- **Docker**: "php" container for code execution
- **Framework**: Symfony 3.x with Doctrine ORM
- **Log Rotation**: Max 10 files to prevent disk space issues

---

**Status**: ✅ Logging implementation complete and tested
**Ready for**: Bug reproduction and root cause analysis

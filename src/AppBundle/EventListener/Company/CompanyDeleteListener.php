<?php

namespace AppBundle\EventListener\Company;

use AppBundle\Entity\CompanyInterface;
use AppBundle\Tools\Random;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\EventDispatcher\GenericEvent;

/**
 * Company delete.
 */
class CompanyDeleteListener
{
    /**
     * Company object manager.
     *
     * @var ObjectManager
     */
    protected $companyManager;

    /**
     * Constructor.
     */
    public function __construct(ObjectManager $companyManager)
    {
        $this->companyManager = $companyManager;
    }

    public function processCompany(GenericEvent $event)
    {
        $company = $event->getSubject();

        if (!$company instanceof CompanyInterface) {
            throw new \InvalidArgumentException('Company listener requires event subject to be instance of "AppBundle\Entity\CompanyInterface".');
        }

        if (method_exists($company, 'getSlug')) {
            $company->setSlug(Random::generateHash());
        }

        $this->companyManager->persist($company);
        $this->companyManager->flush($company);
    }
}

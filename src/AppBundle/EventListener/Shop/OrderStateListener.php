<?php

namespace AppBundle\EventListener\Shop;

use App<PERSON><PERSON>le\Entity\CompanyInterface;
use App<PERSON><PERSON>le\Entity\OrderInterface;
use AppB<PERSON>le\Entity\ShipmentInterface;
use AppBundle\Event\UpplerOrderEvents;
use AppBundle\Provider\ConfigurationProviderInterface;
use AppBundle\Provider\Security\AuthenticatedProvider;
use AppBundle\Provider\Shop\OrderProviderInterface;
use AppBundle\Resolver\Shop\StateResolverInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\EventDispatcher\GenericEvent;
use Symfony\Component\PropertyAccess\PropertyAccess;
use Psr\Log\LoggerInterface;

/**
 * Order inventory processing listener.
 */
class OrderStateListener
{
    public function __construct(
        protected readonly StateResolverInterface $stateResolver,
        protected readonly ConfigurationProviderInterface $configurationProvider,
        protected readonly AuthenticatedProvider $authenticatedProvider,
        protected readonly OrderProviderInterface $orderProvider,
        protected readonly LoggerInterface $logger,
    ) {
    }

    public function resolveOrderStates(GenericEvent $event, string $eventName, EventDispatcherInterface $eventDispatcher)
    {
        return $this->doResolveOrderStates($event, $eventName, $eventDispatcher);
    }

    /**
     * Get the order from event and run the inventory processor on it.
     *
     * @throws \InvalidArgumentException
     */
    protected function doResolveOrderStates(GenericEvent $event, string $eventName, EventDispatcherInterface $eventDispatcher, ?string $state = null)
    {
        $order = $event->getSubject();

        if (!$order instanceof OrderInterface) {
            throw new \InvalidArgumentException('Order inventory listener requires event subject to be an instance of "AppBundle\Entity\OrderInterface"');
        }

        if (!(UpplerOrderEvents::PRE_EDIT === $eventName && $order->isCartState())) {
            if ($order->isCartState() && OrderInterface::STATE_EDITED === $state) {
                $state = OrderInterface::STATE_PENDING;
            }

            $cart = $order->getCart();

            if ($order->inState($state)) {
                // pending it's when buyer valide the cart, and cartConfirmedAt is fill
                if (
                    OrderInterface::STATE_PENDING === $state
                    && OrderInterface::STATE_PENDING !== $order->getState()
                ) {
                    $this->logger->info('[ORDER_STATE_DEBUG] Order transitioning to PENDING state - calling cartConfirme()', [
                        'order_id' => $order->getId(),
                        'current_type' => $order->getType(),
                        'current_state' => $order->getState(),
                        'target_state' => $state,
                        'cart_id' => $order->getCart() ? $order->getCart()->getId() : null,
                        'payment_state' => $order->getPaymentState(),
                        'cart_confirmed_at_before' => $order->getCartConfirmedAt() ? $order->getCartConfirmedAt()->format('Y-m-d H:i:s') : null,
                    ]);

                    $order->cartConfirme();

                    $this->logger->info('[ORDER_STATE_DEBUG] cartConfirme() method called successfully', [
                        'order_id' => $order->getId(),
                        'new_type' => $order->getType(),
                        'new_state' => $order->getState(),
                        'cart_confirmed_at_after' => $order->getCartConfirmedAt() ? $order->getCartConfirmedAt()->format('Y-m-d H:i:s') : null,
                    ]);

                    // CRITICAL: Log entity manager state to detect if changes are persisted
                    $this->logger->info('[ORDER_STATE_DEBUG] CRITICAL - Entity state after cartConfirme()', [
                        'order_id' => $order->getId(),
                        'entity_type_in_memory' => $order->getType(),
                        'entity_state_in_memory' => $order->getState(),
                        'is_entity_managed' => $this->stateResolver->getEntityManager()->contains($order),
                        'entity_change_set' => $this->stateResolver->getEntityManager()->getUnitOfWork()->getEntityChangeSet($order),
                    ]);

                    // If quote transformed to order, set state pending to created order
                    if (
                        $order->isCartState()
                        && OrderInterface::STATE_PENDING === $state
                        && $order->hasQuote()
                    ) {
                        $this->logger->info('[ORDER_STATE_DEBUG] Processing quote to order transformation', [
                            'order_id' => $order->getId(),
                            'quote_id' => $order->getQuote() ? $order->getQuote()->getId() : null,
                        ]);

                        // set quote state to ordered when order is confirmed
                        $quote = $order->getQuote();
                        $quote->setOrderedAt(new \DateTime());
                        // remove expiration date when order is confirmed
                        $quote->setExpiresAt(null);
                    }
                }

                // confirmed it's when seller valide the order, and completedAt is fill
                if (
                    OrderInterface::STATE_CONFIRMED === $state
                    && OrderInterface::STATE_CONFIRMED !== $order->getState()
                ) {
                    $order->complete();
                    // Update quote 'expires at' when seller confirms/creates it
                    if (
                        $order->isQuote()
                        && $order->getExpiresAt() == $this->orderProvider->getQuoteRequestExpireDate($order)
                    ) {
                        $this->orderProvider->updateQuoteExpireDate($order);
                    }
                }
                // When order is cancelled, detach order from cart to avoid many bugs
                if (
                    (
                        OrderInterface::STATE_CANCELED === $state
                        && OrderInterface::STATE_CANCELED !== $order->getState()
                    ) || (
                        OrderInterface::STATE_REFUSED === $state
                        && OrderInterface::STATE_REFUSED !== $order->getState()
                    )
                ) {
                    if (null !== $cart) {
                        $cart->getOrders()->removeElement($order);
                        $order->setCart(null);
                        if ($cart->getOrders()->isEmpty()) {
                            $cart->setState(OrderInterface::STATE_CANCELED);
                        }
                    }
                }

                $order->setState($state);
            }

            $this->stateResolver->resolvePaymentState($order);
            $this->stateResolver->resolveShippingState($order);

            // CRITICAL: Log entity state before resolveOrdersCartState - this might be overwriting our changes
            $this->logger->info('[ORDER_STATE_DEBUG] CRITICAL - Entity state BEFORE resolveOrdersCartState', [
                'order_id' => $order->getId(),
                'order_type_before_resolve' => $order->getType(),
                'order_state_before_resolve' => $order->getState(),
                'cart_id' => $cart ? $cart->getId() : null,
            ]);

            $this->stateResolver->resolveOrdersCartState($cart);

            // CRITICAL: Log entity state AFTER resolveOrdersCartState - check if this overwrote our changes
            $this->logger->info('[ORDER_STATE_DEBUG] CRITICAL - Entity state AFTER resolveOrdersCartState', [
                'order_id' => $order->getId(),
                'order_type_after_resolve' => $order->getType(),
                'order_state_after_resolve' => $order->getState(),
                'cart_id' => $cart ? $cart->getId() : null,
            ]);

            if (null !== $order->getCartConfirmedAt()) {
                $eventDispatcher->dispatch($event, UpplerOrderEvents::STATE_UPDATED);
            }

            if (OrderInterface::STATE_EDITED === $state) {
                $order->calculateTotal();
            }
        }
    }

    /**
     * Change order status after buyer cart confirmation depending on automatic order acceptance parameter.
     */
    public function resolveOrderStateAfterCartConfirmation(GenericEvent $event, string $eventName, EventDispatcherInterface $eventDispatcher): void
    {
        $order = $event->getSubject();

        $seller = $order instanceof OrderInterface ? $order->getCompanyReceiver() : null;
        $this->processOrderPending($event, $eventName, $eventDispatcher);
        // if automatic order acceptance is allowed AND enabled by the seller, then order accepted automatically or if automatic quote is enabled for this seller, then quote accepted automatically (excepting for dropshipped order/quote)
        if ($seller
            && !$order->isDropshipped()
            && (
                ($order->isOrder() && $this->configurationProvider->hasCompanyAutomaticAcceptOrderEnabled($seller))
                || ($order->isQuote() && $this->configurationProvider->hasCompanyAutomaticAcceptQuoteEnabled($seller))
            )
        ) {
            $event->setArgument('is_auto', true);
            $eventDispatcher->dispatch($event, UpplerOrderEvents::PRE_CONFIRMATION);
            $eventDispatcher->dispatch($event, UpplerOrderEvents::POST_CONFIRMATION);
        }
    }

    /**
     * Get the order from event and run state.
     *
     * @throws \InvalidArgumentException
     */
    public function processOrderPending(GenericEvent $event, string $eventName, EventDispatcherInterface $eventDispatcher): void
    {
        $this->doResolveOrderStates($event, $eventName, $eventDispatcher, OrderInterface::STATE_PENDING);
    }

    /**
     * Get the order from event and run state.
     *
     * @throws \InvalidArgumentException
     */
    public function processOrderConfirmation(GenericEvent $event, string $eventName, EventDispatcherInterface $eventDispatcher): void
    {
        $this->doResolveOrderStates($event, $eventName, $eventDispatcher, OrderInterface::STATE_CONFIRMED);
    }

    /**
     * Get the order from event and run state.
     *
     * @throws \InvalidArgumentException
     */
    public function processOrderRefuse(GenericEvent $event, string $eventName, EventDispatcherInterface $eventDispatcher): void
    {
        $this->doResolveOrderStates($event, $eventName, $eventDispatcher, OrderInterface::STATE_REFUSED);
        $this->setUpdater($event->getSubject(), 'abortedBy');
        $event->getSubject()->setRefusedAt(new \DateTime());
    }

    public function processOrderExpired(GenericEvent $event, string $eventName, EventDispatcherInterface $eventDispatcher): void
    {
        $this->doResolveOrderStates($event, $eventName, $eventDispatcher, OrderInterface::STATE_EXPIRED);
        $order = $event->getSubject();
        if ($order instanceof OrderInterface) {
            $order->setExpiredAt(new \DateTime());

            if (OrderInterface::STATE_PENDING === $order->getLastState()) {
                $order->setAbortedBy('seller');
            } elseif (OrderInterface::STATE_CONFIRMED === $order->getLastState()) {
                $order->setAbortedBy('buyer');
            }
        }
    }

    /**
     * Get the order from event and run state.
     *
     * @throws \InvalidArgumentException
     */
    public function processOrderCancel(GenericEvent $event, string $eventName, EventDispatcherInterface $eventDispatcher): void
    {
        $this->doResolveOrderStates($event, $eventName, $eventDispatcher, OrderInterface::STATE_CANCELED);
        $this->setUpdater($event->getSubject(), 'abortedBy');
        $event->getSubject()->setCanceledAt(new \DateTime());
    }

    /**
     * Get the order from event and run state.
     *
     * @throws \InvalidArgumentException
     */
    public function processOrderOutsideTreated(GenericEvent $event, string $eventName, EventDispatcherInterface $eventDispatcher): void
    {
        $this->doResolveOrderStates($event, $eventName, $eventDispatcher, OrderInterface::STATE_OUTSIDE_TREATED);
    }

    /**
     * Get the order from event and run state.
     *
     * @throws \InvalidArgumentException
     */
    public function processOrderCreate(GenericEvent $event, string $eventName, EventDispatcherInterface $eventDispatcher): void
    {
        $this->doResolveOrderStates($event, $eventName, $eventDispatcher, OrderInterface::STATE_NEW);
    }

    public function processOrderEdit(GenericEvent $event, string $eventName, EventDispatcherInterface $eventDispatcher): void
    {
        $authenticated = $this->authenticatedProvider->getAuthenticatedCompany();
        $order = $event->getSubject();

        if (
            $this->configurationProvider->hasSubmitAndConfirmOrderChanges()
            // Order must be in state EDITING only when the seller update its content
            && $authenticated instanceof CompanyInterface
            && $authenticated->isSeller()
            && $order instanceof OrderInterface
            && (
                \in_array($order->getState(), [OrderInterface::STATE_PENDING, OrderInterface::STATE_EDITED])
                || ($order->hasCreatedBySeller() && $order->isNew())
            )
        ) {
            $this->updateOrderState($order, OrderInterface::STATE_EDITING);
        } else {
            $this->doResolveOrderStates($event, $eventName, $eventDispatcher);
        }
    }

    public function processOrderSubmitChanges(GenericEvent $event, string $eventName, EventDispatcherInterface $eventDispatcher): void
    {
        $this->updateOrderState($event->getSubject(), OrderInterface::STATE_EDITED);
    }

    public function processOrderAcceptChanges(GenericEvent $event, string $eventName, EventDispatcherInterface $eventDispatcher): void
    {
        $this->updateOrderState($event->getSubject(), OrderInterface::STATE_PENDING);
    }

    /**
     * Get the order from event and run statev.
     *
     * @throws \InvalidArgumentException
     */
    public function processOrderPayment(GenericEvent $event, string $eventName, EventDispatcherInterface $eventDispatcher): void
    {
        // only resolve payment state to not generate boucl de redirection else
        // state_update it's dispatch and this event retry a charge
        $order = $event->getSubject();

        if (!$order instanceof OrderInterface) {
            throw new \InvalidArgumentException('Order inventory listener requires event subject to be an instance of "AppBundle\Entity\OrderInterface"');
        }

        $this->stateResolver->resolvePaymentState($order);
    }

    public function checkShippingStates(GenericEvent $event, string $eventName, EventDispatcherInterface $eventDispatcher): void
    {
        $order = $event->getSubject();
        // avoid order without shipment to be cancelled
        if (!$order->hasShipments()) {
            return;
        }

        foreach ($order->getShipments() as $shipment) {
            if (ShipmentInterface::STATE_CANCELLED !== $shipment->getState()) {
                return;
            }
        }

        // If all shipments are cancelled, the all order is cancelled
        $eventDispatcher->dispatch($event, UpplerOrderEvents::PRE_CANCEL);

        $eventDispatcher->dispatch($event, UpplerOrderEvents::POST_CANCEL);
    }

    public function resolveImportedOrderState(GenericEvent $event, string $eventName, EventDispatcherInterface $eventDispatcher): void
    {
        $order = $event->getSubject();
        // avoid quote to be treated as order to update state
        if (!$order->isOrder()) {
            return;
        }

        $this->resolveOrderStateAfterCartConfirmation($event, $eventName, $eventDispatcher);
    }

    protected function setUpdater(OrderInterface $order, string $property): OrderInterface
    {
        $authenticated = $this->authenticatedProvider->getAuthenticatedCompany();
        $dropshippedOrder = $this->orderProvider->getDropshippedOrder($order);

        // if dropshipping, test also if user is buyer of dropshipped order
        $byBuyer = $order->isBuyer($authenticated) || ($dropshippedOrder instanceof OrderInterface && $dropshippedOrder->isBuyer($authenticated));

        // if dropshipping, test also if user is seller of dropshipped order
        $bySeller = $order->isSellerOrSupplier($authenticated) || ($dropshippedOrder instanceof OrderInterface && $dropshippedOrder->isSellerOrSupplier($authenticated));

        $updater = $bySeller ? OrderInterface::BY_SELLER : ($byBuyer ? OrderInterface::BY_BUYER : OrderInterface::BY_OPERATOR);
        $propertyAccessor = PropertyAccess::createPropertyAccessor();
        if ($propertyAccessor->isWritable($order, $property)) {
            $propertyAccessor->setValue($order, $property, $updater);
        }

        return $order;
    }

    protected function updateOrderState(OrderInterface $order, string $state): bool
    {
        if ($order->getState() !== $state) {
            $order->setState($state);
            $this->setUpdater($order, 'updatedBy');

            return true;
        }

        return false;
    }
}

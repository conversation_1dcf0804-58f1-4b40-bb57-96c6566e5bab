<?php

namespace AppBundle\EventListener\Shop;

use AppB<PERSON>le\Entity\AdjustmentInterface;
use App<PERSON><PERSON>le\Entity\CartInterface;
use AppBundle\Entity\OrderInterface;
use AppBundle\Event\UpplerCartEvents;
use AppBundle\Processor\ShippingChargesProcessor;
use AppBundle\Processor\TaxationProcessorInterface;
use AppBundle\Provider\ConfigurationProviderInterface;
use Symfony\Component\EventDispatcher\GenericEvent;

/**
 * Order taxation listener.
 */
class OrderTaxationListener
{
    public function __construct(
        protected readonly TaxationProcessorInterface $taxationProcessor,
        protected readonly ConfigurationProviderInterface $configurationProvider,
        protected readonly ShippingChargesProcessor $shippingChargesProcessor
    ) {
    }

    /**
     * Get the order from event and run the taxation processor on it.
     */
    public function applyTaxes(GenericEvent $event, $eventName, $dispatcher): void
    {
        if (UpplerCartEvents::SHIPPING_COMPLETED === $eventName && !$this->configurationProvider->hasShippingMethod()) {
            return;
        }

        $order = $event->getSubject();

        if (!$order instanceof OrderInterface) {
            throw new \InvalidArgumentException('Order taxation listener requires event subject to be instance of "AppBundle\Entity\OrderInterface"');
        }

        $cart = $order->getCart();

        if (
            (
                $cart instanceof CartInterface
                && (
                    !$cart->isNew()
                    || $order->hasCreatedBySeller()
                    || $this->configurationProvider->getShowIncTaxPrices()
                    || $order->isQuote()
                    || $order->isWishlist()
                )
            )
            || $order->isSubOrder()
        ) {
            $this->taxationProcessor->applyTaxes($order);
        }
    }

    public function applyTaxesOnFees(GenericEvent $event): void
    {
        $order = $event->getSubject();

        if ($order instanceof OrderInterface) {
            $orderFeesAmount = $order->getAdjustmentsTotalByType(AdjustmentInterface::FEE_ADJUSTMENT, ['items', 'shipments']);

            // Check fees amount on order before apply VAT on it.
            if ($orderFeesAmount > 0) {
                $this->taxationProcessor->applyTaxesOnFees($order);
                // Recalculate order shipping adjustment with taxes and fees on taxes
                $this->shippingChargesProcessor->applyShippingCharges($order);
            }
        }
    }
}

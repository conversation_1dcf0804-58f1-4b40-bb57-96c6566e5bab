AppBundle\DTO\Output\MethodProposal:
    exclusion_policy: ALL
    accessor_order: custom
    properties:
        shippingMethod:
            serialized_name: shipping_method
            type: AppBundle\DTO\Output\ShippingMethod
            groups:
                - api_cart_shipping_methods
                - api_buyer_cart_shipping_methods

        selected:
            type: boolean
            groups:
                - api_cart_shipping_methods
                - api_buyer_cart_shipping_methods

        amount:
            type: integer
            groups:
                - api_cart_shipping_methods
                - api_buyer_cart_shipping_methods

        cart:
            type: AppBundle\DTO\Output\Cart
            groups:
                - api_cart_shipping_methods

        order:
            type: AppBundle\DTO\Output\Order
            groups:
                - api_cart_shipping_methods
                - api_buyer_cart_shipping_methods

        item:
            type: AppBundle\DTO\Output\OrderItem
            groups:
                - api_cart_shipping_methods
                - api_buyer_cart_shipping_methods

<?php

namespace AppBundle\Command;

use AppBundle\Provider\Bank\PaymentProvider;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class RefundCommand extends Command
{
    public function __construct(private readonly PaymentProvider $paymentProvider)
    {
        parent::__construct();
    }

    protected function configure()
    {
        $this->setName('uppler:bank:refund')
            ->addOption(
                'payment',
                null,
                InputOption::VALUE_REQUIRED,
                'force to refund payment id',
                null
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $paymentId = $input->getOption('payment');

        $this->paymentProvider->transferRefund($paymentId);

        return 0;
    }
}

<?php

namespace AppBundle\Event;

class UpplerQuotableTypes
{
    final public const QUOTABLE_TYPE_NOBODY = 'nobody';
    final public const QUOTABLE_TYPE_CONTACT = 'contact';
    final public const QUOTABLE_TYPE_CERTIFIED = 'certified';
    final public const QUOTABLE_TYPE_ALL = 'all';

    final public const QUOTABLE_TYPE_QUOTABLE = [
        self::QUOTABLE_TYPE_CONTACT,
        self::QUOTABLE_TYPE_CERTIFIED,
        self::QUOTABLE_TYPE_ALL,
    ];

    final public const DEFAULT_QUOTABLE_TYPE = self::QUOTABLE_TYPE_CERTIFIED;

    public static function getAllTypes(): array
    {
        return [
            self::QUOTABLE_TYPE_NOBODY => self::QUOTABLE_TYPE_NOBODY,
            self::QUOTABLE_TYPE_CONTACT => self::QUOTABLE_TYPE_CONTACT,
            self::QUOTABLE_TYPE_CERTIFIED => self::QUOTABLE_TYPE_CERTIFIED,
            self::QUOTABLE_TYPE_ALL => self::QUOTABLE_TYPE_ALL,
        ];
    }

    public static function hasType($type): bool
    {
        return \in_array($type, self::getAllTypes());
    }
}

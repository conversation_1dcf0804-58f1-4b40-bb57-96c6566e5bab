<?php

namespace AppBundle\Security\Authorizer\Review;

use App<PERSON><PERSON>le\Entity\CompanyInterface;
use App<PERSON><PERSON>le\Entity\ProductInterface;
use AppBundle\Entity\ReviewConfigurationInterface;
use AppB<PERSON>le\Entity\ReviewInterface;
use AppB<PERSON>le\Entity\UserInterface;
use AppBundle\Model\ReviewableInterface;
use AppBundle\Provider\Review\ReviewConfigurationProviderInterface;
use AppBundle\Provider\Review\ReviewProviderInterface;
use AppBundle\Provider\Security\AuthenticatedProviderInterface;

abstract class AbstractReviewAuthorizer implements ReviewAuthorizerInterface
{
    public function __construct(private readonly AuthenticatedProviderInterface $authenticatedProvider, private readonly ReviewProviderInterface $reviewProvider, private readonly ReviewConfigurationProviderInterface $reviewConfigurationProvider)
    {
    }

    public function canIndex(UserInterface $user): bool
    {
        return $user->getCompany() instanceof CompanyInterface;
    }

    public function canList(ReviewableInterface $reviewable): bool
    {
        $reviewConfiguration = $this->reviewConfigurationProvider->getEnabledByReviewable($reviewable);

        return $reviewConfiguration instanceof ReviewConfigurationInterface;
    }

    public function canCreate(ReviewableInterface $reviewable): bool
    {
        $reviewConfiguration = $this->reviewConfigurationProvider->getEnabledByReviewable($reviewable);

        return $reviewConfiguration instanceof ReviewConfigurationInterface
            && ($authenticated = $this->authenticatedProvider->getAuthenticatedCompany()) instanceof CompanyInterface
            && ($authenticatedUser = $authenticated->getUser()) instanceof UserInterface
            && $authenticated->isBuyer()
            && ($reviewableOwner = $this->reviewProvider->getReviewableOwner($reviewable)) instanceof CompanyInterface
            && $authenticated->getId() !== $reviewableOwner->getId()
            && (
                (
                    0 === $this->reviewProvider->countAuthorPublishedReviewsByReviewable($reviewable, $authenticatedUser)
                    && !$reviewConfiguration->hasReply()
                )
                || ( //if reply activate, can publish many review
                    $reviewConfiguration->hasReply()
                )
            )
        ;
    }

    public function canSee(ReviewInterface $review): bool
    {
        $authenticated = $this->authenticatedProvider->getAuthenticatedCompany();
        $authenticatedUser = null;
        $reviewConfiguration = $review->getConfiguration();
        $canSee = false;

        if (!$reviewConfiguration->hasVisibilities()) {
            // none selected, so all authorized
            $canSee = true;
            if ($authenticated instanceof CompanyInterface) {
                $authenticatedUser = $authenticated->getUser();
            }
        } elseif ($authenticated instanceof CompanyInterface) {
            $authenticatedUser = $authenticated->getUser();
            // if company authenticated, check roles
            $authenticatedRoles = $authenticated->getRoles();
            foreach ($authenticatedRoles as $role) {
                if ($reviewConfiguration->hasVisibility($role)) {
                    $canSee = true;

                    break;
                }
            }
        } elseif ($reviewConfiguration->hasVisibility(ReviewConfigurationInterface::ROLE_ANONYMOUS)) {
            // if not connected, check anonymous role only
            $canSee = true;
        }

        return $this->authenticatedProvider->isAuthenticatedUserAdminAll()
                || (
                    $review->isPublished()
                    && ($author = $review->getAuthor()) instanceof UserInterface
                    && $author == $authenticatedUser
                    || $canSee
                )
        ;
    }

    public function canUpdate(ReviewInterface $review): bool
    {
        return null !== $this->reviewProvider->getReviewableByReview($review)
            && (
                $this->authenticatedProvider->getAuthenticatedCompany() instanceof CompanyInterface
                || $this->authenticatedProvider->isAuthenticatedUserAdminAll()
            )
        ;
    }

    public function canComment(ReviewInterface $review): bool
    {
        return $this->authenticatedProvider->getAuthenticatedCompany() instanceof CompanyInterface;
    }

    public function canPublish(ReviewInterface $review): bool
    {
        return !$review->isPublished()
            && (
                (
                    $this->authenticatedProvider->isAuthenticatedUserAdminAll()
                    && null !== $this->reviewProvider->getReviewableByReview($review)
                )
                || (
                    ReviewConfigurationInterface::PUBLISH_MODE_OWNER === $review->getConfiguration()->getPublishMode()
                    && ($company = $this->authenticatedProvider->getAuthenticatedCompany()) instanceof CompanyInterface
                    && $this->reviewProvider->getReviewableOwner($this->reviewProvider->getReviewableByReview($review)) === $company
                )
            )
        ;
    }

    public function canPublishReply(ReviewInterface $review): bool
    {
        return $review->isPublished()
            && (($this->authenticatedProvider->isAuthenticatedUserAdminAll())
            || ((ReviewConfigurationInterface::PUBLISH_MODE_OWNER === $review->getConfiguration()->getPublishMode())
                && (($company = $this->authenticatedProvider->getAuthenticatedCompany()) instanceof CompanyInterface)
                && ($this->reviewProvider->getReviewableOwner($this->reviewProvider->getReviewableByReview($review)) === $company)));
    }

    public function canRefuse(ReviewInterface $review): bool
    {
        return !$review->isRefused()
            && (
                (
                    $this->authenticatedProvider->isAuthenticatedUserAdminAll()
                    && null !== $this->reviewProvider->getReviewableByReview($review)
                )
                || (
                    ReviewConfigurationInterface::PUBLISH_MODE_OWNER === $review->getConfiguration()->getPublishMode()
                    && ($company = $this->authenticatedProvider->getAuthenticatedCompany()) instanceof CompanyInterface
                    && $this->reviewProvider->getReviewableOwner($this->reviewProvider->getReviewableByReview($review)) === $company
                )
            )
        ;
    }

    public function canReport(ReviewInterface $review): bool
    {
        // review owner cant self report
        return
            $review->getConfiguration()->hasReport()
            && (
                $this->authenticatedProvider->isAuthenticatedUserAdminAll()
                || (
                    ($author = $review->getAuthor()) instanceof UserInterface
                    && !$this->authenticatedProvider->isAuthenticatedUser($author)
                )
            )
        ;
    }

    public function canSeeAuthor(ReviewInterface $review): bool
    {
        $authenticated = $this->authenticatedProvider->getAuthenticatedCompany();
        $reviewConfiguration = $review->getConfiguration();
        $canSeeAuthor = false;

        if (!$reviewConfiguration->hasAuthorVisibilities()) {
            // none selected, so all authorized
            $canSeeAuthor = true;
        } elseif ($authenticated instanceof CompanyInterface) {
            // if company authenticated, check roles
            $authenticatedRoles = $authenticated->getRoles();

            foreach ($authenticatedRoles as $role) {
                if ($reviewConfiguration->hasAuthorVisibility($role)) {
                    $canSeeAuthor = true;

                    break;
                }
            }
        } elseif ($reviewConfiguration->hasAuthorVisibility(ReviewConfigurationInterface::ROLE_ANONYMOUS)) {
            // if not connected, check anonymous role only
            $canSeeAuthor = true;
        }

        return $this->canSee($review) && $canSeeAuthor;
    }

    public function canCreateProductReview(ReviewableInterface $reviewable): bool
    {
        return
            $reviewable instanceof ProductInterface
            && $reviewable->isAccepte()
            && ($authenticated = $this->authenticatedProvider->getAuthenticatedCompany()) instanceof CompanyInterface
            && ($authenticatedUser = $this->authenticatedProvider->getAuthenticatedUser()) instanceof UserInterface
            && $authenticated->isBuyer()
            && ($reviewableOwner = $this->reviewProvider->getReviewableOwner($reviewable)) instanceof CompanyInterface
            && $authenticated->getId() !== $reviewableOwner->getId()
            && 0 === $this->reviewProvider->countAuthorPublishedReviewsByReviewable($reviewable, $authenticatedUser)
        ;
    }
}

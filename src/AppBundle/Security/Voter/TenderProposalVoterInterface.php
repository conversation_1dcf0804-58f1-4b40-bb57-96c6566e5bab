<?php

namespace AppBundle\Security\Voter;

interface TenderProposalVoterInterface extends VoterInterface
{
    public const CAN_CREATE_CONTRACT = 'can_create_contract';
    public const CAN_DUPLICATE = 'can_duplicate';
    public const CAN_CREATE_QUOTE = 'can_create_quote';
    public const CAN_REFUSE = 'can_refuse';
    public const API_FACTORY_CREATE_PROPOSAL = 'api_factory_create_proposal';
    public const API_FACTORY_DECLINE = 'api_factory_decline';

    public const READ_ONLY_ATTRIBUTES = [
        self::INDEX_RESOURCE,
        self::INDEX_API,
        self::SHOW_RESOURCE,
        self::SHOW_API,
    ];
}

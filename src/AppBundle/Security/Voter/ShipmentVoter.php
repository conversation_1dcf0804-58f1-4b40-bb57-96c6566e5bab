<?php

namespace AppBundle\Security\Voter;

use App<PERSON><PERSON>le\Entity\ShipmentInterface;
use App<PERSON><PERSON>le\Entity\UserInterface;
use AppBundle\Security\Authorizer\ShopAuthorizerInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

/**
 * This voter only vote on OrderItemInterface objects of type 'orderItem'.
 */
class ShipmentVoter extends Voter implements VoterInterface
{
    protected $authorizer;

    public function __construct(ShopAuthorizerInterface $authorizer)
    {
        $this->authorizer = $authorizer;
    }

    protected function supports($attribute, $subject): bool
    {
        $expectedAttributes = [
            self::SHOW_API,
            self::UPDATE_API,
            self::SHOW_RESOURCE,
        ];

        // if the attribute isn't one we support, return false
        if (!\in_array($attribute, $expectedAttributes)) {
            return false;
        }

        // only vote on Order resources of type Quote inside this voter
        if (!($subject instanceof ShipmentInterface)) {
            return false;
        }

        return true;
    }

    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        // We need an authenticated user for the following statement
        $user = $token->getUser();
        if (!$user instanceof UserInterface) {
            return false;
        }

        if ($user->hasRole('ROLE_ADMIN_ALL') || $user->hasRole('ROLE_SUPER_ADMIN')) {
            return true;
        }

        $order = $subject->getOrder();

        return match ($attribute) {
            self::SHOW_API, self::SHOW_RESOURCE => $this->authorizer->canSeeOrder($order),
            self::UPDATE_API => $this->authorizer->canEditOrder($order),
            default => throw new \LogicException('This code should not be reached!'),
        };
    }
}

<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace AppBundle\Session;

/**
 * MemcachedSessionHandler.
 *
 * Memcached based session storage handler based on the Memcached class
 * provided by the PHP memcached extension.
 *
 * @see http://php.net/memcached
 *
 * <AUTHOR> <<EMAIL>>
 */
class MemcachedSessionHandler implements \SessionHandlerInterface
{
    private $memcached = null;

    private array $servers = [];

    /**
     * @var int Time to live in seconds
     */
    private int $ttl;

    /**
     * @var string Key prefix for shared environments
     */
    private $prefix;

    public function __construct(private $persistent_id, array $options = [])
    {
        if ($diff = array_diff(array_keys($options), ['prefix', 'expiretime'])) {
            throw new \InvalidArgumentException(sprintf('The following options are not supported "%s"', implode(', ', $diff)));
        }

        $this->ttl = isset($options['expiretime']) ? (int) $options['expiretime'] : 86400;
        $this->prefix = $options['prefix'] ?? 'sf2s';
    }

    public function addServer($host, $port = 11211, $weight = 0)
    {
        $this->servers[] = [$host, $port, $weight];
    }

    /**
     * {@inheritdoc}
     */
    public function open($savePath, $sessionName): bool
    {
        $memcached = new \Memcached($this->persistent_id);
        $memcached->addServers($this->servers);
        $this->memcached = $memcached;

        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function close(): bool
    {
        return $this->memcached->quit();
    }

    /**
     * {@inheritdoc}
     */
    public function read($sessionId): string
    {
        return $this->memcached->get($this->prefix.$sessionId) ?: '';
    }

    /**
     * {@inheritdoc}
     */
    public function write($sessionId, $data): bool
    {
        return $this->memcached->set($this->prefix.$sessionId, $data, time() + $this->ttl);
    }

    /**
     * {@inheritdoc}
     */
    public function destroy($sessionId): bool
    {
        if (!$this->read($sessionId)) {
            return true;
        }

        return $this->memcached->delete($this->prefix.$sessionId);
    }

    /**
     * {@inheritdoc}
     */
    public function gc($maxlifetime): int|false
    {
        // not required here because memcached will auto expire the records anyhow.
        return true;
    }

    /**
     * Return a Memcached instance.
     *
     * @return \Memcached
     */
    protected function getMemcached()
    {
        return $this->memcached;
    }
}

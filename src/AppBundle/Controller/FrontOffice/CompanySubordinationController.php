<?php

namespace AppBundle\Controller\FrontOffice;

use AppBundle\Entity\AccountActionAuthorizer;
use AppBundle\Form\Type\Resource\FrontOffice\AccountActionAuthorizerType;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

class CompanySubordinationController extends DefaultController
{
    /**
     * @return Response
     *
     * @throws NotFoundHttpException
     */
    public function indexAction(Request $request)
    {
        $user = $this->getUser();

        $sorting = $request->get('sorting', []);
        $criteria = [
            'parentCompany' => $user->getCompany(),
        ];

        $resources = $this->getRepository()->createPaginator($criteria, $sorting);

        $resources
            ->setCurrentPage($request->get('page', 1), true, true)
            ->setMaxPerPage($this->config->getPaginationMaxPerPage())
        ;

        return $this->render($this->config->getTemplate('index.html'), [
            'companySubordinations' => $resources,
        ]);
    }

    public function updateAction(Request $request)
    {
        $resource = $this->findOr404($request);
        $em = $this->getDoctrine()->getManager();

        $form = $this->createForm($this->config->getFormType(), $resource);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->domainManager->update($resource);

            return $this->redirectHandler->redirectToIndex();
        }

        return $this->render($this->config->getTemplate('update.html'), [
            $this->config->getResourceName() => $resource,
            'form' => $form->createView(),
        ]);
    }

    /**
     * Reload AccountActionAuthorizer form according to action type selected.
     */
    public function _reloadAAAFormAction(Request $request): JsonResponse
    {
        $companySubordination = $this->findOr404($request);

        if (!$request->isXmlHttpRequest()) {
            throw $this->createNotFoundException();
        }

        $aaa = $this->getDoctrine()->getRepository(AccountActionAuthorizer::class)->createNew();
        $aaa->setAction($request->request->get('action'));

        $response = $this->renderView(
            'FrontOffice/CompanySubordination/_accountActionAuthorizerForm.html.twig',
            [
                'form' => $this->createForm(AccountActionAuthorizerType::class, $aaa, [
                    'company' => $companySubordination->getChildCompany(),
                ])->createView(),
            ]
        );

        return new JsonResponse(['html' => $response]);
    }
}

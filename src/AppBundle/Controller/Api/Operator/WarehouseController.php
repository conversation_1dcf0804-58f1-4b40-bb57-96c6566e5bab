<?php

namespace AppBundle\Controller\Api\Operator;

use AppB<PERSON>le\Annotation\ApiDoc;
use AppB<PERSON>le\Controller\Api\DefaultController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class WarehouseController extends DefaultController
{
    /**
     * @ApiDoc(
     *     description="Retrieves the list of warehouse",
     *     output={
     *         "collection"=true,
     *         "class"="AppBundle\DTO\Output\Warehouse",
     *         "parsers"={"Nelmio\ApiDocBundle\Parser\JmsMetadataParser"},
     *         "groups"={"api_warehouse_index"}
     *     }
     * )
     */
    public function indexAction(Request $request)
    {
        return parent::indexAction($request);
    }

    /**
     * @ApiDoc(
     *     description="Retrieves the details of a warehouse",
     *     output={
     *         "class"="AppBundle\DTO\Output\Warehouse",
     *         "parsers"={"Nelmio\ApiDocBundle\Parser\JmsMetadataParser"},
     *         "groups"={"api_warehouse_show"}
     *     }
     * )
     */
    public function showAction(Request $request)
    {
        return parent::showAction($request);
    }

    /**
     * @ApiDoc(
     *     description="Add a new Warehouse.",
     *     input={
     *         "class"="AppBundle\DTO\Input\Warehouse",
     *         "parsers"={"Nelmio\ApiDocBundle\Parser\JmsMetadataParser"},
     *         "groups"={"api_warehouse_create"}
     *     },
     * )
     */
    public function createAction(Request $request)
    {
        return parent::createAction($request);
    }

    /**
     * @ApiDoc(
     *     description="Update an existing Warehouse.",
     *     input={
     *         "class"="AppBundle\DTO\Input\Warehouse",
     *         "parsers"={"Nelmio\ApiDocBundle\Parser\JmsMetadataParser"},
     *         "groups"={"api_warehouse_update"}
     *     }
     * )
     */
    public function updateAction(Request $request)
    {
        return parent::updateAction($request);
    }

    /**
     * @ApiDoc(
     *     description="Delete the given Warehouse",
     *     input={
     *         "class"="AppBundle\DTO\Input\Warehouse",
     *         "parsers"={"Nelmio\ApiDocBundle\Parser\JmsMetadataParser"},
     *         "groups"={"api_warehouse_delete"}
     *     },
     * )
     */
    public function deleteAction(Request $request)
    {
        $resource = $this->findOr404($request);
        if ($resource->isDefault()) {
            throw new BadRequestHttpException($this->get('translator')->trans('uppler.warehouse.cannot_delete_default'));
        }
        // do not catch the exceptions for API
        return $this->deleteResource($resource);
    }
}

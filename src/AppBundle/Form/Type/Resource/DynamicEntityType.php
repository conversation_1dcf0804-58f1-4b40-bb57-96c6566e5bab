<?php

namespace AppBundle\Form\Type\Resource;

use AppBundle\Entity\DynamicEntity;
use AppBundle\Form\Type\DynamicEntityType as AbstractDynamicEntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\OptionsResolver\OptionsResolver;

/**
 * DynamicEntity form type.
 */
class DynamicEntityType extends AbstractType
{
    public function getParent()
    {
        return AbstractDynamicEntityType::class;
    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver
            ->setDefaults([
                'multiple' => true,
                'required' => false,
                'class' => DynamicEntity::class,
                'attr' => [
                    'class' => 'uppler-select2',
                    'style' => 'width: 100%;',
                    'data-ajax-delay' => 500,
                ],
            ])
        ;
    }

    public function getBlockPrefix()
    {
        return 'uppler_dynamic_entity';
    }
}

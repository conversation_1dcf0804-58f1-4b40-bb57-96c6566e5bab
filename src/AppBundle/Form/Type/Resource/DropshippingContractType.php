<?php

namespace AppBundle\Form\Type\Resource;

use AppBundle\Entity\Company;
use AppBundle\Entity\CompanyInterface;
use AppBundle\Entity\Contract;
use AppBundle\Entity\ContractInterface;
use AppBundle\Form\Type\CompanyIdHiddenFormType;
use AppBundle\Form\Type\Resource\BackOffice\CompanyMatcherType;
use AppBundle\Provider\Security\AuthenticatedProviderInterface;
use AppBundle\ResourceRepository\SellerRepository;
use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class DropshippingContractType extends AbstractType
{
    public function __construct(private readonly SellerRepository $sellerRepository, private readonly AuthenticatedProviderInterface $authenticatedProvider)
    {
    }

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->addEventListener(FormEvents::PRE_SET_DATA, $this->preSetData(...))
            ->addEventListener(FormEvents::PRE_SUBMIT, $this->preSubmit(...))
        ;
    }

    public function preSetData(FormEvent $event)
    {
        $form = $event->getForm();
        $contract = $event->getData();
        $authenticatedCompany = $this->authenticatedProvider->getAuthenticatedCompany();
        $createdBy = null;

        // First step submitted
        if (
            $contract instanceof ContractInterface
            && !$contract->getSellers()->isEmpty()
        ) {
            // In dropshipping contract there is only one seller so take the first one
            $seller = $contract->getSellers()->first();
            $contract->setCreatedBy($seller);
            $this->buildFieldBySeller($form, $contract);
        }

        if (null !== $authenticatedCompany) {
            $criteria = ['excludeCompanyId' => $authenticatedCompany->getId()];
        } else {
            $criteria = [];
        }

        $form
            ->add('sellers', DynamicEntityType::class, [
                'property_path' => 'companyEligibility.companies[0]',
                'label' => 'uppler.contract.dropshipping.search_seller',
                'class' => Company::class,
                'property' => 'name',
                'required' => false,
                'mapped' => false,
                'multiple' => false,
                'query_criteria' => $criteria,
                'repository_method' => 'findValidDropshippingSellersQueryBuilder',
                'data' => $createdBy, // For dropshipping contract, createdBy is never null
                'mandatory' => true,
            ])
        ;
    }

    public function preSubmit(FormEvent $event)
    {
        $form = $event->getForm();
        $contract = $form->getData();
        $data = $event->getData();

        if (
            (
                !empty($data['sellers'])
                && !empty($seller = $this->sellerRepository->findOneBy(['id' => $data['sellers']]))
            )
            || (
                !empty($data['createdBy'])
                && !empty($seller = $this->sellerRepository->findOneBy(['id' => $data['createdBy']]))
            )
        ) {
            $contract->addSeller($seller);
            $contract->setCreatedBy($seller);

            $this->buildFieldBySeller($form, $contract);
        }
    }

    public function buildFieldBySeller(FormInterface $form, ContractInterface $contract)
    {
        // For dropshipping contract, it's not allowed to have multiple sellers
        $creator = $contract->getCreatedBy();

        $form
            ->add('createdBy', CompanyIdHiddenFormType::class)
            ->add('conditions', CollectionType::class, [
                'entry_type' => ContractConditionType::class,
                'label' => 'uppler.contract.dropshipping.conditions',
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
                'entry_options' => [
                    'contract' => $contract,
                    'forcePercentSign' => 'less',
                ],
            ])
            ->add('restrictedBuyers', CompanyMatcherType::class, [
                'label' => 'uppler.contract.dropshipping.restricted_buyers',
                'role' => CompanyInterface::BUYER_TYPE,
                'owner' => $creator,
                'addOperator' => true,
                'excludes' => true,
                'addAnonymousVisibilityCheckbox' => true,
            ])
            ->add('paymentMethods', EntityType::class, [
                'label' => 'uppler.contract.payment_methods',
                'class' => 'App:PaymentMethod',
                'query_builder' => fn (EntityRepository $er) => $er->findByQuery(['isDropshipping' => true]),
                'multiple' => true,
                'required' => false,
                'attr' => [
                    'class' => 'uppler-select2',
                ],
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => Contract::class,
            'validation_groups' => ['uppler'],
        ]);
    }
}

<?php

namespace AppBundle\Form\Type\Resource\BackOffice;

use AppBundle\Entity\PromotionInterface;
use AppBundle\Entity\PromotionTypeConfiguration;
use AppBundle\Provider\ConfigurationProviderInterface;
use AppBundle\Provider\Promotion\ConfigurationProviderInterface as PromotionConfigurationProviderInterface;
use AppBundle\Tools\Form;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;

class PromotionTypeConfigurationType extends AbstractType
{
    /**
     * @var ConfigurationProvider
     */
    protected $provider;

    protected $configurationProvider;

    public function __construct(ConfigurationProviderInterface $configurationProvider, PromotionConfigurationProviderInterface $provider)
    {
        $this->provider = $provider;
        $this->configurationProvider = $configurationProvider;
    }

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->addEventListener(FormEvents::PRE_SET_DATA, $this->onPreSetData(...))
        ;
    }

    /**
     * On pre set data.
     */
    public function onPreSetData(FormEvent $event)
    {
        $configuration = $event->getData();
        $form = $event->getForm();
        $catalogModels = $this->provider->getTypesConfiguration()[$configuration->getType()]['models'] ?? [];

        foreach ($catalogModels as $key => $model) {
            // If loyalty configuration is not enabled and model type is loyalty then the model is removed from the list
            if (!$this->configurationProvider->hasLoyaltyProgram() && \in_array($model, PromotionInterface::MODELS_LOYALTY)) {
                unset($catalogModels[$key]);
            }
            // DISABLED BEACAUSE THE FEATURE HAS BEEN DEV BEFORE CLIENT VALIDATION
            // REENABLE LATER
            if (PromotionInterface::MODEL_ORDER_PAYMENT_METHOD === $model) {
                unset($catalogModels[$key]);
            }
        }
        $form
            ->add('models', ChoiceType::class, [
                'label' => 'uppler.administrator.promotion_type_configuration.models',
                'multiple' => true,
                'choices' => Form::prepareTransChoices($catalogModels, 'uppler.promotion.models', 'name'),
                'attr' => ['class' => 'uppler-select2'],
            ])
        ;
    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver
            ->setDefaults([
                'data_class' => PromotionTypeConfiguration::class,
            ])
        ;
    }
}

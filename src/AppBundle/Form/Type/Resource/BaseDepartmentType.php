<?php

namespace AppBundle\Form\Type\Resource;

use AppBundle\Entity\Address;
use AppBundle\Entity\Department;
use AppBundle\Entity\DepartmentInterface;
use AppBundle\Provider\Security\AuthenticatedProvider;
use AppBundle\Repository\AddressRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

/**
 * Department form type.
 */
class BaseDepartmentType extends AbstractType
{
    protected $authenticatedProvider;

    /**
     * Constructor.
     *
     * @param string $className
     */
    public function __construct(AuthenticatedProvider $authenticatedProvider)
    {
        $this->authenticatedProvider = $authenticatedProvider;
    }

    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $authenticatedCompany = $this->authenticatedProvider->getAuthenticatedCompany();

        $builder
            ->add(
                'name',
                TextType::class,
                [
                    'label' => 'uppler.department.name',
                    'mandatory' => true,
                ]
            )
            ->add(
                'enabled',
                CheckboxType::class,
                ['label' => 'uppler.department.enabled']
            )
        ;

        if ($authenticatedCompany && $authenticatedCompany->isBuyer()) {
            $builder
                ->add(
                    'shippingAddress',
                    EntityType::class,
                    [
                        'class' => Address::class,
                        'choice_label' => 'name',
                        'label' => 'uppler.account.shipping_address',
                        'required' => false,
                        'placeholder' => 'uppler.account.shipping_address.empty',
                        'query_builder' => fn (AddressRepository $er) => $er->createQueryBuilderByCompany(
                            $authenticatedCompany
                        ),
                    ]
                )
                ->add(
                    'billingAddress',
                    EntityType::class,
                    [
                        'class' => Address::class,
                        'choice_label' => 'name',
                        'label' => 'uppler.account.billing_address',
                        'required' => false,
                        'placeholder' => 'uppler.account.billing_address.empty',
                        'query_builder' => fn (AddressRepository $er) => $er->createQueryBuilderByCompany(
                            $authenticatedCompany
                        ),
                    ]
                )
            ;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver
            ->setDefaults([
                'data_class' => Department::class,
                'validation_groups' => function (FormInterface $form) {
                    if ($form->getData() instanceof DepartmentInterface) {
                        return $form->getData()->getId() ? 'DepartmentUpdate' : 'DepartmentCreate';
                    }

                    return [];
                },
            ])
        ;
    }
}

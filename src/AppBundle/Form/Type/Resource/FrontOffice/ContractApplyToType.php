<?php

namespace AppBundle\Form\Type\Resource\FrontOffice;

use AppBundle\Entity\CompanyMatcher;
use AppBundle\Entity\Contract;
use AppBundle\Entity\ContractInterface;
use AppBundle\Form\Type\DynamicEntityType;
use AppBundle\Form\Type\Resource\ContractType as BaseContractType;
use AppBundle\Provider\Contract\ConfigurationProviderInterface;
use AppBundle\Provider\Security\AuthenticatedProvider;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;

class ContractApplyToType extends AbstractType
{
    final const CONTRACT_TYPES_NOT_DISPLAYED = [
        'dropshipping',
        'inventory_report',
    ];

    protected $contractConfigurationProvider;
    protected $authenticatedProvider;

    public function __construct(ConfigurationProviderInterface $contractConfigurationProvider, AuthenticatedProvider $authenticatedProvider)
    {
        $this->contractConfigurationProvider = $contractConfigurationProvider;
        $this->authenticatedProvider = $authenticatedProvider;
    }

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->remove('sellers')
            ->addEventListener(FormEvents::PRE_SET_DATA, function (FormEvent $event) {
                $contract = $event->getData();
                $form = $event->getForm();

                if (
                    $contract instanceof ContractInterface
                    && null === $contract->getCompanyEligibility()
                ) {
                    $contract->setCompanyEligibility(new CompanyMatcher());
                }

                $typeChoices = [];
                foreach ($this->contractConfigurationProvider->getStrategies() as $strategy) {
                    if ($strategy->isEnabled() && !\in_array($strategy->getType(), self::CONTRACT_TYPES_NOT_DISPLAYED)) {
                        $typeChoices[sprintf('uppler.contract.meta_type.%s.name', $strategy->getType())] = $strategy->getType();
                    }
                }

                // Auto contract specific
                $uniqueCompany = null;
                if ($contract instanceof ContractInterface) {
                    if (false === \in_array(ContractInterface::TYPE_AUTOMATIC, $typeChoices)
                        || 1 === $contract->getCompanyEligibility()->getCompanies()->count()
                    ) {
                        $uniqueCompany = $contract->getCompanyEligibility()->getCompanies()->first();
                    }
                }

                // Unique type, set hidden
                if (1 === \count($typeChoices)) {
                    $contractType = reset($typeChoices);
                    $contract->setType($contractType);
                } elseif (1 < \count($typeChoices)) {
                    $form->add('type', ChoiceType::class, [
                        'expanded' => true,
                        'choices' => $typeChoices,
                        'attr' => [
                            'class' => 'contract-type',
                        ],
                    ]);
                }

                $authenticatedCompanyId = $this->authenticatedProvider->getAuthenticatedCompany()->getId();
                $form
                    ->add('company', DynamicEntityType::class, [
                        'property_path' => 'companyEligibility.companies[0]',
                        'label' => 'uppler.contract.company',
                        'class' => 'App:Company',
                        'property' => 'name',
                        'required' => false,
                        'mapped' => false,
                        'query_criteria' => ['company' => $authenticatedCompanyId],
                        'repository_method' => 'findValidBuyersContractApplyQueryBuilder',
                        'data' => $uniqueCompany,
                    ])
                ;
            })
            ->addEventListener(FormEvents::PRE_SUBMIT, function (FormEvent $event) use ($options) {
                $form = $event->getForm();
                $data = $event->getData();

                $contractType = $data['type'] ?? $form->getData()->getType();

                if (
                    \in_array($contractType, [
                        ContractInterface::TYPE_SPECIFIC,
                        ContractInterface::TYPE_SEQUENTIAL,
                    ])
                ) {
                    $fieldOptions = $form->get('company')->getConfig()->getOptions();

                    $fieldOptions['constraints'] = [
                        new NotBlank(['groups' => $options['validation_groups']]),
                    ];
                    $form
                        ->add('company', DynamicEntityType::class, $fieldOptions)
                    ;
                }

                if (isset($data['company']) && !empty($data['company'])) {
                    $data['companyEligibility']['companies'] = [$data['company']];
                }

                $event->setData($data);
            })
        ;
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver
            ->setDefaults([
                'data_class' => Contract::class,
            ])
        ;
    }

    public function getParent()
    {
        return BaseContractType::class;
    }
}

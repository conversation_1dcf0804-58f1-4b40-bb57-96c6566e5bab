<?php

namespace AppBundle\Form\Type\Resource\FrontOffice\Shipment\Proposal;

use AppBundle\Model\Shipment\Proposal\CategoryProposal;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

/**
 * Shipping method cart step form type.
 */
class CategoryProposalType extends AbstractType
{
    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('objectProposals', CollectionType::class, [
                'entry_type' => ObjectProposalType::class,
                'label' => false,
            ]);
    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver
            ->setDefaults([
                'data_class' => CategoryProposal::class,
            ])
        ;
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix()
    {
        return 'uppler_shipment_category_proposal';
    }
}

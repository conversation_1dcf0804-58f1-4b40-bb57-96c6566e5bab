<?php

namespace AppBundle\Form\Type\Resource\FrontOffice\Product;

use AppBundle\Entity\ProductLink;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class ProductAssociateType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('product', ProductidType::class)
            ->add('associatedTo', ProductidType::class, [
                'attr' => [
                    'class' => 'autocomplete-product',
                ],
            ])
            ->add('position', IntegerType::class, [
                'label' => 'uppler.product.form.position',
            ])
        ;
    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver
            ->setDefaults([
                'data_class' => ProductLink::class,
                'validation_groups' => ['uppler'],
            ])
        ;
    }

    public function getBlockPrefix()
    {
        return 'uppler_shop_product_associate';
    }
}

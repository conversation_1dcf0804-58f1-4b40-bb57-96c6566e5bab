<?php

namespace AppBundle\Form\Type\Resource\FrontOffice\Product;

use AppBundle\Entity\Option;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

/**
 * @deprecated seems unused
 */
class OptionType extends AbstractType
{
    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('name', TextType::class, [
                'label' => false,
                'attr' => [
                    'placeholder' => 'uppler.option.name',
                    'class' => 'product-new-option-name',
                ],
            ])
            ->add('values', CollectionType::class, [
                'entry_type' => OptionValueType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
                'label' => false,
            ])
        ;
    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver
            ->setDefaults([
                'data_class' => Option::class,
                'validation_groups' => ['uppler'],
            ])
        ;
    }

    public function getBlockPrefix()
    {
        return 'uppler_shop_option';
    }
}

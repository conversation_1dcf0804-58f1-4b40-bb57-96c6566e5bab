<?php

namespace AppBundle\Form\Type;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\FormBuilderInterface;

/**
 * Class ContactImporterTextType.
 */
class ContactImporterTextType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder->add('text', TextareaType::class, [
            'label' => 'uppler.importer.emails',
            'mandatory' => true,
            'attr' => [
                'rows' => 10,
            ],
        ]);
    }

    public function getParent()
    {
        return ContactImporterType::class;
    }
}

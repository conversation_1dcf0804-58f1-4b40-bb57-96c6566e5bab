<?php

namespace AppBundle\Form\Type;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\Email;
use Symfony\Component\Validator\Constraints\NotBlank;

/**
 * Class ContactImporterMatchType.
 *
 * @deprecated seems unused
 */
class ContactImporterMatchType extends AbstractType
{
    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder->add('email', TextType::class, [
            'label' => false,
            'constraints' => [
                new NotBlank(),
                new Email(),
            ],
        ]);
    }

    public function getBlockPrefix()
    {
        return 'uppler_importer_match';
    }
}

<?php

namespace AppBundle\Form\Type\DataImporter;

use AppBundle\Form\DataTransformer\StringToArrayTransformer;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;

class MultipleTextType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder->addModelTransformer(new StringToArrayTransformer());
    }

    public function getParent()
    {
        return TextType::class;
    }
}

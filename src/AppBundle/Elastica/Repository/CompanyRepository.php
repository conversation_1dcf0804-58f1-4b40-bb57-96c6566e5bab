<?php

namespace AppBundle\Elastica\Repository;

use AppBundle\Context\CompanySearchContext;
use AppBundle\Context\SearchContextInterface;
use AppBundle\Elastica\Paginator\FantaPaginatorAdapter;
use AppBundle\Elastica\Repository\Aggregation\CompanyCountryGlobalAggregation;
use AppBundle\Elastica\Search;
use AppBundle\Elastica\Transformer\ElasticaToModelTransformer;
use AppBundle\Entity\CompanyInterface;
use AppBundle\Entity\CountryInterface;
use AppBundle\Entity\DynamicFieldConfigurationInterface;
use AppBundle\Entity\PropertyInterface;
use AppBundle\Entity\SubscriptionInterface;
use AppBundle\Matcher\ZoneMatcherInterface;
use AppBundle\Model\SearchInterface;
use AppBundle\Provider\ConfigurationProviderInterface as GlobalConfigurationProviderInterface;
use AppBundle\Provider\DebugProvider;
use AppBundle\Provider\Search\ConfigurationProviderInterface as SearchConfigurationProviderInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Elastica\Index;
use Elastica\Query;
use Elastica\Query\AbstractQuery;
use Elastica\Query\BoolQuery;
use Elastica\Query\Exists;
use Elastica\Query\FunctionScore;
use Elastica\Query\MatchAll;
use Elastica\Query\Nested;
use Elastica\Query\Prefix;
use Elastica\Query\Regexp;
use Elastica\Query\Term;
use Elastica\Query\Terms;
use Elastica\Script\Script;
use Pagerfanta\Exception\OutOfRangeCurrentPageException;
use Pagerfanta\Pagerfanta;

/**
 * Company elastica query.
 */
class CompanyRepository extends AbstractRepository
{
    final public const SEARCH_TYPE_DEFAULT = 'default';
    final public const SEARCH_TYPE_NAME = 'name';

    protected $debugProvider;
    protected $zoneMatcher;

    public function __construct(
        EntityManagerInterface $em,
        GlobalConfigurationProviderInterface $globalConfigurationProvider,
        SearchConfigurationProviderInterface $searchConfigurationProvider,
        ElasticaToModelTransformer $transformer,
        Index $index,
        DebugProvider $debugProvider,
        ZoneMatcherInterface $zoneMatcher
    ) {
        parent::__construct($em, $globalConfigurationProvider, $searchConfigurationProvider, $index, $transformer);
        $this->debugProvider = $debugProvider;
        $this->zoneMatcher = $zoneMatcher;
    }

    public function all($page = 1)
    {
        $elasticaQuery = Query::create(new MatchAll());
        $elasticaQuery->setSort(['id' => 'asc']);
        $elasticaQuery->setSource(false);

        $paginatorAdapter = $this->createHybridPaginatorAdapter($elasticaQuery);

        $result = new Pagerfanta(new FantaPaginatorAdapter($paginatorAdapter));

        $result->setMaxPerPage(20);
        $result->setCurrentPage($page);

        return $result;
    }

    public function search(CompanySearchContext $searchContext)
    {
        // Check max_result_window index configration, or set to 10000, default elastic search param
        $maxResultWindow = $this->index->getSettings()->get('max_result_window');
        $maxResultWindow ??= 10000;

        // Calculate max search page can retrieve elasticsearch
        $searchMaxResultWindow = $searchContext->getMaxItems() * $searchContext->getOption('page');
        $searchContext->setOption('maxPageResults', floor($maxResultWindow / $searchContext->getMaxItems()));

        // If current search page is more than elastic max_result_window, add error to suggest search specify
        if ($maxResultWindow < $searchMaxResultWindow) {
            $searchContext->setError(SearchContextInterface::ERROR_MAX_RESULT_WINDOW);
        }

        $name = null;

        $searchData = $searchContext->getSearchData();

        if ($searchData instanceof SearchInterface) {
            $name = $searchData->getName();
        }

        $query = $this->getQuery($searchContext);

        if (self::SEARCH_TYPE_NAME === $this->resolveSearchType($searchContext)) {
            // Based on https://www.elastic.co/guide/en/elasticsearch/guide/current/practical-scoring-function.html#query-norm
            // queryNorm = 1 / √sumOfSquaredWeights + (custom coef)
            $query->setMinScore(1 / (sqrt($searchContext->getOption('minScoreDivisor')) * 2));
        }

        $elasticaQuery = Query::create($query);

        if (null !== $searchData) {
            // Apply default search sort
            if (
                self::SEARCH_TYPE_NAME !== $this->resolveSearchType($searchContext)
                && (
                    null !== ($defaultSort = $this->searchConfigurationProvider->getDefaultCompanySort())
                    && null === $searchData->getSort()
                )
            ) {
                $searchData->setSort($defaultSort);
            }

            if (null !== $searchData->getSort()) {
                $this->applySort($elasticaQuery, $searchContext);
            }
        }

        $elasticaQuery->setSource(false);

        // Apply aggregations
        if ($searchContext->getOption('getAggregations')) {
            $this->applyAggregations($elasticaQuery, $searchContext);
        }

        $this->debugProvider->addData('elasticaQuery', $elasticaQuery);

        if ($searchContext->getOption('getResults')) {
            if ($searchContext->getOption('paginated')) {
                $result = $this->findPaginated($elasticaQuery);
                $result->setMaxPerPage($searchContext->getMaxItems());

                try {
                    $result->setCurrentPage($searchContext->getOption('page'));
                } catch (OutOfRangeCurrentPageException) {
                    return;
                }
            } else {
                $result = $this->find($elasticaQuery, $searchContext->getMaxItems());
            }

            // Debug
            if ($this->debugProvider->hasDebug()) {
                $elasticaQuery->setSource(true);
                $hybridPaginator = $this->createHybridPaginatorAdapter($elasticaQuery);

                $hybridResults = $hybridPaginator->getResults(
                    ($searchContext->getOption('page') - 1) * $searchContext->getMaxItems(),
                    $searchContext->getMaxItems()
                )->toArray();

                $sources = [];
                foreach ($hybridResults as $hybridResult) {
                    $product = $hybridResult->getTransformed();
                    $data = $hybridResult->getResult()->getHit();
                    // $hybridResult->getTransformed()->setSearchScore($data['_score']);
                    $sources[] = $data;
                }
            }
        } else {
            $elasticaQuery->setSize($searchContext->getMaxItems());
            $result = $this->createPaginatorAdapter($elasticaQuery);
        }

        return $result;
    }

    protected function getQueryTypeName(CompanySearchContext $searchContext)
    {
        $searchData = $searchContext->getSearchData();

        $name = null;
        if ($searchData instanceof SearchInterface) {
            $name = $searchData->getName();
        }

        // Ensure name is not null or default query
        if (null === $name) {
            return $this->getQueryTypeDefault($searchContext);
        }

        // Query match and prefix name
        $query = new FunctionScore();

        $authenticated = $searchContext->getOption('authenticated');
        $defaultLocale = $this->getDefaultLocale($authenticated);

        $textSearchFields = ['name'];

        $analyzers = [
            'keyword' => 20,
            'lowercase_analyzer' => 20,
            'strict_analyzer' => 20,
            'standard' => 10,
            null => 10,
            'custom_search_analyzer' => 5,
            'multi_custom_search_analyzer' => 5,
            'reverse_reference_flat_analyzer' => 2,
            'lowercase_ngram_analyzer' => 2,
            'reference_ngram_flat_analyzer' => 1,
            'custom_flat_search_analyzer' => 1,
        ];

        // Complete all fields by subfields
        $allSearchFields = [];

        $cleanText = $this->cleanSearch('company', $defaultLocale, 'name', $name);
        foreach ($analyzers as $analyzer => $weight) {
            if (!isset($allSearchFields[$analyzer])) {
                $allSearchFields[$analyzer] = [];
            }

            $allSearchFields[$analyzer][(string) $cleanText] = ['value' => 'name', 'weight' => $weight];
        }

        foreach ($allSearchFields as $analyzer => $subFields) {
            foreach ($subFields as $cleanText => $fields) {
                if ('lowercase_analyzer' === $analyzer) {
                    $multiWords = preg_split('/( )/', strtolower($cleanText));
                } else {
                    $multiWords = preg_split('/( )/', str_replace('-', '', strtolower($cleanText)));
                }

                foreach ($multiWords as $word) {
                    $fieldValue = $fields['value'];

                    // create query
                    $analyzerSlug = array_search($analyzer, Search::ANALYZERS);
                    if (!empty($analyzerSlug)) {
                        $fieldValue = $fieldValue.'.'.$analyzerSlug;
                    }

                    $fieldQuery = new Term([$fieldValue => $word]);

                    // add wieght for this field query
                    $query->addWeightFunction($fields['weight'], $fieldQuery);
                }
            }
        }

        // search in name is used with wieght
        // $this->searchConfigurationProvider->getStrategy($searchContext)->getQuery($searchContext, $allSearchFields);
        $query->setScoreMode(FunctionScore::SCORE_MODE_SUM);
        // filter is used with boost
        $query->setBoostMode(FunctionScore::BOOST_MODE_SUM);
        $query->setMinScore((int) $this->searchConfigurationProvider->getMinScore() ?: 5);

        $filter = $this->getFilter($searchContext);
        $query->setQuery($filter);

        $globalQuery = new BoolQuery();
        $globalQuery->addMust($query);
        $globalQuery->addFilter($filter);

        $elasticaQuery = Query::create($globalQuery);
        $elasticaQuery->setSource(false);

        return $elasticaQuery;
    }

    protected function getQueryTypeDefault(CompanySearchContext $searchContext)
    {
        $searchData = $searchContext->getSearchData();

        // Query by score
        $query = new FunctionScore();

        // Add weight function by retail price
        if (
            (null !== $searchData && null !== $searchData->getRetailPrices() && (is_countable($searchData->getRetailPrices()) ? \count($searchData->getRetailPrices()) : 0) > 0)
            || (null !== $searchData && null !== $searchData->getCategories() && (is_countable($searchData->getCategories()) ? \count($searchData->getCategories()) : 0) > 0)
        ) {
            if (null !== ($retailPrices = $searchContext->getOption('retailPrices')) && \is_array($retailPrices) && \count($retailPrices) > 0) {
                foreach ($retailPrices as $retailPrice) {
                    $retailPriceFilter = new Term(['retail_price' => $retailPrice]);
                    $query->addWeightFunction(100, $retailPriceFilter);
                }
            }
        }
        // Add weight for specific country
        if (null !== $searchContext->getOption('countryId')) {
            $countryFilter = new Term(['country' => $searchContext->getOption('countryId')]);
            $query->addWeightFunction(50, $countryFilter);
        }

        // Apply company score for buyer
        if ($searchContext->getOption('isBuyer') && null === $searchContext->getOption('subscribed')) {
            $query->addScriptScoreFunction(new Script('doc["score"].value < 0 ? 0 : doc["score"].value'));
        } else {
            if (date('H') > 12) {
                $query->addScriptScoreFunction(new Script('doc["score"].value < 0 ? 0 : doc["score"].value'));
            } else {
                $date = new \DateTime();
                $query->addDecayFunction(FunctionScore::DECAY_EXPONENTIAL, 'created_at', $date->format('c'), '30d', '0d', null, 1);
            }
        }

        $filter = $this->getFilter($searchContext);

        $query->setQuery($filter);

        return $query;
    }

    public function getFilter(CompanySearchContext $searchContext)
    {
        $searchData = $searchContext->getSearchData();

        $name = null;
        if ($searchData instanceof SearchInterface) {
            $name = $searchData->getName();
        }

        $filter = new BoolQuery();

        $stateRemoveFilter = new Term(['state' => CompanyInterface::REFUSE]);
        $filter->addMustNot($stateRemoveFilter);
        $authenticated = $searchContext->getOption('authenticated');

        $companyContactOrVisibleFilter = new BoolQuery();
        $stateFilter = new Term(['visibility' => CompanyInterface::VISIBILITY_VISIBLE]);

        if (null !== $name) {
            // Filter company in contact or is visible
            $companyContactOrVisibleFilter->addShould(new Terms('id', $searchContext->getOption('acceptedContactIds')));
            $companyContactOrVisibleFilter->addMustNot(new Term(['visibility' => CompanyInterface::VISIBILITY_UNVISIBLE]));
        }

        if ($authenticated && $authenticated->hasCertifiedState()) {
            $companyContactOrVisibleFilter->addShould(new Term(['visibility' => CompanyInterface::VISIBILITY_CERTIFIED]));
        } else {
            $companyContactOrVisibleFilter->addMustNot(new Term(['visibility' => CompanyInterface::VISIBILITY_CERTIFIED]));
        }

        $companyContactOrVisibleFilter->addShould($stateFilter);
        $filter->addMust($companyContactOrVisibleFilter);

        if (null !== ($roles = $searchContext->getOption('roles'))) {
            $filter->addMust(
                new Terms('roles', $roles)
            );
        }

        $filter->addMustNot(
            new Terms('id', $searchContext->getOption('excludeIds'))
        );

        // Restricted IDs from company's restrictions
        $restrictedIds = $searchContext->getOption('restrictedIds');
        if (null !== $restrictedIds) {
            $filter->addMust(
                new Terms('id', (array) $restrictedIds)
            );
        }

        if ($searchData instanceof SearchInterface) {
            // If force company with image if in config
            if (
                $searchContext->getOption('forceImage')
            ) {
                $filter->addMust(
                    new Exists('avatar')
                );
            }

            if (
                $searchData->getCountry() instanceof ArrayCollection
                && !$searchData->getCountry()->isEmpty()
            ) {
                $countryIds = [];
                foreach ($searchData->getCountry() as $key => $country) {
                    $countryIds[] = $country->getId();
                }
                $filter->addMust(
                    new Terms('country', $countryIds)
                );
            } elseif ($searchData->getCountry() instanceof CountryInterface) {
                $filter->addMust(
                    new Term(['country' => $searchData->getCountry()->getId()])
                );
            }

            $this->applyDynamicFieldFilter($searchContext, $searchData, $filter);

            if (null !== $searchData->getProvinces() && (is_countable($searchData->getProvinces()) ? \count($searchData->getProvinces()) : 0) > 0) {
                foreach ($searchData->getProvinces() as $province) {
                    $filter->addMust(
                        new Term(['province' => $province->getId()])
                    );
                }
            }

            if (null !== $searchData->getCompanyTypes() && (is_countable($searchData->getCompanyTypes()) ? \count($searchData->getCompanyTypes()) : 0) > 0) {
                foreach ($searchData->getCompanyTypes() as $companyType) {
                    $filter->addMust(
                        new Term(['type' => $companyType->getId()])
                    );
                }
            }

            if (null !== $searchData->getRetailPrices() && (is_countable($searchData->getRetailPrices()) ? \count($searchData->getRetailPrices()) : 0) > 0) {
                foreach ($searchData->getRetailPrices() as $retailPrice) {
                    $filter->addMust(
                        new Term(['retail_prices' => $retailPrice->getId()])
                    );
                }
            }

            if (null !== $searchData->getCategories() && (is_countable($searchData->getCategories()) ? \count($searchData->getCategories()) : 0) > 0) {
                foreach ($searchData->getCategories() as $category) {
                    $filter->addMust(
                        new Term(['categories' => $category->getId()])
                    );
                }
            }

            // Alphabetical filter to filter the companies which begin with the selected letter
            $initialLetter = $searchData->getInitialLetter();
            if (!empty($initialLetter)) {
                // Use lowercase analyzer to avoid name with multiple words selection
                if ('other' === $initialLetter) {
                    $filter->addMust(
                        new Regexp('name.lowercase', '[^a-zA-Z].*')
                    );
                } else {
                    $filter->addMust(
                        new Prefix(['name.lowercase' => $initialLetter])
                    );
                }
            }
        }

        if (null !== $searchContext->getOption('subscribed')) {
            $filter->addMust(
                new Term(['subscription_state' => SubscriptionInterface::STATE_SUBSCRIBE])
            );

            $filter->addMust(
                new Term(['cover' => true])
            );
        }

        // Filter product zones
        if (
            $this->globalConfigurationProvider->hasProductZones()
            && ($authenticated = $searchContext->getOption('authenticated')) instanceof CompanyInterface
            && $authenticated->isBuyer()
        ) {
            $zoneFilter = new BoolQuery();
            $zoneIds = $this->zoneMatcher->matchAllByCompany($authenticated);

            if ((is_countable($zoneIds) ? \count($zoneIds) : 0) > 0) {
                $zoneFilter->addShould(new Terms('product_zones', array_values($zoneIds)));
            }

            $missingZoneFilter = new BoolQuery();
            $missingZoneFilter->addMustNot(new Exists('product_zones'));

            $zoneFilter->addShould($missingZoneFilter);

            $filter->addMust($zoneFilter);
        }

        return $filter;
    }

    public function applyDynamicFieldFilter(CompanySearchContext $searchContext, SearchInterface $searchData, AbstractQuery $filter)
    {
        // Filter by properties
        if (null !== $searchData->getDynamicFields() && !empty($searchData->getDynamicFields())) {
            $propertiesOr = [];

            foreach ($searchData->getDynamicFields() as $dynamicField) {
                $dynamicFieldConfigurationId = $dynamicField->getDynamicFieldConfiguration()->getId();

                if (!empty($dynamicField->getRealValue())) {
                    $dynamicFieldOperator = $this->getDynamicFieldOperator($searchContext, $dynamicFieldConfigurationId);

                    if (PropertyInterface::OPERATOR_AND === $dynamicFieldOperator) {
                        // Value of dynamic field can be bool or array, but Elastica Term need an array.
                        $dynamicFieldValue = (array) $dynamicField->getRealValue();

                        $dynamicFieldAndFilter = new BoolQuery();
                        $dynamicFieldAndFilter->addMust(new Term(['dynamic_fields.dynamic_field_configuration_id' => $dynamicFieldConfigurationId]));

                        if (\in_array($dynamicField->getDynamicFieldConfiguration()->getType(), [
                            DynamicFieldConfigurationInterface::TEXT_TRANSLATE,
                            DynamicFieldConfigurationInterface::TEXTAREA_TRANSLATE,
                        ])) {
                            $dynamicFieldAndFilter->addMust(new Terms('dynamic_fields.value_en', $dynamicFieldValue));
                        } else {
                            $dynamicFieldAndFilter->addMust(new Terms('dynamic_fields.value', $dynamicFieldValue));
                        }

                        $nested = new Nested();
                        $nested->setPath('dynamic_fields');
                        $nested->setQuery($dynamicFieldAndFilter);

                        $filter->addMust($nested);
                    } elseif (PropertyInterface::OPERATOR_OR === $dynamicFieldOperator) {
                        $dynamicFieldValue = $dynamicField->getRealValue();

                        if (!empty($propertiesOr[$dynamicField->getDynamicFieldConfiguration()->getId()])) {
                            $propertiesOr[$dynamicField->getDynamicFieldConfiguration()->getId()] = [];
                        }

                        if (\is_array($dynamicFieldValue)) {
                            foreach ($dynamicFieldValue as $value) {
                                $propertiesOr[$dynamicField->getDynamicFieldConfiguration()->getId()][] = $value;
                            }
                        } else {
                            $propertiesOr[$dynamicField->getDynamicFieldConfiguration()->getId()][] = $dynamicFieldValue;
                        }
                    }
                } else {
                    continue;
                }
            }

            if (\count($propertiesOr) > 0) {
                foreach ($propertiesOr as $dynamicFieldConfigurationId => $values) {
                    $propertiesOrFilter = new BoolQuery();
                    $propertiesOrFilter->addMust(new Term(['dynamic_fields.dynamic_field_configuration_id' => $dynamicFieldConfigurationId]));
                    $propertiesOrFilter->addMust(new Terms('dynamic_fields.value', $values));

                    $nested = new Nested();
                    $nested->setPath('dynamic_fields');
                    $nested->setQuery($propertiesOrFilter);

                    $filter->addMust($nested);
                }
            }
        }
    }

    /**
     * Get property filter operator.
     */
    public function getDynamicFieldOperator(CompanySearchContext $searchContext, $dynamicFieldConfigurationId): ?string
    {
        $filters = $this->searchConfigurationProvider->getCompanyFilters();

        $dynamicFieldConfigurationSearchFilter = null;
        foreach ($filters as $filter) {
            if (
                null !== $filter->getFilter()
                && (
                    $filter->getFilter()->getType() === "dynamic_field_configuration_$dynamicFieldConfigurationId"
                    || 'dynamic_field_configuration' === $filter->getFilter()->getType()
                )
            ) {
                $dynamicFieldConfigurationSearchFilter = $filter;
                break;
            }
        }

        if (null === $dynamicFieldConfigurationSearchFilter) {
            return null;
        }

        return $dynamicFieldConfigurationSearchFilter->getFilter()->getParameters()['operator'] ?? ($this->searchConfigurationProvider->hasPropertyCount() ? PropertyInterface::OPERATOR_AND : PropertyInterface::OPERATOR_OR);
    }

    protected function resolveSearchType(CompanySearchContext $searchContext)
    {
        $name = null;
        $searchData = $searchContext->getSearchData();

        if ($searchData instanceof SearchInterface) {
            $name = $searchData->getName();
        }

        if (null !== $name) {
            return self::SEARCH_TYPE_NAME;
        }

        return self::SEARCH_TYPE_DEFAULT;
    }

    protected function getQuery(CompanySearchContext $searchContext)
    {
        // Get query by type
        $searchType = $this->resolveSearchType($searchContext);

        $query = match ($searchType) {
            self::SEARCH_TYPE_NAME => $this->getQueryTypeName($searchContext),
            default => $this->getQueryTypeDefault($searchContext),
        };

        return $query;
    }

    protected function applyAggregations(Query $elasticaQuery, CompanySearchContext $searchContext)
    {
        // Category aggregation
        $categoriesAggregation = new \Elastica\Aggregation\Terms('categories');
        $categoriesAggregation->setField('categories');
        $categoriesAggregation->setSize(100);

        $elasticaQuery->addAggregation($categoriesAggregation);

        // Province aggregation
        $provinceAggregation = new \Elastica\Aggregation\Terms('provinces');
        $provinceAggregation->setField('province');
        $provinceAggregation->setSize(100);

        $elasticaQuery->addAggregation($provinceAggregation);

        // Company type aggregation
        $companyTypeAggregation = new \Elastica\Aggregation\Terms('company_types');
        $companyTypeAggregation->setField('type');
        $companyTypeAggregation->setSize(100);

        $elasticaQuery->addAggregation($companyTypeAggregation);

        // Country aggregation
        $countryAggregation = CompanyCountryGlobalAggregation::get($this, $searchContext);

        $this->addAggregation($elasticaQuery, $countryAggregation);
    }

    public function searchByProduct(CompanySearchContext $searchContext, array $productCountAggragation = [], array $bestPriceCountAggragation = [])
    {
        // Check max_result_window index configration, or set to 10000, default elastic search param
        $maxResultWindow = $this->index->getSettings()->get('max_result_window');
        $maxResultWindow ??= 10000;

        // Calculate max search page can retrieve elasticsearch
        $searchMaxResultWindow = $searchContext->getMaxItems() * $searchContext->getOption('page');
        $searchContext->setOption('maxPageResults', floor($maxResultWindow / $searchContext->getMaxItems()));

        // If current search page is more than elastic max_result_window, add error to suggest search specify
        if ($maxResultWindow < $searchMaxResultWindow) {
            $searchContext->setError(SearchContextInterface::ERROR_MAX_RESULT_WINDOW);
        }

        $queryFilter = new BoolQuery();
        $companyIdFilter = new Terms('id', array_keys($productCountAggragation));
        $queryFilter->addMust($companyIdFilter);

        $companyBestPrices = [];
        foreach ($bestPriceCountAggragation as $key => $value) {
            $companyBestPrices[$key] = is_countable($value) ? \count($value) : 0;
        }

        $functionScoreQuery = new FunctionScore();
        if (!empty($companyBestPrices)) {
            $functionScoreQuery->addScriptScoreFunction(
                new Script(
                    "id = (String) doc['id'].value; score = product_score.containsKey(id) ? product_score.get(id) : 0; price_score = bestprice_score.containsKey(id) ? bestprice_score.get(id) : 0; return score + price_score;",
                    [
                        'product_score' => $productCountAggragation,
                        'bestprice_score' => $companyBestPrices,
                    ]
                )
            );
        } else {
            $functionScoreQuery->addScriptScoreFunction(
                new Script(
                    "id = (String) doc['id'].value; score = product_score.containsKey(id) ? product_score.get(id) : 0; return score;",
                    [
                        'product_score' => $productCountAggragation,
                    ]
                )
            );
        }

        $functionScoreQuery->setQuery($queryFilter);
        $functionScoreQuery->setMinScore(1);
        $functionScoreQuery->setBoostMode(FunctionScore::BOOST_MODE_REPLACE);

        $elasticaQuery = Query::create($functionScoreQuery);

        $paginator = $this->findPaginated($elasticaQuery);
        $paginator->setMaxPerPage($searchContext->getMaxItems());

        try {
            $paginator->setCurrentPage($searchContext->getOption('page'));
        } catch (OutOfRangeCurrentPageException) {
            return;
        }

        return $paginator;
    }

    protected function applySort(Query $elasticaQuery, CompanySearchContext $searchContext)
    {
        $query = $elasticaQuery->getQuery();
        $searchData = $searchContext->getSearchData();
        $sort = [];

        if (
            $searchData instanceof SearchInterface
            && null !== $searchData->getSort()
        ) {
            switch ($searchData->getSort()) {
                case 'name_asc':
                    $sort['name.raw'] = [
                        'order' => 'asc',
                    ];
                    break;
                case 'name_desc':
                    $sort['name.raw'] = [
                        'order' => 'desc',
                    ];
                    break;
                default:
                    break;
            }
        }

        if (\count($sort)) {
            $elasticaQuery->setSort($sort);
        }
    }
}

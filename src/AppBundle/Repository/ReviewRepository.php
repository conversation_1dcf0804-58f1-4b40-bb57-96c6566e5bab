<?php

namespace AppBundle\Repository;

use AppBundle\Entity\Company;
use AppBundle\Entity\Product;
use AppBundle\Entity\PublicationStateInterface;
use AppBundle\Entity\Review;
use AppBundle\Entity\ReviewConfigurationInterface;
use AppBundle\Entity\UserInterface;
use AppBundle\Model\ReviewableInterface;
use AppBundle\Model\ReviewableOwnerInterface;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Pagerfanta\Pagerfanta;

/**
 * Review repository.
 */
class ReviewRepository extends EntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Review::class);
    }

    /**
     * Create filter paginator.
     *
     * @param array $criteria
     * @param array $sorting
     *
     * @return Pagerfanta
     */
    public function createFilterPaginator($criteria = [], $sorting = [])
    {
        $criteria = array_filter($criteria);
        $queryBuilder = $this->getCollectionQueryBuilder()
            ->leftJoin('o.author', 'u')
        ;

        if (isset($criteria['query'])) {
            $queryBuilder
                ->andWhere('o.name LIKE :query OR o.id LIKE :query')
                ->setParameter('query', '%'.$criteria['query'].'%');
        }

        if (empty($sorting)) {
            if (!\is_array($sorting)) {
                $sorting = [];
            }

            $sorting['id'] = 'DESC';
        } else {
            if (!empty($sorting['author'])) {
                $queryBuilder->addOrderBy('u.id', $sorting['author']);
                unset($sorting['author']);
            }
        }

        $this->applySorting($queryBuilder, $sorting);
        $this->applyCriteria($queryBuilder, $criteria);

        return $this->getPaginator($queryBuilder);
    }

    public function countReviewsByState($state)
    {
        $queryBuilder = $this->getCollectionQueryBuilder();

        return (int) $queryBuilder
            ->select('COUNT(DISTINCT o.id)')
            ->where('o.state = :state')
            ->setParameter('state', $state)
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    public function getReviewsByState(string $state)
    {
        $queryBuilder = $this->getCollectionQueryBuilder();

        return $queryBuilder
            ->where('o.state = :state')
            ->setParameter('state', $state)
            ->getQuery()
            ->getResult()
        ;
    }

    public function getAllReviewsByAuthor(UserInterface $author)
    {
        $queryBuilder = $this->getCollectionQueryBuilder();

        return $queryBuilder
            ->where('o.author = :author')
            ->setParameter('author', $author)
            ->getQuery()
            ->getResult()
        ;
    }

    public function countAllReviewsByAuthor(UserInterface $author)
    {
        $queryBuilder = $this->getCollectionQueryBuilder();

        return (int) $queryBuilder
            ->select('COUNT(DISTINCT o.id)')
            ->where('o.author = :author')
            ->setParameter('author', $author)
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    public function getAllReviewsByReviewable(ReviewableInterface $reviewable)
    {
        $queryBuilder = $this->getCollectionQueryBuilder();

        return $queryBuilder
            ->andWhere('o.reviewableId = :reviewableId AND o.reviewableType = :reviewableType')
            ->setParameter('reviewableId', $reviewable->getId())
            ->setParameter('reviewableType', $reviewable->getReviewableType())
            ->getQuery()
            ->getResult()
        ;
    }

    public function countAllReviewsByReviewable(ReviewableInterface $reviewable)
    {
        $queryBuilder = $this->getCollectionQueryBuilder();

        return (int) $queryBuilder
            ->select('COUNT(DISTINCT o.id)')
            ->andWhere('o.reviewableId = :reviewableId AND o.reviewableType = :reviewableType')
            ->setParameter('reviewableId', $reviewable->getId())
            ->setParameter('reviewableType', $reviewable->getReviewableType())
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    public function filterByReviewableQueryBuilder(ReviewableInterface $reviewable, $criteria = [], $sorting = [], $limit = null, $offset = null)
    {
        $queryBuilder = $this->getCollectionQueryBuilder();
        $queryBuilder
            ->innerJoin('o.configuration', 'c')
            ->andWhere('o.reviewableId = :reviewableId AND o.reviewableType = :reviewableType')
            ->setParameter('reviewableId', $reviewable->getId())
            ->setParameter('reviewableType', $reviewable->getReviewableType())
        ;

        if (isset($criteria['state']) && \in_array($criteria['state'], PublicationStateInterface::ALL_STATES)) {
            $queryBuilder
                ->andWhere('o.state = :state')
                ->setParameter('state', $criteria['state'])
            ;

            unset($criteria['state']);
        }

        if (null !== $limit) {
            $queryBuilder->setMaxResults($limit);
        }

        if (null !== $offset) {
            $queryBuilder->setFirstResult($offset);
        }

        if (empty($sorting)) {
            if (!\is_array($sorting)) {
                $sorting = [];
            }
            $sorting['createdAt'] = 'DESC';
        }

        $this->applyCriteria($queryBuilder, $criteria);
        $this->applySorting($queryBuilder, $sorting);

        return $queryBuilder;
    }

    public function getLastPublishedReviewsByReviewable(ReviewableInterface $reviewable, $limit = 5)
    {
        $criteria = [];
        $criteria['state'] = PublicationStateInterface::STATE_PUBLISHED;

        return $this->getAllReviewsByReviewableAndState($reviewable, false, $criteria, [], $limit);
    }

    public function getAllReviewsByPublishMode(string $mode)
    {
        $query = $this->getCollectionQueryBuilder()
            ->innerJoin('o.configuration', 'c')
            ->where('c.publishMode = :mode')
            ->setParameter(':mode', $mode);

        return $this->getPaginator($query);
    }

    public function getReviewsByReviewableOwner(ReviewableOwnerInterface $owner, array $criteria = []): Pagerfanta
    {
        $criteria['reviewableOwner'] = $owner;
        $qb = $this->getCollectionQueryBuilder();

        $this->getPaginator($qb
            ->innerJoin('o.configuration', 'c')
            ->andWhere(
                $qb->expr()->orX(
                    $qb->expr()->eq('c.publishMode', ':publishModeOwner'),
                    $qb->expr()->eq('c.publishMode', ':publishModeAuto'),
                    $qb->expr()->andX(
                        $qb->expr()->eq('c.publishMode', ':publishModeOperator'),
                        $qb->expr()->eq('o.state', ':publishedState')
                    )
                )
            )
            ->setParameter('publishModeOwner', ReviewConfigurationInterface::PUBLISH_MODE_OWNER)
            ->setParameter('publishModeOperator', ReviewConfigurationInterface::PUBLISH_MODE_OPERATOR)
            ->setParameter('publishModeAuto', ReviewConfigurationInterface::PUBLISH_MODE_DIRECTLY)
            ->setParameter('publishedState', PublicationStateInterface::STATE_PUBLISHED))
        ;

        $this->applyCriteria($qb, $criteria);

        return $this->getPaginator($qb);
    }

    public function paginateByOwner(ReviewableOwnerInterface $owner, array $criteria = []): Pagerfanta
    {
        $criteria['owner'] = $owner;

        return $this->getPaginator($this->findByQuery($criteria));
    }

    /**
     * Create filter paginator.
     *
     * @param array $criteria
     * @param array $sorting
     *
     * @return Pagerfanta
     */
    public function createFilterByReviewablePaginator(ReviewableInterface $reviewable, $criteria = [], $sorting = [])
    {
        $queryBuilder = $this->filterByReviewableQueryBuilder($reviewable, $criteria, $sorting);

        return $this->getPaginator($queryBuilder);
    }

    public function getAllPublishedReviewsByReviewable(ReviewableInterface $reviewable, $count = false, $criteria = [], $sorting = [], $limit = null, $offset = null)
    {
        $criteria['state'] = PublicationStateInterface::STATE_PUBLISHED;

        return $this->getAllReviewsByReviewableAndState($reviewable, $count, $criteria, $sorting, $limit, $offset);
    }

    public function getAllReviewsByReviewableAndState(ReviewableInterface $reviewable, $count = false, $criteria = [], $sorting = [], $limit = null, $offset = null)
    {
        $queryBuilder = $this->filterByReviewableQueryBuilder($reviewable, $criteria, $sorting, $limit, $offset);

        if ($count) {
            return (int) $queryBuilder
                ->select('COUNT(DISTINCT o.id)')
                ->getQuery()
                ->getSingleScalarResult()
            ;
        } else {
            return $queryBuilder
                ->getQuery()
                ->getResult()
            ;
        }
    }

    public function getAuthorPublishedReviewsByReviewable(ReviewableInterface $reviewable, UserInterface $author)
    {
        $queryBuilder = $this->getCollectionQueryBuilder();

        return $queryBuilder
            ->andWhere('o.reviewableId = :reviewableId AND o.reviewableType = :reviewableType AND o.author = :author AND o.state = :state')
            ->setParameter('reviewableId', $reviewable->getId())
            ->setParameter('reviewableType', $reviewable->getReviewableType())
            ->setParameter('author', $author)
            ->setParameter('state', PublicationStateInterface::STATE_PUBLISHED)
            ->getQuery()
            ->getResult()
        ;
    }

    public function countAuthorPublishedReviewsByReviewable(ReviewableInterface $reviewable, UserInterface $author)
    {
        $queryBuilder = $this->getCollectionQueryBuilder();

        return (int) $queryBuilder
            ->select('COUNT(DISTINCT o.id)')
            ->andWhere('o.reviewableId = :reviewableId AND o.reviewableType = :reviewableType AND o.author = :author AND o.state = :state')
            ->setParameter('reviewableId', $reviewable->getId())
            ->setParameter('reviewableType', $reviewable->getReviewableType())
            ->setParameter('author', $author)
            ->setParameter('state', PublicationStateInterface::STATE_PUBLISHED)
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    public function findOnePendingByReviewableAndAuthor(ReviewableInterface $reviewable, UserInterface $author)
    {
        $queryBuilder = $this->getCollectionQueryBuilder();

        return $queryBuilder
            ->andWhere('o.reviewableId = :reviewableId AND o.reviewableType = :reviewableType AND o.author = :author AND o.state = :state')
            ->setParameter('reviewableId', $reviewable->getId())
            ->setParameter('reviewableType', $reviewable->getReviewableType())
            ->setParameter('author', $author)
            ->setParameter('state', PublicationStateInterface::STATE_PENDING)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    public function findLastOneByReviewableAndAuthor(ReviewableInterface $reviewable, UserInterface $author)
    {
        $queryBuilder = $this->getCollectionQueryBuilder();

        return $queryBuilder
            ->andWhere('o.reviewableId = :reviewableId AND o.reviewableType = :reviewableType AND o.author = :author')
            ->setParameter('reviewableId', $reviewable->getId())
            ->setParameter('reviewableType', $reviewable->getReviewableType())
            ->setParameter('author', $author)
            ->orderBy('o.createdAt', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    public function countOnePendingByReviewableAndAuthor(ReviewableInterface $reviewable, UserInterface $author)
    {
        $queryBuilder = $this->getCollectionQueryBuilder();

        return (int) $queryBuilder
            ->select('COUNT(DISTINCT o.id)')
            ->andWhere('o.reviewableId = :reviewableId AND o.reviewableType = :reviewableType AND o.author = :author AND o.state = :state')
            ->setParameter('reviewableId', $reviewable->getId())
            ->setParameter('reviewableType', $reviewable->getReviewableType())
            ->setParameter('author', $author)
            ->setParameter('state', PublicationStateInterface::STATE_PENDING)
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    protected function applyCriteria(QueryBuilder $qb, array $criteria = null, bool $excludeEmpty = false)
    {
        if ($owner = $this->extractCriteria($criteria, 'owner')) {
            $qb
                ->innerJoin('o.author', 'user')
                ->innerJoin('user.companyUsers', 'cu')
                ->where('cu.company = :owner')
                ->setParameter('owner', $owner);
        }

        if ($reviewableOwner = $this->extractCriteria($criteria, 'reviewableOwner')) {
            $qb
                ->leftJoin('App:Product', 'p', Join::WITH, $qb->expr()->eq('p.id', 'o.reviewableId'))
                ->andWhere($qb->expr()->orX(
                    $qb->expr()->andX(
                        $qb->expr()->eq('o.reviewableType', ':companyReviewableType'),
                        $qb->expr()->eq('o.reviewableId', ':reviewableOwner')),
                    $qb->expr()->andX(
                        $qb->expr()->eq('o.reviewableType', ':productReviewableType'),
                        $qb->expr()->eq('p.company', ':reviewableOwner'))
                ))
                ->setParameter('companyReviewableType', ReviewableInterface::TYPE_COMPANY)
                ->setParameter('productReviewableType', ReviewableInterface::TYPE_PRODUCT)
                ->setParameter('reviewableOwner', $reviewableOwner);
        }

        if ($reviewable = $this->extractCriteria($criteria, 'reviewableLike')) {
            $this->join($qb, Company::class, 'co', Join::LEFT_JOIN, Join::WITH, 'o.reviewableId = co and o.reviewableType = :companyType');
            $this->join($qb, Product::class, 'pr', Join::LEFT_JOIN, Join::WITH, 'o.reviewableId = pr and o.reviewableType = :productType');
            $orX = $qb->expr()->orx(
                $this->compareTranslatable($qb, 'pr', 'name', "%$reviewable%", 'like'),
                $this->compareTranslatable($qb, 'co', 'corporateName', "%$reviewable%", 'like'),
                $qb->expr()->like('co.name', ':reviewable')
            );
            $qb->andWhere($orX)
                ->setParameter('companyType', ReviewableInterface::TYPE_COMPANY)
                ->setParameter('productType', ReviewableInterface::TYPE_PRODUCT)
                ->setParameter('reviewable', "%$reviewable%");
        }

        // Need this criteria to extract its value
        $this->compareEqual($qb, $criteria, 'reviewableType', 'o.reviewableType');

        if ($query = $this->extractCriteria($criteria, 'query')) {
            $orX = $qb->expr()->orX();
            $orX->add('o.id LIKE :query');
            $orX->add('o.title LIKE :query');
            $qb->andWhere($orX)->setParameter(':query', "%$query%");
        }

        return parent::applyCriteria($qb, $criteria, $excludeEmpty);
    }
}

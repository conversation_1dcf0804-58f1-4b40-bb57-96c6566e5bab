<?php

namespace AppBundle\Repository;

use AppBundle\DoctrineBehavior\Translatable\TranslatableInterface;
use AppBundle\Entity\CompanyInterface;
use AppBundle\Entity\PropertyField;
use AppBundle\Entity\PropertyFieldInterface;
use AppBundle\Entity\PropertyInterface;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Pagerfanta\PagerfantaInterface;

class PropertyFieldRepository extends EntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PropertyField::class);
    }

    public function countByProperty(PropertyInterface $property)
    {
        $queryBuilder = $this->createQueryBuilder('o');
        $queryBuilder
            ->select('count(o.id)')
            ->where('o.property = :property')
            ->setParameter('property', $property)
            ->andWhere('o.status != :pending OR o.status IS NULL')
            ->setParameter('pending', PropertyFieldInterface::STATUS_PENDING)
        ;

        return $queryBuilder
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    public function findArrayByProperty(PropertyInterface $property, $defaultLocale = null, array $ids = [])
    {
        $qb = $this->createQueryBuilder('o')
            ->innerJoin('o.property', 'property')
            ->where('property.id = :property')
            ->setParameter('property', $property)
            ->andWhere('o.status != :pending OR o.status IS NULL')
            ->setParameter('pending', PropertyFieldInterface::STATUS_PENDING)
        ;

        if (!empty($ids)) {
            $qb->andWhere('o.id in (:ids)')
                ->setParameter('ids', $ids);
        }

        if (null !== $defaultLocale) {
            $qb->select('o.id, o.image');
            $this->selectTranslatable($qb, 'o', 'name');
        }
        $this->orderByTranslatable($qb, 'o', 'name', 'ASC');

        return $qb
            ->addOrderBy('o.position', 'ASC')
            ->getQuery()
            ->getScalarResult()
        ;
    }

    public function findByExternalIds(array $externalIds): array
    {
        $qb = $this->getQueryBuilder();

        return $qb
            ->andWhere($qb->expr()->in('o.externalId', ':externalIds'))
            ->setParameter('externalIds', $externalIds)
            ->getQuery()
            ->getResult()
        ;
    }

    public function createFilterBuilder($criteria = [], $sorting = []): QueryBuilder
    {
        $qb = $this->getCollectionQueryBuilder();

        if (empty($sorting)) {
            $sorting = ['id' => 'desc'];
        }

        $this->applyCriteria($qb, $criteria);
        $this->applySorting($qb, $sorting);

        return $qb;
    }

    public function createFilterPaginator($criteria = [], $sorting = []): PagerfantaInterface
    {
        return $this->getPaginator($this->createFilterBuilder($criteria, $sorting));
    }

    public function autocompleteQueryBuilder(array $criteria, array $fields, $term, $indexBy = 'id'): QueryBuilder
    {
        $alias = $this->getAlias();

        // doctrine can't index on translatable field, so add it in select to do indexing manually after
        if ($this->isTranslatableProperty($indexBy)) {
            $qb = $this->createQueryBuilder($alias);
            $this->joinDefaultTranslation($qb);
            $this->joinCurrentTranslation($qb);

            $qb->addSelect("COALESCE(o_translation_current.$indexBy, o_translation_default.$indexBy) AS index");
        } else {
            $qb = $this->createQueryBuilder($alias, $this->getPropertyName($indexBy));
        }

        if (!empty($criteria['createdBy'])) {
            $orx = $qb->expr()->orx(
                sprintf('%s.status != :pending', $alias),
                sprintf('%s.status IS NULL', $alias),
                sprintf('(%s.status = :pending AND %s.createdBy = :createdBy)', $alias, $alias)
            );

            $qb->andWhere($orx)
                ->setParameter(':pending', PropertyFieldInterface::STATUS_PENDING)
                ->setParameter(':createdBy', $criteria['createdBy'])
            ;
        }

        unset($criteria['createdBy']);

        $this->applyCriteria($qb, $criteria);
        $this->applySearchByTerm($qb, $fields, $term);

        return $qb;
    }

    /**
     * Apply search by term.
     *
     * @param string $term
     */
    public function applySearchByTerm(QueryBuilder $qb, array $fields, $term): QueryBuilder
    {
        if (empty($term)) {
            return $qb;
        }

        $alias = $this->getAlias();
        $expr = $qb->expr();
        $orX = $expr->orX();

        $translatables = ['name'];

        // search into translations
        foreach ($translatables as $translatable) {
            $orX->add(
                $this->compareTranslatable($qb, $alias, $translatable, "%$term%", 'like'),
            )

            ;
        }

        foreach ($fields as $field) {
            $fieldName = $field;

            if (!str_contains((string) $field, '.')) {
                // Check translatable properties from entity to search
                if (
                    !\in_array($field, $this->getClassMetadata()->fieldNames)
                    && is_a($this->getClassName(), TranslatableInterface::class, true)
                    && \in_array($field, $this->_em->getClassMetadata($this->getClassName()::getTranslationEntityClass())->fieldNames)
                ) {
                    $orX->add($this->compareTranslatable($qb, $alias, $field, "%$term%", 'like'));
                    continue;
                } else {
                    $fieldName = "$alias.$field";
                }
            }

            $orX->add($expr->like($fieldName, ':_term'));
            $qb->setParameter('_term', "%$term%");
        }

        return $qb->andWhere($orX);
    }

    /**
     * Creates a common criteria for filtering a property fields collection or apply to a querybuilder.
     * -> Retrieves the fields that are allowed for the given company
     *     - operator property fields
     *     - non operator but accepted property fields
     *      pending property fields of the given company.
     */
    public static function createAllowedCompanyCriteria(?CompanyInterface $allowedCompany): Criteria
    {
        $expr = Criteria::expr();
        $orx = [
            $expr->isNull('createdBy'),
            $expr->eq('status', PropertyFieldInterface::STATUS_ACCEPTED),
        ];

        if ($allowedCompany) {
            $orx[] = $expr->andX(
                $expr->eq('status', PropertyFieldInterface::STATUS_PENDING),
                $expr->eq('createdBy', $allowedCompany)
            );
        }

        return Criteria::create()->andWhere($expr->orX(...$orx));
    }

    protected function applyCriteria(QueryBuilder $queryBuilder, array $criteria = null, bool $excludeEmpty = false): void
    {
        $expr = $queryBuilder->expr();

        if ($allowedCompany = $this->extractCriteria($criteria, 'allowed_for_company')) {
            $queryBuilder->addCriteria(self::createAllowedCompanyCriteria($allowedCompany));
        }

        if ($property = $this->extractCriteria($criteria, 'property')) {
            $queryBuilder
                ->andWhere($expr->eq('o.property', ':property'))
                ->setParameter('property', $property)
            ;
        }

        if ($query = $this->extractCriteria($criteria, 'query')) {
            $orX = $expr->orX();
            $orX->add($expr->like('o.id', ':query'));
            $orX->add($expr->like('o.externalId', ':query'));
            $orX->add($this->compareTranslatable($queryBuilder, 'o', 'name', sprintf('%%%s%%', $query), 'like'));

            $queryBuilder->andWhere($orX)->setParameter('query', sprintf('%%%s%%', $query));
        }

        parent::applyCriteria($queryBuilder, $criteria, $excludeEmpty);
    }
}

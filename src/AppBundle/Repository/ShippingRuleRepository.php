<?php

namespace AppBundle\Repository;

use AppBundle\Entity\CurrencyInterface;
use AppB<PERSON>le\Entity\ShippingMethodInterface;
use AppBundle\Entity\ShippingMethodRule;
use AppBundle\Entity\ShippingMethodRuleInterface;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Pagerfanta\Pagerfanta;

/**
 * ShippingRuleRepository repository.
 */
class ShippingRuleRepository extends EntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ShippingMethodRule::class);
    }

    public function createFilterBuilder(ShippingMethodInterface $shippingMethod, array $criteria = [], array $sorting = []): Pagerfanta
    {
        $queryBuilder = $this->getCollectionQueryBuilder();

        $queryBuilder
            ->andWhere('o.method = :id')
            ->setParameter('id', $shippingMethod->getId());

        if (isset($criteria['zones'])) {
            if (\is_array($criteria['zones']) && \count($criteria['zones']) > 0) {
                $queryBuilder->innerJoin('o.zones', 'z');
                $queryBuilder->andWhere('z.id in (:zone)');
                $queryBuilder->setParameter('zone', $criteria['zones']);
            }
            if ($criteria['zones'] instanceof Collection && !$criteria['zones']->isEmpty()) {
                $queryBuilder->innerJoin('o.zones', 'z');
                $queryBuilder->andWhere('z in (:zone)');
                $queryBuilder->setParameter('zone', $criteria['zones']);
            }
        }

        if (isset($criteria['currency'])) {
            $queryBuilder->andWhere('o.currency = :currency')
                ->setParameter('currency', $criteria['currency'])
            ;
        }

        if (isset($criteria['enabled'])) {
            $queryBuilder->andWhere('o.enabled = :enabled')
                ->setParameter('enabled', $criteria['enabled'])
            ;
        } else {
            $queryBuilder->andWhere('o.enabled = 1');
        }

        if (empty($sorting)) {
            if (!\is_array($sorting)) {
                $sorting = [];
            }
            $sorting['id'] = 'desc';
        }

        $this->applySorting($queryBuilder, $sorting);

        return $this->getPaginator($queryBuilder);
    }

    /**
     * Get iterable filter results.
     *
     * @param int $limit
     *
     * @return Pagerfanta
     */
    public function getExportIterableResult(array $criteria = [])
    {
        // always export rules by shippping method
        if (!isset($criteria['method'])) {
            return [];
        }

        $qb = $this->getCollectionQueryBuilder()
            ->select(
                'o.id',
                'method.code as shipping_method_code',
                'z.name as zone',
                "'' as country",
                "'' as province",
                "'' as post_code",
                'o.minWeight as min_weight',
                'o.maxWeight as max_weight',
                '(o.amount / 100) as price',
                'c.code as currency',
                'o.amountByUnit as price_by_kg',
                '(o.priceForExtraUnit / 100) as price_for_extra_unit',
            )
            ->innerJoin('o.method', 'method')
            ->leftJoin('method.owner', 'owner')
            ->innerJoin('o.zones', 'z')
            ->leftJoin('o.currency', 'c')
            ->andWhere('o.enabled = 1')
        ;

        if (!empty($criteria['owner'])) {
            $qb
                ->andWhere('owner = :owner')
                ->setParameter('owner', $criteria['owner'])
            ;

            unset($criteria['owner']);
        }

        $this->applyCriteria($qb, $criteria);

        return $qb
            ->distinct()
            ->getQuery()
            ->iterate()
        ;
    }

    public function findMatchByMethod(
        ShippingMethodInterface $shippingMethod,
        $weight,
        $zones = null,
        CurrencyInterface $currency = null
    ) {
        $zoneIds = [];

        if (null !== $zones) {
            foreach ($zones as $zone) {
                if (null !== $zone) {
                    $zoneIds[] = $zone->getId();
                }
            }
        }

        $queryBuilder = $this->createQueryBuilder('o');

        $queryBuilder->select('o, MIN(o.amount) AS HIDDEN min_amount');

        $queryBuilder
            ->innerJoin('o.method', 'shippingMethod')
            ->leftJoin('o.zones', 'zone')
            ->andWhere('shippingMethod = :shippingMethod')
            ->setParameter('shippingMethod', $shippingMethod)
            ->andWhere('o.enabled = true');

        if (\count($zoneIds) > 0) {
            $queryBuilder
                ->andWhere('(zone.id IS NULL OR (zone.id IN (:zoneIds)))')
                ->setParameter('zoneIds', $zoneIds);
        } else {
            $queryBuilder
                ->andWhere('(zone.id IS NULL)');
        }

        if (null !== $currency) {
            $queryBuilder
                ->leftJoin('o.currency', 'c')
                ->andWhere('(c.id = :currency OR c.id IS NULL)')
                ->setParameter('currency', $currency->getId());
        }
        // Rule weight must match the minWeight
        // And rule weight must be lower than the maxWeight or if has price for extra unit enabled and price greated than 0
        $queryBuilder
            ->andWhere(
                $queryBuilder->expr()->andX(
                    '(o.minWeight IS NULL OR (o.minWeight <= :weight))',
                    $queryBuilder->expr()->orX(
                        'shippingMethod.hasPriceForExtraUnit = 1 AND o.priceForExtraUnit > 0',
                        '(o.maxWeight IS NULL OR (o.maxWeight >= :weight))'
                    )
                )
            )
            ->setParameter('weight', $weight)
        ;

        return $queryBuilder
            ->groupBy('o.id')
            ->getQuery()
            ->getResult()
            ;
    }

    /**
     * Count.
     *
     * @return
     */
    public function getCount(ShippingMethodInterface $method = null)
    {
        $query = $this->getQueryBuilder()
            ->select('COUNT(o)')
            ->andWhere('o.enabled = true');

        if (null !== $method) {
            $query
                ->andWhere('o.method = :method')
                ->setParameter('method', $method);
        }

        return $query
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * @param bool $enabled
     */
    public function findConflictShippingRuleQueryBuilder(ShippingMethodRuleInterface $rule, $enabled = true): QueryBuilder
    {
        $existingZones = $rule->getZones()->filter(fn ($zone) => null !== $zone->getId());

        $qb = $this->getQueryBuilder()
            ->andWhere('o.currency = :currency')
            ->setParameter('currency', $rule->getCurrency())
            ->andWhere('(:maxWeight > o.minWeight AND o.maxWeight > :minWeight)')
            ->setParameter('minWeight', $rule->getMinWeight())
            ->setParameter('maxWeight', $rule->getMaxWeight())
            ->innerJoin('o.zones', 'z')
            ->andWhere('z in (:zone)')
            ->setParameter('zone', $existingZones)
            ->innerJoin('o.method', 'method')
            ->andWhere('method = :method')
            ->setParameter('method', $rule->getMethod());

        if ($enabled) {
            $qb->andWhere('o.enabled = :enabled')
                ->setParameter('enabled', $enabled);
        }

        if ($rule->getId()) {
            $qb->andWhere('o.id != :id')
                ->setParameter('id', $rule->getId());
        }

        return $qb;
    }

    /**
     * @param bool $enabled
     *
     * @return ShippingMethodRuleInterface[]
     */
    public function findConflictShippingRule(ShippingMethodRuleInterface $rule, $enabled = true): array
    {
        return $this->findConflictShippingRuleQueryBuilder($rule, $enabled)->getQuery()->getResult();
    }

    /**
     * @param bool $enabled
     */
    public function hasConflictShippingRule(ShippingMethodRuleInterface $rule, $enabled = true): bool
    {
        $count = (int) $this->findConflictShippingRuleQueryBuilder($rule, $enabled)
            ->select("COUNT('o')")
            ->getQuery()
            ->getSingleScalarResult()
        ;

        return $count > 0;
    }
}

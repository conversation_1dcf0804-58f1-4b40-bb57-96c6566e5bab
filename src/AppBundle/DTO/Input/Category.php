<?php

namespace AppBundle\DTO\Input;

final class Category implements EntityInputInterface
{
    /**
     * The list of category names (translated).
     *
     * @var array<string,string>
     */
    public $name;

    /**
     * The list of category associated names (translated).
     *
     * @var array<string,string>
     */
    public $associatedName;

    /**
     * The list of category descriptions (translated).
     *
     * @var array<string,string>
     */
    public $description;

    /**
     * The list of category meta-titles (translated).
     *
     * @var array<string,string>
     */
    public $metaTitle;

    /**
     * The list of category meta-descriptions (translated).
     *
     * @var array<string,string>
     */
    public $metaDescription;

    /**
     * The slug of the category.
     *
     * @var string
     */
    public $slug;

    /**
     * Whether or not the slug will be overriden.
     */
    private false $overrideSlug = false;

    /**
     * The parent of the category.
     *
     * @var int
     */
    public $parent;

    /**
     * The position of the category.
     *
     * @var int
     */
    public $position;

    /**
     * The external ID of the category.
     *
     * @var string
     */
    public $externalId;

    /**
     * The image of the category.
     *
     * @var string
     */
    public $image;

    /**
     * The banner of the category.
     *
     * @var string
     */
    public $banner;

    /**
     * The path of the category.
     *
     * @var string
     */
    public $path;

    /**
     * Whether or not the category products is searchable.
     *
     * @var bool
     */
    public $searchable = true;

    /**
     * Whether or not the category products are visible.
     *
     * @var bool
     */
    public $visible = true;

    /**
     * Whether or not the category products are orderable.
     *
     * @var bool
     */
    public $orderable = true;

    /**
     * The meta robot of the category.
     *
     * @var array
     */
    public $metaRobot;

    /**
     * The taxon root.
     *
     * @var Taxon
     */
    public $root;
}

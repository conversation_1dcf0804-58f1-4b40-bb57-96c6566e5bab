<?php

namespace AppBundle\DTO\Output;

use Doctrine\Common\Collections\ArrayCollection;

final class Variant implements EntityOutputInterface
{
    /**
     * The variant id.
     *
     * @var int
     */
    public $id;

    /**
     * Wether or not the variant is the master one or not.
     *
     * @var bool
     */
    public $master;

    /**
     * The variant product.
     *
     * @var Product
     */
    public $product;

    /**
     * The list of variant options values.
     *
     * @var ArrayCollection<OptionValue>
     */
    public $optionValues;

    /**
     * The list of variant options values ids.
     *
     * @var array
     */
    public $optionValuesIds;

    /**
     * The on hold quantity.
     *
     * @var int
     */
    public $onHold;

    /**
     * The on hand quantity.
     *
     * @var int
     */
    public $onHand;

    /**
     * The stock.
     *
     * @var int
     */
    public $stock;

    /**
     * Wether or not the variant is available on demand.
     *
     * @var bool
     */
    public $availableOnDemand;

    /**
     * The variant list of images of the products variants (attached to the master variant).
     *
     * @var ArrayCollection<VariantImage>
     */
    public $images;

    /**
     * The variant SKU.
     *
     * @var string
     */
    public $sku;

    /**
     * The variant EAN.
     *
     * @var string
     */
    public $ean;

    /**
     * The variant UPC.
     *
     * @var string
     */
    public $upc;

    /**
     * The variant harmonized system, used to identify products for international import purposes.
     *
     * @var string
     */
    public $hs;

    /**
     * The variant UID.
     *
     * @var string
     */
    public $uid;

    /**
     * The variant height.
     *
     * @var float
     */
    public $height;

    /**
     * The variant width.
     *
     * @var float
     */
    public $width;

    /**
     * The variant depth.
     *
     * @var float
     */
    public $depth;

    /**
     * The variant weight.
     *
     * @var float
     */
    public $weight;

    /**
     * The list of variant prices.
     *
     * @var ArrayCollection<VariantPrice>
     */
    public $prices;

    /**
     * The variant user price.
     *
     * @var VariantPrice
     */
    public $price;

    /**
     * The variant user price without promotions.
     *
     * @var VariantPrice
     */
    public $basePrice;

    /**
     * The date from which the product is available (set on master variant).
     *
     * @var \DateTime
     */
    public $orderableFrom;

    /**
     * The date until which the product is available (set on master variant).
     *
     * @var \DateTime
     */
    public $orderableTo;

    /**
     * The date from which the product is visible (set on master variant).
     *
     * @var \DateTime
     */
    public $visibleFrom;

    /**
     * The date until which the product is visible (set on master variant).
     *
     * @var \DateTime
     */
    public $visibleTo;

    /**
     * Wether or not the stock is visible.
     *
     * @var bool
     */
    public $visibleStock;

    /**
     * The quantity of a pack of the variant.
     *
     * @var float
     */
    public $packQuantity;

    /**
     * The variant quantity by master product unit.
     *
     * @var int
     */
    public $masterQuantity;

    /**
     * The list of tax unit quantities applyable to the variant.
     *
     * @var ArrayCollection<VariantTaxUnitQuantity>
     */
    public $taxUnitQuantities;

    /**
     * The variant deletion date.
     *
     * @var \DateTime
     */
    public $deletedAt;

    /**
     * The variant metadata.
     *
     * @var ArrayCollection<VariantMetadata>
     */
    public $metadata;

    /**
     * The variant restocks.
     *
     * @var ArrayCollection<VariantRestock>
     */
    public $restocks;

    /**
     * The variant quantity by unit.
     *
     * @var int
     */
    public $unitQuantity;

    /**
     * Wether or not the variant is enabled.
     *
     * @var bool
     */
    public $enabled;

    /**
     * The variant state condition for product rental (as_new|used|damaged|incomplete|out_of_usage).
     *
     * @var string
     */
    public $rentState;

    /**
     * The variant creation date.
     *
     * @var \DateTime
     */
    public $createdAt;

    /**
     * The variant updated date.
     *
     * @var \DateTime
     */
    public $updatedAt;

    /**
     * The variant external id.
     *
     * @var string
     */
    public $externalId;
}

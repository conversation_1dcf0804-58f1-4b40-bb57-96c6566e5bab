<?php

namespace AppBundle\Gateway\ShippingMethod;

use AppBundle\Attribute\GatewaySetup as AttributeGatewaySetup;
use AppBundle\Calculator\Shipment\ShipmentCalculatorInterface;
use AppBundle\Calculator\ShipmentDelegatingCalculator;
use AppBundle\Entity\AddressInterface;
use AppBundle\Entity\CartInterface;
use AppBundle\Entity\CompanyInterface;
use AppBundle\Entity\FileEntity;
use AppBundle\Entity\GatewaySetup;
use AppBundle\Entity\GatewaySetupInterface;
use AppBundle\Entity\OrderInterface;
use AppBundle\Entity\OrderItem;
use AppBundle\Entity\OrderItemInterface;
use AppBundle\Entity\Shipment;
use AppBundle\Entity\ShipmentInterface;
use AppBundle\Entity\ShippableInterface;
use AppBundle\Entity\ShippingCategoryInterface;
use AppBundle\Entity\ShippingDeliveryTimeInterface;
use AppBundle\Entity\ShippingMethodInterface;
use AppBundle\Entity\VariantInterface;
use AppBundle\Event\UpplerVisibleTypes;
use AppBundle\Exception\UpelaGatewayException;
use AppBundle\Factory\ShipmentFactoryInterface;
use AppBundle\FileHandler\FileUploader;
use AppBundle\Form\Type\Resource\Commons\Shipment\ShippingGatewayUpelaSetupType;
use AppBundle\Matcher\ZoneMatcherInterface;
use AppBundle\Model\BankCurrencyInterface;
use AppBundle\Model\Shipment\Resolve\ItemResolve;
use AppBundle\Provider\ConfigurationProviderInterface;
use AppBundle\Provider\FileProvider;
use AppBundle\Tools\Encryptor;
use AppBundle\Tools\StringFormatter;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use OAuth2\Client;
use Psr\Log\LoggerInterface;
use Psr\Log\LogLevel;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * API shipment based on UPELA API : https://apiv3.doc.upela.com/.
 **/
#[AttributeGatewaySetup(form: ShippingGatewayUpelaSetupType::class)]
class UpelaShippingMethodGateway extends AbstractShippingMethodGateway
{
    private ?CompanyInterface $company = null;
    protected const ROUTE_LOGIN = 'login/';
    protected const ROUTE_RATE = 'rate/';
    protected const ROUTE_SELECT_OFFER = 'select_offer/';
    protected const ROUTE_SHIP = 'ship/';
    protected const ROUTE_PICK_UP = 'pickup/';
    protected const ADDRESS_FROM = 'from';
    protected const ADDRESS_TO = 'to';

    public function __construct(
        LoggerInterface $logger,
        ShipmentDelegatingCalculator $calculator,
        ShipmentFactoryInterface $shipmentFactory,
        protected readonly Client $client,
        protected readonly ?string $endpoint,
        protected readonly ZoneMatcherInterface $zoneMatcher,
        protected readonly EntityManagerInterface $entityManager,
        protected readonly FileProvider $fileProvider,
        protected readonly FileUploader $fileUploader,
        protected readonly ConfigurationProviderInterface $configurationProvider,
        protected readonly ?string $passphrase,
        protected readonly TranslatorInterface $translator
    ) {
        parent::__construct($logger, $calculator, $shipmentFactory);
    }

    public function getName(): string
    {
        return ShippingMethodGatewayInterface::UPELA;
    }

    /**
     * @throws UpelaGatewayException
     */
    public function checkCredential(array $credentials, string $salt): bool
    {
        $result = $this->fetchLogin($this->formatLoginParameters($credentials, $salt));

        // We use '==' and 'true' because we can't be sur the response is a boolean or a string
        return 'true' == $result['success'];
    }

    public function support(ShippingMethodInterface $shippingMethod, ShippableInterface $shippable, ShippingCategoryInterface $shippingCategory = null, AddressInterface $shippingAddress = null, ShippableInterface $item = null): array
    {
        $rule = null;
        $note = '';
        $compatibility = false;

        if (null === $shippingAddress) {
            $note = ItemResolve::COMPATIBILITY_NO_WITHOUT_ADDRESS;
        } elseif (ShipmentCalculatorInterface::FREE_ORDER === $shippingMethod->getCalculator()) {
            $compatibility = true;
        }

        return [$compatibility, $rule, $note];
    }

    private function formatLoginParameters(array $credentials, string $salt): array
    {
        return [
            'account' => [
                'login' => $credentials['login'],
                'password' => Encryptor::decrypt($credentials['password'], $this->passphrase, $salt, Encryptor::AES_256_GCM),
            ],
        ];
    }

    /**
     * Because all fields must be filled to call the api, this method is used to check if all filed are correctly set.
     * If only one field is empty, return false, otherwise return true.
     */
    private function isShippingAddressComplete(ShippableInterface $shippable): bool
    {
        if ($shippable instanceof OrderItemInterface) {
            $shippable = $shippable->getOrder();
        }

        $addresses = [
            $this->addressToArray($shippable), // Retrieve "from" address
            $this->addressToArray($shippable, self::ADDRESS_TO), // Retrieve "to" address
        ];

        foreach ($addresses as $address) {
            foreach ($address as $addressProperty) {
                if (empty($addressProperty)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Use to generate address to send to the api, ensure all fields are set, because they are mandatory by the api.
     */
    private function addressToArray(OrderInterface $order, string $addressType = self::ADDRESS_FROM): array
    {
        if (self::ADDRESS_FROM === $addressType) {
            $address = $order->getSellerAddress();
            $company = $order->getCompany();
        } else {
            $address = $order->getShippingAddress();
            $company = $order->getCompanyReceiver();
        }

        return [
            'company' => $address?->getCompany() ?: $company?->getName() ?: '',
            'name' => $address?->getFullName() ?: $company?->getUser()?->getFullName() ?: '',
            'phone' => $address?->getPhone() ?: $address?->getMobile() ?: $company?->getPhone() ?: $company?->getUser()?->getPhone() ?: '',
            'address1' => $address?->getStreet() ?: '',
            'email' => $address?->getEmail() ?: $company?->getEmail() ?: $company?->getUser()?->getEmail() ?: '',
            'country_code' => $address?->getCountry()?->getIsoName() ?: '',
            'postcode' => $address?->getPostcode() ?: '',
            'city' => $address?->getCity() ?: '',
            'pro' => 1,
        ];
    }

    protected function hasDocumentsToFetch(Shipment $shipment, OrderInterface $order): bool
    {
        return !$this->entityManager->getRepository(FileEntity::class)->hasBy([
            'fileOwnerType' => OrderInterface::NAME,
            'fileOwnerId' => $order->getFileOwnerId(),
            'fileNameOrType' => $shipment->getMethodName(),
        ]);
    }

    /**
     * @throws UpelaGatewayException
     */
    protected function getLoginParameters(CompanyInterface $company): array
    {
        /** @var ?GatewaySetup $shippingGatewaySetup */
        $shippingGatewaySetup = $this->entityManager->getRepository(GatewaySetup::class)->findAvailableCredentials(
            $company,
            $this::UPELA,
            GatewaySetupInterface::GATEWAY_TYPE_SHIPPING,
        );

        if (!$shippingGatewaySetup instanceof GatewaySetup) {
            throw new UpelaGatewayException(UpelaGatewayException::GATEWAY_NO_CREDENTIAL_ERROR);
        }

        return $this->formatLoginParameters($shippingGatewaySetup->getCredentials(), $shippingGatewaySetup->getSalt());
    }

    /**
     * @throws UpelaGatewayException
     */
    protected function fetch(string $urlPath, CompanyInterface $company, array $parameters = [], string $httpMethod = Client::HTTP_METHOD_GET, array $httpHeaders = [], string $formContentType = Client::HTTP_FORM_CONTENT_TYPE_MULTIPART): array
    {
        $parameters = array_merge($this->getLoginParameters($company), $parameters);
        $url = $this->endpoint.$urlPath;

        try {
            $response = $this->client->fetch(
                $url,
                // If the header should be a json, transform it
                \in_array('application/json', $httpHeaders, true) ? json_encode($parameters, JSON_THROW_ON_ERROR) : $parameters,
                $httpMethod,
                $httpHeaders,
                $formContentType
            );

            // If the result is not an array, consider that the response contains a fatal error from Upela API.
            if (!\is_array($response['result'])) {
                throw new UpelaGatewayException(UpelaGatewayException::GATEWAY_RESPONSE_ERROR, ['response' => $response]);
            }

            // If upela return an error, throw only the details
            if (!empty($response['result']['errors'])) {
                $upelaGatewayException = new UpelaGatewayException(UpelaGatewayException::GATEWAY_BAD_REQUEST_ERROR, ['errors' => $response['result']['errors']]);
                $upelaGatewayException->translateMessage($this->translator);

                throw $upelaGatewayException;
            }

            $this->log(LogLevel::NOTICE, ['url' => $url, 'response' => $response]);

            return $response['result'] ?: [];
        } catch (\Exception $e) {
            $details = [
                'url' => $url,
                'exception' => $e,
                'parameters' => $parameters,
            ];

            if ($e instanceof UpelaGatewayException) {
                $details = array_merge($details, $e->getDetails());
            }
            $this->log(LogLevel::ERROR, $details);

            /* Transform an exception into an UpelaGatewayException to be sure the correct error message will be displayed,
             * no 500 will be thrown if there is a problem with the gateway
             */
            if (!$e instanceof UpelaGatewayException) {
                $e = new UpelaGatewayException(UpelaGatewayException::GATEWAY_FETCH_ERROR, $details, Response::HTTP_INTERNAL_SERVER_ERROR, $e);
            }

            throw $e;
        }
    }

    /**
     * @throws UpelaGatewayException
     */
    protected function fetchLogin(array $parameters = []): array
    {
        $url = $this->endpoint.self::ROUTE_LOGIN;

        try {
            $response = $this->client->fetch(
                $url,
                json_encode($parameters, JSON_THROW_ON_ERROR),
                Client::HTTP_METHOD_POST,
                ['Content-Type' => 'application/json']
            );

            // If the result is not an array, consider that the response contains a fatal error from Upela API.
            if (!\is_array($response['result'])) {
                throw new UpelaGatewayException(UpelaGatewayException::GATEWAY_RESPONSE_ERROR, $response);
            }

            return $response['result'] ?: [];
        } catch (\Exception $e) {
            $this->log(LogLevel::ERROR, ['exception' => $e]);

            /* Transform an exception into an UpelaGatewayException to be sure the correct error message will be
             * displayed, no 500 will be thrown if there is a problem with the gateway
             */
            if (!$e instanceof UpelaGatewayException) {
                $details = [
                    'url' => $url,
                    'parameters' => $parameters,
                    'method' => Client::HTTP_METHOD_POST,
                ];

                $e = new UpelaGatewayException(UpelaGatewayException::GATEWAY_FETCH_ERROR, $details, Response::HTTP_INTERNAL_SERVER_ERROR, $e);
            }

            throw $e;
        }
    }

    /**
     * @throws UpelaGatewayException
     */
    protected function fetchRate(array $data, CompanyInterface $company): array
    {
        return $this->fetch(self::ROUTE_RATE, $company, $data, Client::HTTP_METHOD_POST, ['Content-Type' => 'application/json']);
    }

    /**
     * @throws UpelaGatewayException
     */
    protected function fetchShip(array $data, CompanyInterface $company): array
    {
        return $this->fetch(self::ROUTE_SHIP, $company, $data, Client::HTTP_METHOD_POST, ['Content-Type' => 'application/json']);
    }

    /**
     * @throws UpelaGatewayException
     */
    protected function fetchPickUp(array $data, CompanyInterface $company): array
    {
        return $this->fetch(self::ROUTE_PICK_UP, $company, $data, Client::HTTP_METHOD_POST, ['Content-Type' => 'application/json']);
    }

    /**
     * @throws UpelaGatewayException
     */
    protected function fetchSelectOffer(array $data, CompanyInterface $company): array
    {
        return $this->fetch(self::ROUTE_SELECT_OFFER, $company, $data, Client::HTTP_METHOD_POST, ['Content-Type' => 'application/json']);
    }

    /**
     * Check if there are credentials available for the gateway.
     */
    public function isCredentialAvailable(CompanyInterface $company): bool
    {
        /** @var ?GatewaySetup $shippingGatewaySetup */
        $shippingGatewaySetup = $this->entityManager->getRepository(GatewaySetup::class)->findAvailableCredentials(
            $company,
            $this::UPELA,
            GatewaySetupInterface::GATEWAY_TYPE_SHIPPING,
        );

        return
            $shippingGatewaySetup instanceof GatewaySetup
            && isset($shippingGatewaySetup->getCredentials()['login'], $shippingGatewaySetup->getCredentials()['password'])
        ;
    }

    /**
     * @throws UpelaGatewayException
     */
    public function calculate(
        ShippingMethodInterface $shippingMethod,
        ShippableInterface $shippable,
        Collection $items,
        AddressInterface $fromAddress = null,
        AddressInterface $toAddress = null,
        $force = false,
        BankCurrencyInterface $currency = null
    ): ?ShipmentInterface {
        // If the address can't be fulfilled, do not return a shipping method
        if ($shippable instanceof CartInterface || !$this->isShippingAddressComplete($shippable)) {
            return null;
        }

        if ($force || !($shipment = $shippable->getShipmentByShippingMethod($shippingMethod)) instanceof ShipmentInterface) {
            $shipment = $this->shipmentFactory->createShipment();
            $shipment->setShippingMethod($shippingMethod);
        }

        if ($shippable instanceof OrderInterface) {
            $rateData = $this->getShipmentRate($shippable, $items);

            foreach ($rateData['offers'] ?? [] as $offer) {
                if ($shippingMethod->getCode() !== $offer['carrier_code']) {
                    continue;
                }

                foreach ($items as $item) {
                    $this->shipmentFactory->getOrCreateShipmentItem(
                        $shipment,
                        $item->getVariant(),
                        null,
                        $item->getQuantity(),
                        $item->getTotalWeight()
                    );
                }

                if (!empty($offer['price_incl_tax'])) {
                    $shipment->setAmount($offer['price_incl_tax'] * 100);
                }

                if (!empty($offer['service_name']) || !empty($offer['carrier_name'])) {
                    $methodName = trim(($offer['carrier_name'] ?? '').' '.($offer['service_name'] ?? ''));
                    $shipment->setMethodName($methodName);
                }

                if (!empty($offer['id'])) {
                    $shipment->setMethodExternalId($offer['id']);
                }

                if (!empty($rateData['shipment_id'])) {
                    $shipment->setExternalId($rateData['shipment_id']);
                }
            }
        }

        // Do not return shipment if api do not provide an offer id and shipment id
        if (\is_null($shipment->getMethodExternalId()) && \is_null($shipment->getExternalId())) {
            return null;
        }

        return $shipment;
    }

    public function findDeliveryTime(ShippingMethodInterface $shippingMethod, AddressInterface $address): ?ShippingDeliveryTimeInterface
    {
        foreach ($shippingMethod->getDeliveryTimes() as $deliveryTime) {
            if (
                $deliveryTime->getZones()->isEmpty()
                || $this->zoneMatcher->hasCommonZonesByAddress($address, $deliveryTime->getZones()->toArray(), $shippingMethod->getOwner())
            ) {
                return $deliveryTime;
            }
        }

        return null;
    }

    /**
     * For decorator see {@see UpelaShippingMethodGatewayDecorator::getShipmentRate}.
     *
     * @param Collection|OrderItemInterface[] $items
     *
     * @throws UpelaGatewayException
     */
    public function getShipmentRate(OrderInterface $shippable, iterable $items): array
    {
        $itemsData = [];
        foreach ($items as $item) {
            // If no variant is defined for the current order item, no need to continue
            if (!$item->getVariant() instanceof VariantInterface) {
                continue;
            }

            $itemsData[] = [
                'number' => $item->getQuantity(),
                'weight' => $item->getVariant()->getWeight(),
                'x' => $item->getVariant()->getHeight(),
                'y' => $item->getVariant()->getWidth(),
                'z' => $item->getVariant()->getDepth(),
            ];
        }

        // if there are no items to send, no need to continue
        if (empty($itemsData)) {
            return [];
        }

        $date = new \DateTime();
        $date->add(new \DateInterval('P7D'));

        $data = [
            'ship_from' => [
                'country_code' => $shippable->getSellerAddress()?->getCountry()?->getIsoName(),
                'postcode' => $shippable->getSellerAddress()?->getPostcode(),
                'city' => $shippable->getSellerAddress()?->getCity(),
            ],
            'ship_to' => [
                'country_code' => $shippable->getShippingAddress()?->getCountry()?->getIsoName(),
                'postcode' => $shippable->getShippingAddress()?->getPostcode(),
                'city' => $shippable->getShippingAddress()?->getCity(),
            ],
            'parcels' => $itemsData,
            'shipment_date' => $date->format('Y-m-d'),
        ];

        return $this->fetchRate($data, $shippable->getCompanyReceiver());
    }

    /**
     * @throws UpelaGatewayException
     */
    public function sendSelectedOfferInformation(Shipment $shipment): array
    {
        return $this->fetchSelectOffer([
            'shipment_id' => $shipment->getExternalId(),
            'offer_id' => $shipment->getMethodExternalId(),
        ], $shipment->getOrder()?->getCompanyReceiver());
    }

    /**
     * @throws UpelaGatewayException
     */
    public function sendShipInformation(Shipment $shipment, OrderInterface $order, ?bool $flush = true): array
    {
        $content = [];

        /** @var OrderItem $item */
        foreach ($order->getValidatedItems() as $item) {
            // Check if the items have family to fill the content type for the api
            if (!\is_null($family = $item->getProduct()->getFamily())) {
                $content[] = $family->getName();
            }
        }

        $shipmentInformation = $this->fetchShip([
            'shipment_id' => $shipment->getExternalId(),
            'ship_from' => $this->addressToArray($order),
            'ship_to' => $this->addressToArray($order, self::ADDRESS_TO),
            'reason' => 'Shipping order',
            'content' => implode(', ', array_unique($content)) ?: 'Merchandising',
        ], $order->getCompanyReceiver());

        // Keep the tracking number to propose a tracking url
        if (!empty($shipmentInformation['tracking_number'])) {
            $shipment->setTrackingCode($shipmentInformation['tracking_number']);
        }

        // Keep the waybill to retrieve the pdf with delivery from
        if (!empty($shipmentInformation['waybill']['code']) && !empty($shipmentInformation['waybill']['url'])) {
            $path = $this->fileUploader->generateFilename('pdf');
            [$thumbUrl, $thumbPath] = $this->fileUploader->createThumbFromPath($path);
            $waybill = $this->fileProvider->createFile(
                $this->fileUploader->uploadFromContent($path, file_get_contents($shipmentInformation['waybill']['url']), 'application/pdf'),
                $shipmentInformation['waybill']['code'].'.pdf',
                $thumbPath,
                $order
            );
            $waybill->setDisplayName($shipment->getMethodName().'_'.$shipmentInformation['waybill']['code'].'.pdf');
            $waybill->setVisibility(UpplerVisibleTypes::VISIBLE_TYPE_CONTACT);
            $this->entityManager->persist($waybill);
        }

        $this->entityManager->persist($shipment);

        if ($flush) {
            $this->entityManager->flush();
        }

        return $shipmentInformation;
    }

    /**
     * @throws UpelaGatewayException
     */
    public function sendPickUpInformation(Shipment $shipment, OrderInterface $order): array
    {
        return $this->fetchPickUp([
            'shipment_id' => $shipment->getExternalId(),
            'ship_from' => $this->addressToArray($order),
            'reason' => 'Shipping order',
            'content' => 'Merchandising',
            'label_format' => 'PDF',
        ], $order->getCompanyReceiver());
    }

    public function allowsRecoveryDocuments(): bool
    {
        return true;
    }

    /**
     * @throws UpelaGatewayException
     */
    public function getDocuments(Shipment $shipment, OrderInterface $order, ?bool $flush = true): array
    {
        if (
            !$this->isShippingAddressComplete($order)
            || !$this->hasDocumentsToFetch($shipment, $order)
        ) {
            return [];
        }

        return $this->sendShipInformation($shipment, $order, $flush);
    }

    public function log(string $level, array $context = []): void
    {
        // Check if there is sensitive information before log them
        if (isset($context['parameters']['account']['login'])) {
            $context['parameters']['account']['login'] = StringFormatter::sensitive($context['parameters']['account']['login'], ['isEmail' => true]);
        }

        if (isset($context['parameters']['account']['password'])) {
            $context['parameters']['account']['password'] = StringFormatter::sensitive($context['parameters']['account']['password'], ['full' => true]);
        }

        parent::log($level, $context);
    }

    public function allowNonFreeCalculator(): bool
    {
        return false;
    }
}

<?php

namespace AppBundle\Provider\Cms;

use AppBundle\Entity\CompanyMatcherInterface;
use AppBundle\Entity\PageInterface;
use AppBundle\Gateway\Page\PageGatewayInterface;

/**
 * Interface provides the page.
 */
interface PageProviderInterface
{
    public function registerGateway(PageGatewayInterface $gateway, string $alias);

    public function getPageGateway(string $alias = null);

    public function invalidateCachePage(PageInterface $page);

    public function findPageByUrl(?string $url, ?string $type = PageInterface::TYPE_PAGE, ?bool $ignoreAnonymousRole = false): ?PageInterface;

    public function findAllPageByUrl($url, array $criteria = []);

    public function findPatternByUrl($url);

    public function canSeePageByUrl($url);

    public function canSeeByPage(PageInterface $page);

    public function findUrlByPage(PageInterface $page);

    public function getAuthenticatedOwner();

    public function isPageModified(PageInterface $page);

    public function findRolesByUrl($url, PageInterface $page, array $criteria);

    public function renderExternalPage(string $gatewayAlias = null, array $context = []);

    public function findByCompanyEligibility(CompanyMatcherInterface $companyMatcher);
}

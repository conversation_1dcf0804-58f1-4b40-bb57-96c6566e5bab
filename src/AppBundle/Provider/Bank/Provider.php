<?php

namespace AppBundle\Provider\Bank;

use AppBundle\Client\Bank\BankClientInterface;
use AppBundle\Context\CurrencyContextInterface;
use AppBundle\Entity\Address;
use AppBundle\Entity\BankConfigurationInterface;
use AppBundle\Entity\CartInterface;
use AppBundle\Entity\CompanyInterface;
use AppBundle\Entity\FeeInterface;
use AppBundle\Entity\OrderInterface;
use AppBundle\Entity\Payment;
use AppBundle\Entity\PaymentInterface;
use AppBundle\Entity\UserInterface;
use AppBundle\Event\UpplerBankEvents;
use AppBundle\FileHandler\UploaderClientInterface;
use AppBundle\Gateway\Payment\PaymentGatewayInterface;
use AppBundle\Model\BankRefererInterface;
use AppBundle\Model\PayerInterface;
use AppBundle\Model\UrlTokenRefererInterface;
use AppBundle\Provider\ConfigurationProviderInterface;
use AppBundle\Resolver\Shop\TaxRateResolverInterface;
use AppBundle\Security\UrlToken\Factory as UrlTokenFactory;
use AppBundle\Twig\Extension\MoneyExtension;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Pagerfanta\Pagerfanta;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\EventDispatcher\GenericEvent;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Translation\TranslatorInterface;
use Psr\Log\LoggerInterface;

/**
 * Provides bank action.
 */
class Provider implements ProviderInterface
{
    protected $configurationProvider;

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly SessionInterface $session,
        private readonly TokenStorageInterface $tokenStorage,
        private readonly UrlTokenFactory $urlTokenFactory,
        private readonly TranslatorInterface $translator,
        private readonly UploaderClientInterface $fileUploader,
        ConfigurationProviderInterface $configurationProvider,
        private readonly EventDispatcherInterface $eventDispatcher,
        private readonly EntityRepository $userRepository,
        private readonly EntityRepository $companyRepository,
        private readonly EntityRepository $paymentRepository,
        private readonly EntityRepository $bankConfigurationRepository,
        private readonly EntityRepository $currencyRepository,
        private readonly CurrencyContextInterface $currencyContext,
        private readonly TaxRateResolverInterface $taxRateResolver,
        private readonly MoneyExtension $twigMoney,
        private readonly LoggerInterface $logger,
        private $ownerBankId,
        private $walletOwnerBankId,
        private $referer,
        private $payer
    ) {
        $this->configurationProvider = $configurationProvider;
    }

    public function getWalletOwnerBankId()
    {
        return $this->walletOwnerBankId;
    }

    public function setExternalId(PayerInterface $payer, $externalId)
    {
        $payer->setExternalId($externalId);

        $this->entityManager->persist($payer);
        $this->entityManager->flush();
    }

    public function setBankExternalCode(PayerInterface $payer, $bankExternalCode)
    {
        $user = $payer->getUser();

        $user->setBankExternalCode($bankExternalCode);

        $this->entityManager->persist($user);
        $this->entityManager->flush();
    }

    public function getBankExternalCode(PayerInterface $payer)
    {
        return $payer->getUser()?->getBankExternalCode();
    }

    public function setBankExternalCodeSecond(PayerInterface $payer, $bankExternalCode)
    {
        $user = $payer->getUser();

        $user->setBankExternalCodeSecond($bankExternalCode);

        $this->entityManager->persist($user);
        $this->entityManager->flush();
    }

    public function getBankExternalCodeSecond(PayerInterface $payer)
    {
        $user = $payer->getUser();

        return $user->getBankExternalCodeSecond();
    }

    public function setBankAccountCode(PayerInterface $payer, $bankAccountCode)
    {
        $user = $payer->getUser();

        $user->setBankAccountCode($bankAccountCode);

        $this->entityManager->persist($user);
        $this->entityManager->flush();
    }

    public function getBankAccountCode(PayerInterface $payer)
    {
        $user = $payer->getUser();

        return $user->getBankAccountCode();
    }

    public function setBankCode(PayerInterface $payer, $bankCode)
    {
        $user = $payer->getUser();

        $user->setBankCode($bankCode);

        $this->entityManager->persist($user);
        $this->entityManager->flush();
    }

    public function getBankCode(PayerInterface $payer)
    {
        $user = $payer->getUser();

        return $user->getBankCode();
    }

    public function setBankClientPostCode(PayerInterface $payer, $bankClientPostCode)
    {
        $user = $payer->getUser();

        $user->setBankClientPostCode($bankClientPostCode);

        $this->entityManager->persist($user);
        $this->entityManager->flush();
    }

    public function getBankClientPostCode(PayerInterface $payer)
    {
        $user = $payer->getUser();

        return $user->getBankClientPostCode();
    }

    public function findRefererByPayment(PaymentInterface $payment)
    {
        return $this->findRefererByIdAndType($payment->getRefererId(), $payment->getRefererType());
    }

    public function isPayable(BankRefererInterface $referer)
    {
        if (null !== $referer->getCurrency()) {
            return $this->currencyRepository->isPayableByCode($referer->getCurrency());
        }

        return false;
    }

    public function findPayerByPayment(PaymentInterface $payment): ?PayerInterface
    {
        return $this->findPayerByIdAndType($payment->getPayerId(), $payment->getPayerType());
    }

    public function findRefererByIdAndType($id, $type)
    {
        if (isset($this->referer[$type])) {
            $repository = $this->entityManager->getRepository($this->referer[$type]);

            return $repository->find($id);
        }

        return;
    }

    public function findPayerByIdAndType($id, $type): ?PayerInterface
    {
        if (isset($this->payer[$type])) {
            $repository = $this->entityManager->getRepository($this->payer[$type]);

            return $repository->find($id);
        }

        return null;
    }

    public function getPayoutPayments(PaymentInterface $payment)
    {
        return $this->paymentRepository->findBy(['payoutId' => $payment->getId()]);
    }

    public function findPayerByKycState($state, $payerType = null)
    {
        if (null === $payerType) {
            $payerType = reset($this->payer);
        } elseif (isset($this->payer[$payerType])) {
            $payerType = $this->payer[$payerType];
        } else {
            return;
        }

        $repository = $this->entityManager->getRepository($payerType);

        return $repository->findBy(['kycState' => $state]);
    }

    /**
     * Payment choice.
     *
     * @return Company
     */
    public function getTaxRateApplicable(PaymentInterface $payment, BankRefererInterface $referer = null, PayerInterface $payer = null, $vatOnly = false)
    {
        // If payer and referer not set, get from payment
        if (null === $payer) {
            $payer = $this->findPayerByPayment($payment);
        }
        if (null === $referer) {
            $referer = $this->findRefererByPayment($payment);
        }

        // Can't find applicable taxRate if no payer
        if (null === $payer || null === $referer) {
            return;
        }

        $taxCategory = null;
        // Get taxCategory from fee if exists
        if ($payment->getFee() instanceof FeeInterface) {
            $taxCategory = $payment->getFee()->getTaxCategory();
        }

        if (
            // No applicable taxRate for order
            (
                !$referer instanceof OrderInterface
                && !$referer instanceof CartInterface
                && BankClientInterface::TYPE_PAYOUT !== $payment->getType()
            )
            // Or calculate if it fee
            || BankClientInterface::TYPE_FEE === $payment->getType()
        ) {
            return $this->taxRateResolver->getTaxeRateByConfiguration($payer, $taxCategory, $vatOnly);
        }

        return;
    }

    /**
     * Payment choice.
     *
     * @return Company
     */
    public function hasPaymentChoice()
    {
        return $this->configurationProvider->hasPaymentChoice();
    }

    public function getInvoiceExternalFormat()
    {
        $configuration = $this->getConfiguration();

        if ($configuration instanceof BankConfigurationInterface) {
            return $configuration->getInvoiceExternalFormat();
        }

        return;
    }

    /**
     * {@inheritdoc}
     */
    public function getUserByBankExternalCode($bankExternalCode)
    {
        return $this->userRepository->findOneBy(['bankExternalCode' => $bankExternalCode]);
    }

    /**
     * {@inheritdoc}
     */
    public function getPayerById($id)
    {
        return $this->companyRepository->find($id);
    }

    /**
     * {@inheritdoc}
     */
    public function getPayerWithKycState($state)
    {
        return $this->companyRepository->findBy(['kycState' => $state]);
    }

    /**
     * Get bank configuration.
     */
    public function getConfiguration(): BankConfigurationInterface
    {
        return $this->bankConfigurationRepository->findOneBy([]);
    }

    public function getSigle()
    {
        $configuration = $this->getConfiguration();

        if ($configuration instanceof BankConfigurationInterface) {
            return $configuration->getSigle();
        }

        return 'MKP';
    }

    public function getOperatorSigle()
    {
        $configuration = $this->getConfiguration();

        if ($configuration instanceof BankConfigurationInterface) {
            return $configuration->getOperatorSigle();
        }

        return 'OWN';
    }

    public function hasCartPaymentMethod()
    {
        $configuration = $this->getConfiguration();

        if ($configuration instanceof BankConfigurationInterface) {
            return $configuration->getHasCartPaymentMethod();
        }

        return false;
    }

    public function hasCreditInsurance()
    {
        $configuration = $this->getConfiguration();

        if ($configuration instanceof BankConfigurationInterface) {
            return $configuration->getHasCreditInsurance();
        }

        return false;
    }

    public function hasMethodCardChoice()
    {
        $configuration = $this->getConfiguration();

        if ($configuration instanceof BankConfigurationInterface) {
            return $configuration->getHasMethodCardChoice();
        }

        return false;
    }

    public function canDownload(PaymentInterface $payment): bool
    {
        if (!$this->findRefererByPayment($payment) instanceof OrderInterface && null === $payment->getFilePath()) {
            return false;
        }

        return $this->canDownloadByType($payment->getDisplayType());
    }

    public function canDownloadByType($type)
    {
        $configuration = $this->getConfiguration();

        if ($configuration instanceof BankConfigurationInterface) {
            $types = $configuration->getDownloadType();

            // force to set, to generate good number invoice
            if (null === $types) {
                $types = [
                    BankClientInterface::TYPE_FEE,
                    BankClientInterface::TYPE_FEE_REFUND,
                    BankClientInterface::TYPE_CREDIT,
                    BankClientInterface::TYPE_SUBSCRIPTION,
                    BankClientInterface::TYPE_ORDER,
                    BankClientInterface::TYPE_REFUND,
                ];
            }

            if (\is_array($types) && !empty($types)) {
                return \in_array($type, $types);
            }
        }

        return true;
    }

    public function getInvoiceFormat(): ?string
    {
        $configuration = $this->getConfiguration();

        if ($configuration instanceof BankConfigurationInterface) {
            return $configuration->getInvoiceFormat();
        }

        return null;
    }

    public function getHasExternalInvoiceTypes()
    {
        $configuration = $this->getConfiguration();

        if ($configuration instanceof BankConfigurationInterface) {
            return $configuration->getHasExternalInvoice();
        }

        return false;
    }

    public function getHasExternalInvoice($type)
    {
        $configuration = $this->getConfiguration();

        if ($configuration instanceof BankConfigurationInterface) {
            $types = $configuration->getHasExternalInvoice();
            if (\is_array($types)) {
                return \in_array($type, $types);
            }
        }

        return false;
    }

    public function getPaymentDelay(): int
    {
        $configuration = $this->getConfiguration();

        if ($configuration instanceof BankConfigurationInterface) {
            return $configuration->getPaymentDelay();
        }

        return 0;
    }

    public function getLastKycView()
    {
        $configuration = $this->getConfiguration();

        if ($configuration instanceof BankConfigurationInterface) {
            return $configuration->getLastKycView();
        }

        return 0;
    }

    public function getLastInvoiceView()
    {
        $configuration = $this->getConfiguration();

        if ($configuration instanceof BankConfigurationInterface) {
            return $configuration->getLastInvoiceView();
        }

        return 0;
    }

    public function getLastReconciliationView()
    {
        $configuration = $this->getConfiguration();

        if ($configuration instanceof BankConfigurationInterface) {
            return $configuration->getLastReconciliationView();
        }

        return 0;
    }

    public function getBillingAddress()
    {
        $configuration = $this->getConfiguration();

        if ($configuration instanceof BankConfigurationInterface) {
            return $configuration->getBillingAddress();
        }

        return;
    }

    public function getInvoiceClientCode()
    {
        $configuration = $this->getConfiguration();
        if ($configuration instanceof BankConfigurationInterface) {
            return $configuration->getInvoiceClientCode();
        }

        return BankConfigurationInterface::INVOICE_CLIENT_CODE_ID;
    }

    public function getVat()
    {
        $configuration = $this->getConfiguration();

        if ($configuration instanceof BankConfigurationInterface) {
            return $configuration->getVat();
        }

        return;
    }

    /**
     * Update last kyc view.
     */
    public function updateLastKycView()
    {
        $configuration = $this->getConfiguration();

        if (null !== $configuration) {
            $configuration->setLastKycView(new \DateTime());
            $this->entityManager->persist($configuration);
            $this->entityManager->flush();
        }
    }

    /**
     * Update last invoice view.
     */
    public function updateLastInvoiceView()
    {
        $configuration = $this->getConfiguration();

        if (null !== $configuration) {
            $configuration->setLastInvoiceView(new \DateTime());
            $this->entityManager->persist($configuration);
            $this->entityManager->flush();
        }
    }

    /**
     * @return string|null
     */
    public function getPaymentGateway()
    {
        $configuration = $this->getConfiguration();

        if (null !== $configuration) {
            return $configuration->getPaymentGateway();
        }

        return null;
    }

    public function hasDefaultPaymentGateway(): bool
    {
        return PaymentGatewayInterface::GATEWAY_DEFAULT === $this->getPaymentGateway();
    }

    public function canAttachInvoiceFile(): bool
    {
        $configuration = $this->getConfiguration();

        if (null !== $configuration) {
            return $configuration->getCanAttachInvoiceFile();
        }

        return false;
    }

    /**
     * Update payment.
     */
    public function updatePayment(PaymentInterface $payment): PaymentInterface
    {
        $paymentId = $payment->getId();
        $refererId = $payment->getRefererId();
        $refererType = $payment->getRefererType();

        // Log initial payment state for debugging cart-to-order transition issues
        $this->logger->info('[PAYMENT_DEBUG] Starting updatePayment', [
            'payment_id' => $paymentId,
            'referer_id' => $refererId,
            'referer_type' => $refererType,
            'current_state' => $payment->getState(),
            'payment_method' => $payment->getMethod() ? $payment->getMethod()->getType() : null,
            'amount' => $payment->getAmount(),
        ]);

        $changes = $this->entityManager->getRepository(Payment::class)->getEntityChangeSet($payment);

        // Log entity change set for debugging
        if (!empty($changes)) {
            $this->logger->info('[PAYMENT_DEBUG] Payment entity changes detected', [
                'payment_id' => $paymentId,
                'referer_id' => $refererId,
                'changes' => $changes,
            ]);
        }

        // Log order/cart state if this is an order payment
        if ($refererType === 'order' && $refererId) {
            try {
                $order = $this->entityManager->getRepository(\AppBundle\Entity\Order::class)->find($refererId);
                if ($order) {
                    $this->logger->info('[PAYMENT_DEBUG] Order state before payment update', [
                        'order_id' => $order->getId(),
                        'order_type' => $order->getType(),
                        'order_state' => $order->getState(),
                        'payment_state' => $order->getPaymentState(),
                        'cart_confirmed_at' => $order->getCartConfirmedAt() ? $order->getCartConfirmedAt()->format('Y-m-d H:i:s') : null,
                        'completed_at' => $order->getCompletedAt() ? $order->getCompletedAt()->format('Y-m-d H:i:s') : null,
                        'cart_id' => $order->getCart() ? $order->getCart()->getId() : null,
                    ]);
                }
            } catch (\Exception $e) {
                $this->logger->error('[PAYMENT_DEBUG] Failed to load order for logging', [
                    'referer_id' => $refererId,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        if (!empty($changes['state'])) {
            $this->logger->info('[PAYMENT_DEBUG] Payment state change detected, dispatching event', [
                'payment_id' => $paymentId,
                'referer_id' => $refererId,
                'old_state' => $changes['state'][0] ?? null,
                'new_state' => $changes['state'][1] ?? null,
            ]);

            $this->eventDispatcher->dispatch(new GenericEvent($payment), UpplerBankEvents::PAYMENT_STATE_CHANGE);

            $this->logger->info('[PAYMENT_DEBUG] Payment state change event dispatched', [
                'payment_id' => $paymentId,
                'referer_id' => $refererId,
            ]);
        }

        try {
            $this->logger->info('[PAYMENT_DEBUG] Persisting payment entity', [
                'payment_id' => $paymentId,
                'referer_id' => $refererId,
            ]);

            $this->entityManager->persist($payment);

            $this->logger->info('[PAYMENT_DEBUG] Starting entity manager flush', [
                'payment_id' => $paymentId,
                'referer_id' => $refererId,
            ]);

            $this->entityManager->flush();

            $this->logger->info('[PAYMENT_DEBUG] Entity manager flush completed successfully', [
                'payment_id' => $paymentId,
                'referer_id' => $refererId,
            ]);

        } catch (\Exception $e) {
            $this->logger->error('[PAYMENT_DEBUG] CRITICAL: Entity manager flush failed', [
                'payment_id' => $paymentId,
                'referer_id' => $refererId,
                'referer_type' => $refererType,
                'error_message' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'stack_trace' => $e->getTraceAsString(),
                'payment_state' => $payment->getState(),
                'entity_changes' => $changes,
            ]);

            // Log additional entity manager state for debugging
            try {
                $uow = $this->entityManager->getUnitOfWork();
                $this->logger->error('[PAYMENT_DEBUG] Entity manager unit of work state', [
                    'payment_id' => $paymentId,
                    'referer_id' => $refererId,
                    'scheduled_insertions' => count($uow->getScheduledEntityInsertions()),
                    'scheduled_updates' => count($uow->getScheduledEntityUpdates()),
                    'scheduled_deletions' => count($uow->getScheduledEntityDeletions()),
                    'identity_map_count' => count($uow->getIdentityMap()),
                ]);
            } catch (\Exception $uowException) {
                $this->logger->error('[PAYMENT_DEBUG] Failed to get unit of work state', [
                    'error' => $uowException->getMessage(),
                ]);
            }

            throw $e;
        }

        // Log final state after successful update
        $this->logger->info('[PAYMENT_DEBUG] Payment update completed successfully', [
            'payment_id' => $paymentId,
            'referer_id' => $refererId,
            'final_state' => $payment->getState(),
        ]);

        return $payment;
    }

    /**
     * Update referer.
     */
    public function updateReferer(BankRefererInterface $referer): BankRefererInterface
    {
        $this->entityManager->persist($referer);
        $this->entityManager->flush();

        return $referer;
    }

    public function addFlash($type, $message, $options = []): void
    {
        $this->session->getBag('flashes')->add($type, $this->translator->trans($message, $options));
    }

    /**
     * Gets all payment type.
     */
    public function getAllPaymentType(): array
    {
        return [
            BankClientInterface::TYPE_ORDER,
            BankClientInterface::TYPE_PAYOUT,
            BankClientInterface::TYPE_REFUND,
            BankClientInterface::TYPE_CREDIT,
            BankClientInterface::TYPE_FEE,
            BankClientInterface::TYPE_FEE_REFUND,
            BankClientInterface::TYPE_PAYIN,
            BankClientInterface::TYPE_SUBSCRIPTION,
            BankClientInterface::TYPE_SUBSCRIPTION_RECURRING,
        ];
    }

    /**
     * Gets payment type by type.
     * to display in MO/FO.
     */
    public function getPaymentTypeByType($type = null)
    {
        $types = [];

        if (null === $type) {
            $types = BankClientInterface::TYPE_ORDER;
        } else {
            $types = match ($type) {
                BankClientInterface::TYPE_FEE => [
                    BankClientInterface::TYPE_FEE,
                    BankClientInterface::TYPE_FEE_REFUND,
                ],
                BankClientInterface::TYPE_FEE_PAYOUT => [
                    BankClientInterface::TYPE_FEE_PAYOUT,
                    BankClientInterface::TYPE_FEE_REFUND,
                ],
                BankClientInterface::TYPE_REFUND => BankClientInterface::TYPE_REFUND,
                BankClientInterface::TYPE_CREDIT => BankClientInterface::TYPE_CREDIT,
                BankClientInterface::TYPE_PAYOUT => BankClientInterface::TYPE_PAYOUT,
                BankClientInterface::TYPE_SUBSCRIPTION => [
                    BankClientInterface::TYPE_PAYIN,
                    BankClientInterface::TYPE_SUBSCRIPTION,
                    BankClientInterface::TYPE_SUBSCRIPTION_RECURRING,
                ],
                BankClientInterface::TYPE_EXTERNAL_ORDER => BankClientInterface::TYPE_EXTERNAL_ORDER,
                default => BankClientInterface::TYPE_ORDER,
            };
        }

        return $types;
    }

    /**
     * Gets default invoice by type.
     */
    public function getInvoices(string $payerType, array $payers = [], $types = null, bool $paginated = false, int $page = 1, array $criteria = [], array $sorting = [])
    {
        $payer = $this->getAuthenticated();
        if (empty($payers)) {
            if (!$payer instanceof PayerInterface) {
                return [];
            }
            $payers[] = $payer;
        }

        $originAddressesIds = [];

        if (!\is_array($types) && BankClientInterface::TYPE_EXTERNAL_ORDER === $types) {
            $originAddresses = $this->entityManager->getRepository(Address::class)->findFromOriginCompany($payer);

            $originAddressesIds = array_map(fn ($value) => $value->getId(), $originAddresses);
        }

        // to fix for fee_payout
        if (\is_array($types) && 1 === \count($types)) {
            $types = $types[0];
        }

        $pager = $this->paymentRepository->findInvoice($payerType, $types, $payers, $paginated, $originAddressesIds, $criteria, $sorting);

        if ($pager instanceof Pagerfanta) {
            $pager->setMaxPerPage(20);
            $pager->setCurrentPage($page);
        }

        return $pager;
    }

    /**
     * Gets default invoice by type.
     */
    public function countInvoices(string $payerType, array $payers = [], $types = null): int
    {
        if (empty($payers)) {
            if (!($payer = $this->getAuthenticated()) instanceof PayerInterface) {
                return 0;
            }
            $payers[] = $payer;
        }

        return $this->paymentRepository->countInvoice($payerType, $types, $payers);
    }

    /**
     * Gets default invoice by type.
     */
    public function countDownloadableInvoices(string $payerType, array $payers = [], $types = null): int
    {
        if (empty($payers)) {
            if (!($payer = $this->getAuthenticated()) instanceof PayerInterface) {
                return 0;
            }
            $payers[] = $payer;
        }

        return $this->paymentRepository->countDownloadableInvoice($payerType, $types, $payers);
    }

    public function getAuthenticated(): ?PayerInterface
    {
        if (($token = $this->tokenStorage->getToken()) instanceof TokenInterface) {
            if (($user = $token->getUser()) instanceof UserInterface) {
                if (($payer = $user->getCompany()) instanceof PayerInterface) {
                    return $payer;
                }
            }
        }

        return null;
    }

    public function createPdf($content, $returnUrl = false)
    {
        $name = sprintf('%s.pdf', md5(uniqid(random_int(0, mt_getrandmax()), true)));
        $hash = md5(uniqid(random_int(0, mt_getrandmax()), true));
        $explodePath = explode('.', $name);
        $path = $hash.'.'.$explodePath[\count($explodePath) - 1];

        $pathDest = sprintf(
            '%s/%s/%s',
            substr($path, 0, 2),
            substr($path, 2, 2),
            substr($path, 4)
        );

        $path = $this->fileUploader->uploadFromContent($pathDest, $content, 'application/pdf');

        if ($returnUrl) {
            return $this->fileUploader->getUrlByPath($path);
        }

        return $path;
    }

    public function getMoneyExtension(): MoneyExtension
    {
        return $this->twigMoney;
    }

    /**
     * {@inheritDoc}
     */
    public function checkInvoiceConditions(BankRefererInterface $referer, PaymentInterface $payment): bool
    {
        if (null === ($paymentMethod = $payment->getMethod())
            || null !== $payment->getInvoiceDate()
        ) {
            return false;
        }

        return $this->checkPaymentMethodConditions($referer, $paymentMethod->getTriggerInvoices());
    }

    /**
     * {@inheritDoc}
     */
    public function checkChargeConditions(BankRefererInterface $referer, PaymentInterface $payment): bool
    {
        if (null === ($paymentMethod = $payment->getMethod())) {
            return false;
        }

        return $this->checkPaymentMethodConditions($referer, $paymentMethod->getTriggerCharges());
    }

    /**
     * {@inheritDoc}
     */
    public function checkTransactionConditions(BankRefererInterface $referer, PaymentInterface $payment): bool
    {
        if (null === ($paymentMethod = $payment->getMethod())) {
            return false;
        }

        return $this->checkPaymentMethodConditions($referer, $paymentMethod->getTriggerTransactions());
    }

    /**
     * {@inheritDoc}
     */
    public function checkPaymentMethodConditions(BankRefererInterface $referer, Collection $triggerStates = null): bool
    {
        if (\is_null($triggerStates)) {
            return false;
        }

        foreach ($triggerStates as $triggerState) {
            if (
                $triggerState->getRefererType() === $referer->getRefererType()
                && (
                    (
                        null === $triggerState->getOrderState()
                        || (
                            method_exists($referer, 'getState')
                            && $triggerState->getOrderState() === $referer->getState()
                        )
                    ) && (
                        null === $triggerState->getCartState()
                        || (
                            method_exists($referer, 'getCartState')
                            && $triggerState->getCartState() === $referer->getCartState()
                        )
                    ) && (
                        null === $triggerState->getPaymentState()
                        || (
                            method_exists($referer, 'getPaymentState')
                            && $triggerState->getPaymentState() === $referer->getPaymentState()
                        )
                    ) && (
                        null === $triggerState->getShippingState()
                        || (
                            method_exists($referer, 'getShippingState')
                            && $triggerState->getShippingState() === $referer->getShippingState()
                        )
                    ) && (
                        null === $triggerState->getSubscriptionState()
                        || (
                            method_exists($referer, 'getSubscriptionState')
                            && $triggerState->getSubscriptionState() === $referer->getSubscriptionState()
                        )
                    )
                )
            ) {
                return true;
            }
        }

        return false;
    }

    public function generateUrlCaptureCreditCard($type, UrlTokenRefererInterface $referer, $targetPath)
    {
        $token = $this->urlTokenFactory->createCaptureToken($type, $referer, $targetPath);

        return $token->getTargetUrl();
    }

    public function canCompanyDownloadByType(CompanyInterface $company, string $type): bool
    {
        return ($company->isSeller() && \in_array($type, BankClientInterface::SELLER_TYPES))
            || ($company->isBuyer() && \in_array($type, BankClientInterface::BUYER_TYPES));
    }
}

<?php

namespace AppBundle\Provider\Dashboard;

use App<PERSON><PERSON>le\Entity\DashboardBlock;
use App<PERSON><PERSON>le\Entity\DashboardBlockInterface;
use AppBundle\ExpressionLanguage\ExpressionLanguage;
use AppBundle\Formatter\PriceFormatter;
use AppBundle\Provider\ConfigurationProviderInterface;
use AppBundle\Tools\Number;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class DashboardProvider implements DashboardProviderInterface
{
    protected $em;
    protected $dashboardConfiguration;
    protected $configurationProvider;
    protected $metricProviders;
    protected $parameterBag;
    protected $priceFormatter;

    public function __construct(EntityManagerInterface $em, array $dashboardConfiguration, ConfigurationProviderInterface $configurationProvider, iterable $metricProviders, ParameterBagInterface $parameterBag, PriceFormatter $priceFormatter)
    {
        $this->em = $em;
        $this->dashboardConfiguration = $dashboardConfiguration;
        $this->configurationProvider = $configurationProvider;
        $this->metricProviders = $metricProviders;
        $this->parameterBag = $parameterBag;
        $this->priceFormatter = $priceFormatter;
    }

    /**
     * Returns true if the given board is configured in the YAML configuration.
     */
    public function isConfiguredBoard(string $board): bool
    {
        return !empty($this->dashboardConfiguration['boards'][$board]);
    }

    /**
     * Gets filtered sections configured for the given block.
     */
    public function getConfiguredBoardSections(string $board): array
    {
        if (!\array_key_exists($board, $this->dashboardConfiguration['boards'])) {
            throw new \Exception(sprintf('The board "%s" doesn\'t exit. Maybe you missed to configure it in dashboard.yaml?', $board));
        }

        $board = $this->dashboardConfiguration['boards'][$board];

        return $this->filterSections($board['sections'] ?? []); // Evaluate sections conditions
    }

    /**
     * Gets sections blocks enabled by the operator in Back Office dashboard configuration.
     */
    public function getEnabledSectionsBlocks(array $sections): ArrayCollection
    {
        // Match configured blocks in DB with blocks available from the configuration
        $dashboardBlockRepository = $this->em->getRepository(DashboardBlock::class);
        $blocks = $dashboardBlockRepository->findBy(['section' => array_keys($sections)], ['section' => 'ASC', 'position' => 'ASC']);

        return new ArrayCollection($blocks);
    }

    public function hydrateBlocksWithConfiguration(iterable $blocks = []): void
    {
        foreach ($blocks as $block) {
            $block->setConfiguration($this->dashboardConfiguration['blocks'][$block->getName()]);
        }
    }

    public function hydrateBlocksWithMetrics(iterable $blocks = [], $criteria = []): array
    {
        $metricsByBlocks = [];
        foreach ($blocks as $block) {
            if (!$block->getConfiguration()) {
                throw new \Exception(sprintf('The block configuration of "%s" is missing. It should be loaded before calling this method.', $block->getName()));
            }

            $provider = $this->getProvider($block->getConfiguration()['data_provider']); // get appropriate provider
            $block->setMetrics(
                $provider->getMetrics($block->getConfiguration()['data_provider_method'], $criteria)
            );
        }

        return $metricsByBlocks;
    }

    /**
     * Filters given sections.
     * The filtering is performed by the evaluation of section's condition.
     */
    protected function filterSections(iterable $sections): array
    {
        foreach ($sections as $sectionName => $section) {
            if (false === $this->evaluateCondition($section['condition'] ?? '')) {
                unset($sections[$sectionName]);
            }
        }

        return $sections;
    }

    /**
     * Filters given blocks.
     * The filtering is performed by the evaluation of block's condition.
     */
    public function filterBlocks(ArrayCollection $blocks): ArrayCollection
    {
        return $blocks->filter(function (DashboardBlockInterface $block) {
            if (!$block->getConfiguration()) {
                throw new \Exception(sprintf('The block configuration of "%s" is missing. It should be loaded before calling this method.', $block->getName()));
            }

            return $this->evaluateCondition($block->getConfiguration()['condition'] ?? '');
        });
    }

    /**
     * Evaluates the given expression with the ExpressionLanguage component.
     */
    public function evaluateCondition(string $condition): bool
    {
        if (empty($condition)) {
            return true;
        }

        $expr = new ExpressionLanguage();

        return (bool) $expr->evaluate($condition, [
            'configurationProvider' => $this->configurationProvider,
            'parameters' => $this->parameterBag->all(),
        ]);
    }

    protected function getProvider(string $serviceId): DashboardMetricProviderInterface
    {
        $providers = [];

        $metricProviders = reset($this->metricProviders);

        foreach ($metricProviders as $metricProvider) {
            if (($metricProviderFqn = $metricProvider->getFqn()) === $serviceId) {
                return $metricProvider;
            }

            $providers[] = $metricProviderFqn;
        }

        throw new \Exception(sprintf('The requested provider "%s" has not been declared. Declared dashboard providers: %s', $serviceId, implode(', ', $providers)));
    }

    /**
     * Retrieve metrics according to blocks.
     */
    public function getMetricsByBlockSection(string $sectionName, array $metrics): array
    {
        $data = [];
        switch ($sectionName) {
            case 'nb_messages':
                $data['nb_messages'][] = $metrics['nb_total'];
                break;
            case 'nb_contacts':
                $data['contacts_total'][] = $metrics['nb_total'];
                $data['contacts_accepted'][] = $metrics['nb_accepted'];
                $data['contacts_pending'][] = $metrics['nb_pending'];
                $data['contacts_refused'][] = $metrics['nb_refused'];
                $data['contacts_deleted'][] = $metrics['nb_deleted'];
                break;
            case 'words_most_search':
                foreach ($metrics as $value) {
                    if (!empty($value)) {
                        $data['words_most_search'][] = $value['term'].':'.$value['count'].'x('.$value['percent'].'%)';
                    }
                }
                break;
            case 'nb_next_commission':
                $data['next_commission'][] = $metrics['nb_next_commission'];
                break;
            case 'nb_products_category':
                foreach ($metrics['chart']['data']['data'] as $key => $value) {
                    $data['products_category'][] = $key.':'.$value;
                }
                break;
            case 'nb_orders_category':
                foreach ($metrics['chart']['data']['data'] as $key => $value) {
                    $data['orders_category'][] = $key.':'.$value;
                }
                break;
            case 'nb_product_seen':
                $data['product_seen'][] = $metrics['nb_total'];
                foreach ($metrics['chart']['data']['data'] as $key => $value) {
                    $data['product_seen_by_day'][] = $key.':'.$value;
                }
                break;
            case 'nb_stocks':
                $data['stocks_total'][] = $metrics['nb_total'];
                $data['product_orderable_without_stock'][] = $metrics['nb_product_orderable_without_stock'];
                $data['product_in_stock'][] = $metrics['nb_product_in_stock'];
                $data['product_out_of_stock'][] = $metrics['nb_product_out_of_stock'];
                break;
            case 'avg_scoring':
                $data['avg_scoring'][] = $metrics['total'];
                $data['avg_scoring'][] = $metrics['avg_product'];
                $data['avg_scoring'][] = $metrics['avg_company'];
                break;
            case 'nb_buyers_subscriptions':
                $data['buyers_subscriptions_total'][] = $metrics['nb_total'];
                $data['buyers_subscriptions_confirmed'][] = $metrics['nb_confirmed'];
                $data['buyers_subscriptions_pending'][] = $metrics['nb_pending'];
                $data['buyers_subscriptions_refused'][] = $metrics['nb_refused'];
                break;
            case 'nb_products':
                $data['products_total'][] = $metrics['nb_total'];
                $data['products_accepted'][] = $metrics['nb_accepted'];
                $data['products_incomplete'][] = $metrics['nb_incomplete'];
                $data['products_pending'][] = $metrics['nb_pending'];
                $data['products_editing'][] = $metrics['nb_editing'];
                $data['products_refuse'][] = $metrics['nb_refuse'];
                $data['products_refuse_seller'][] = $metrics['nb_product_refuse_seller'];
                $data['products_master'][] = $metrics['nb_product_master'];
                break;
            case 'nb_sellers_subscriptions':
                $data['sellers_subscriptions_total'][] = $metrics['nb_total'];
                $data['sellers_subscriptions_confirmed'][] = $metrics['nb_confirmed'];
                $data['sellers_subscriptions_pending'][] = $metrics['nb_pending'];
                $data['sellers_subscriptions_refused'][] = $metrics['nb_refused'];
                break;
            case 'amount_orders':
                $data['amount_orders_total'][] = $this->priceFormatter->formatCurrency($metrics['amount_total']);
                $data['amount_orders_delivered'][] = $this->priceFormatter->formatCurrency($metrics['amount_delivered']);
                $data['amount_orders_confirmed'][] = $this->priceFormatter->formatCurrency($metrics['amount_confirmed']);
                $data['amount_orders_pending'][] = $this->priceFormatter->formatCurrency($metrics['amount_pending']);
                $data['amount_orders_refuse'][] = $this->priceFormatter->formatCurrency($metrics['amount_cancel_refuse']);
                foreach ($metrics['chart']['data']['data'] as $key => $value) {
                    $data['amount_orders_by_day'][] = $key.':'.$value;
                }
                break;
            case 'nb_orders':
                $data['orders_total'][] = $metrics['nb_total'];
                $data['orders_delivered'][] = $metrics['nb_delivered'];
                $data['orders_confirmed'][] = $metrics['nb_confirmed'];
                $data['orders_pending'][] = $metrics['nb_pending'];
                $data['orders_refuse'][] = $metrics['nb_cancel_refuse'];
                foreach ($metrics['chart']['data']['data'] as $key => $value) {
                    $data['orders_by_day'][] = $key.':'.$value;
                }
                break;
            case 'nb_products_sold':
                $data['products_sold_total'][] = $metrics['nb_total'];
                break;
            case 'top_products_amount_sold':
                foreach ($metrics['top_infos'] as $value) {
                    $data['top_products_amount_sold'][] = $value['name'].':'.$this->priceFormatter->formatCurrency($value['total']);
                }
                break;
            case 'top_products_seen':
                foreach ($metrics['top_infos'] as $value) {
                    $data['top_products_seen'][] = $value['name'].':'.$value['total'];
                }
                break;
            case 'top_products_sold':
                foreach ($metrics['top_infos'] as $value) {
                    $data['top_products_sold'][] = $value['name'].':'.$value['total'].':'.$value['unit'];
                }
                break;
            case 'nb_subscription':
                $data['nb_subscription'][] = $metrics['nb_total'];
                break;
            case 'amount_subscription':
                $data['subscription_total'][] = $this->priceFormatter->formatCurrency($metrics['total']);
                $data['subscription_amount_total'][] = $this->priceFormatter->formatCurrency($metrics['amount_total']);
                break;
            case 'nb_buyers_sessions':
                $data['buyers_sessions'][] = $metrics['nb_buyers_sessions'];
                break;
            case 'top_buyers_orders':
                foreach ($metrics['top_infos'] as $key => $value) {
                    $data['top_buyers_orders'][] = $value['name'].':'.$value['total'];
                }
                break;
            case 'top_buyers_turnover':
                foreach ($metrics['top_infos'] as $key => $value) {
                    $data['top_buyers_turnover'][] = $value['name'].':'.$this->priceFormatter->formatCurrency($value['total']);
                }
                break;
            case 'top_sellers_orders':
                foreach ($metrics['top_infos'] as $key => $value) {
                    $data['top_sellers_orders'][] = $value['name'].':'.$value['total'];
                }
                break;
            case 'top_sellers_turnover':
                foreach ($metrics['top_infos'] as $key => $value) {
                    $data['top_sellers_turnover'][] = $value['name'].':'.$this->priceFormatter->formatCurrency($value['total']);
                }
                break;
            case 'nb_sellers_sessions':
                $data['sellers_sessions'][] = $metrics['nb_sellers_sessions'];
                break;
            case 'rate_dispute':
                $data['rate_dispute'][] = Number::displayPercent($metrics['nb_total']);
                break;
            case 'amount_dispute':
                $data['amount_dispute_total'][] = $this->priceFormatter->formatCurrency($metrics['amount_total']);
                $data['amount_dispute_resolved'][] = $this->priceFormatter->formatCurrency($metrics['amount_resolved']);
                $data['amount_dispute_canceled'][] = $this->priceFormatter->formatCurrency($metrics['amount_canceled']);
                foreach ($metrics['chart']['data']['data'] as $key => $value) {
                    $data['amount_dispute_by_day'][] = $key.':'.$value;
                }
                break;
            case 'nb_wishlist':
                $data['wishlist_total'][] = $metrics['nb_total'];
                $data['wishlist_transformed'][] = $metrics['nb_transformed'];
                $data['wishlist_pending'][] = $metrics['nb_pending'];
                break;
            case 'nb_tenders_proposal':
                $data['tenders_proposal_total'][] = $metrics['nb_total'];
                $data['tenders_proposal_accepted'][] = $metrics['nb_accepted'];
                $data['tenders_proposal_pending'][] = $metrics['nb_pending'];
                $data['tenders_proposal_refused'][] = $metrics['nb_refused'];
                break;
            case 'nb_tenders':
                $data['tenders_total'][] = $metrics['nb_total'];
                $data['tenders_closed'][] = $metrics['nb_closed'];
                $data['tenders_pending'][] = $metrics['nb_pending'];
                $data['tenders_draft'][] = $metrics['nb_draft'];
                foreach ($metrics['chart']['data']['data'] as $key => $value) {
                    $data['tenders_by_day'][] = $key.':'.$value;
                }
                break;
            case 'nb_quotes':
                $data['quotes_total'][] = $metrics['nb_total'];
                $data['quotes_transformed'][] = $metrics['nb_transformed'];
                $data['quotes_validated'][] = $metrics['nb_validated'];
                $data['quotes_confirmed'][] = $metrics['nb_confirmed'];
                $data['quotes_pending'][] = $metrics['nb_pending'];
                $data['quotes_closed'][] = $metrics['nb_closed'];
                foreach ($metrics['chart']['data']['data'] as $key => $value) {
                    $data['quotes_by_day'][] = $key.':'.$value;
                }
                break;
            case 'amount_quotes':
                $data['amount_quotes_total'][] = $this->priceFormatter->formatCurrency($metrics['amount_total']);
                $data['amount_quotes_transformed'][] = $this->priceFormatter->formatCurrency($metrics['amount_transformed']);
                $data['amount_quotes_validated'][] = $this->priceFormatter->formatCurrency($metrics['amount_validated']);
                $data['amount_quotes_confirmed'][] = $this->priceFormatter->formatCurrency($metrics['amount_confirmed']);
                $data['amount_quotes_pending'][] = $this->priceFormatter->formatCurrency($metrics['amount_pending']);
                $data['amount_quotes_closed'][] = $this->priceFormatter->formatCurrency($metrics['amount_closed']);
                foreach ($metrics['chart']['data']['data'] as $key => $value) {
                    $data['amount_quotes_by_day'][] = $key.':'.$value;
                }
                break;
            case 'delay_validation_orders':
                $data['delay_validation_orders'][] = $metrics['delay_validation'];
                break;
            case 'amount_next_commission':
                $data['amount_next_commission'][] = $this->priceFormatter->formatCurrency($metrics['amount_next_commission']);
                break;
            case 'amount_received':
                $data['amount_received_total'][] = $this->priceFormatter->formatCurrency($metrics['total']);
                $data['amount_received_commission'][] = $this->priceFormatter->formatCurrency($metrics['commission']);
                $data['amount_received_subscription'][] = $this->priceFormatter->formatCurrency($metrics['subscription']);
                foreach ($metrics['chart']['data']['data'] as $key => $value) {
                    $data['amount_received_by_day'][] = $key.':'.$value;
                }
                break;
            case 'avg_cart':
                $data['avg_cart'][] = $this->priceFormatter->formatCurrency($metrics['avg_cart']);
                break;
            case 'rate_conversion':
                $data['rate_cart_created'][] = Number::displayPercent($metrics['rate_cart_created']);
                $data['rate_cart_canceled'][] = Number::displayPercent($metrics['rate_cart_canceled']);
                $data['rate_cart_address'][] = Number::displayPercent($metrics['rate_cart_address']);
                $data['rate_cart_confirmed'][] = Number::displayPercent($metrics['rate_cart_confirmed']);
                $data['rate_order_confirmed'][] = Number::displayPercent($metrics['rate_order_confirmed']);
                $data['rate_order_shipped'][] = Number::displayPercent($metrics['rate_order_shipped']);
                break;
        }

        return $data;
    }
}

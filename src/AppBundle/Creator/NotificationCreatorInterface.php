<?php

namespace AppBundle\Creator;

use AppBundle\Model\NotificationOwnerInterface;

/**
 * Create notification.
 */
interface NotificationCreatorInterface
{
    public function createNotificationByName(string $name, ?NotificationOwnerInterface $owner = null, ?NotificationOwnerInterface $from = null, ?string $path = null, ?array $paramPath = null, ?bool $toOperator = false, ?array $paramText = null, ?string $text = null, ?string $anchor = null): void;
}

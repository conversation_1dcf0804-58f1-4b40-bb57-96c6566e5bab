<?php

namespace AppBundle\Entity;

use AppBundle\DoctrineBehavior\Translatable\LoggableTranslationTrait;
use Knp\DoctrineBehaviors\Contract\Entity\TranslationInterface;
use Knp\DoctrineBehaviors\Model\Translatable\TranslationTrait;

class DynamicFieldTranslation implements TranslationInterface, LogEntryCollectableInterface
{
    use TranslationTrait;
    use LoggableTranslationTrait;

    protected $id;
    protected $translatedValue;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTranslatedValue(): ?string
    {
        return $this->translatedValue;
    }

    public function setTranslatedValue(?string $translatedValue): self
    {
        $this->translatedValue = $translatedValue;

        return $this;
    }

    public function getValue(): ?string
    {
        return $this->translatedValue;
    }

    public function setValue(?string $translatedValue): self
    {
        $this->translatedValue = $translatedValue;

        return $this;
    }
}

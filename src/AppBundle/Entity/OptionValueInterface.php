<?php

namespace AppBundle\Entity;

/**
 * Option value interface.
 */
interface OptionValueInterface extends LogEntryCollectionParentInterface
{
    public const STATUS_PENDING = 'pending';
    public const STATUS_ACCEPTED = 'accepted';
    public const STATUS_PROCESSING = 'processing';

    public const QUEUEABLE = 'option_value';

    public function getId(): ?int;

    /**
     * Get option.
     *
     * @return OptionInterface
     */
    public function getOption(): ?OptionInterface;

    /**
     * Set option.
     *
     * @param OptionInterface $option
     */
    public function setOption(?OptionInterface $option): self;

    /**
     * get Slug.
     */
    public function getSlug(): string;

    /**
     * set Slug.
     */
    public function setSlug(string $slug): self;

    /**
     * Proxy method to access the name of real option object.
     * Those methods are mostly useful in templates so you can easily
     * display option name with their values.
     */
    public function getName(): ?string;

    /**
     * @return string
     */
    public function getStatus(): ?string;

    public function setStatus(string $status = null): self;

    public function isAccepted(): bool;

    public function isPending(): bool;

    public function isAllowed(CompanyInterface $createdBy = null): bool;
}

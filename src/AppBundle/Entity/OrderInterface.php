<?php

namespace AppBundle\Entity;

use App<PERSON><PERSON>le\Gateway\ShippingMethod\ShippingMethodGatewayInterface;
use AppBundle\Model\AdjustableInterface;
use AppBundle\Model\BankRefererInterface;
use AppBundle\Model\FeeOwnerInterface;
use AppBundle\Model\MatchableInterface;
use AppBundle\Model\NotificationOwnerInterface;
use AppBundle\Model\PromotableInterface;
use AppBundle\Model\PromotionMatchableInterface;
use AppBundle\Model\ReferenceThreadInterface;
use AppBundle\Model\RetentionableInterface;
use AppBundle\Model\ReviewableInterface;
use AppBundle\Model\SoftDeletableInterface;
use AppBundle\Model\TimestampableInterface;
use AppBundle\Model\WebhookSubjectInterface;
use Doctrine\Common\Collections\Collection;
use Symfony\Component\Security\Core\User\UserInterface;

/**
 * Order model.
 */
interface OrderInterface extends RefererInterface, BankRefererInterface, AdjustableInterface, TimestampableInterface, SoftDeletableInterface, QueueableInterface, WebhookSubjectInterface, FileOwnerInterface, PromotableInterface, PromotionMatchableInterface, FeeOwnerInterface, ScopableInterface, RetentionableInterface, ReferenceThreadInterface, ShippableInterface, ExportableInterface, LogEntryCollectionParentInterface, ReviewableInterface, MatchableInterface
{
    public const NAME = 'order';
    public const CART = 'cart';
    public const QUOTE = 'quote';
    public const ORDER = 'order';
    public const SUB_ORDER = 'sub_order';
    public const DISPUTE = 'dispute';
    public const DROPSHIP = 'dropship';
    public const ALL = 'all';
    public const WISHLIST = 'wishlist';
    public const RECURRING = 'recurring';

    public const BY_BUYER = 'buyer';
    public const BY_SELLER = 'seller';
    public const BY_OPERATOR = 'operator';

    public const QUEUEABLE = 'order';
    public const THREAD_REFERENCE_TYPE = ReferenceThreadInterface::REFERENCE_TYPE_ORDER;

    // order states
    // when order is created
    public const STATE_NEW = 'new';
    // when order is cart steps ended
    public const STATE_PENDING = 'pending';
    // when order is confirmed/completed by seller
    public const STATE_CONFIRMED = 'confirmed';
    public const STATE_EDITED = 'edited';
    public const STATE_EDITING = 'editing';
    public const STATE_REFUSED = 'refused';
    public const STATE_EXPIRED = 'expired';
    public const STATE_CANCELED = 'canceled';
    public const STATE_APPROVING = 'approving';
    public const STATE_OUTSIDE_TREATED = 'outside_treated';
    // Only used for order of type wishlist
    public const STATE_WISHLIST_ARCHIVED = 'wishlist_archived';
    public const STATE_WISHLIST_REMOVED = 'wishlist_removed';

    // Action states to remove when order will be refactorized with a real workflow
    public const ACTION_REFUSE = 'refuse';
    public const ACTION_CONFIRM = 'confirm';
    public const ACTION_INVOICE = 'invoice';
    public const ACTION_READY = 'ready';
    public const ACTION_SHIPPING_PARTIALLY = 'shipping_partially';
    public const ACTION_SHIPPING = 'shipping';
    public const ACTION_SHIPPING_UPDATE = 'shipping_update';

    public const ACTIONS = [
        self::ACTION_REFUSE => self::ACTION_REFUSE,
        self::ACTION_CONFIRM => self::ACTION_CONFIRM,
        self::ACTION_READY => self::ACTION_READY,
        self::ACTION_SHIPPING_PARTIALLY => self::ACTION_SHIPPING_PARTIALLY,
        self::ACTION_SHIPPING => self::ACTION_SHIPPING,
        self::ACTION_INVOICE => self::ACTION_INVOICE,
        self::ACTION_SHIPPING_UPDATE => self::ACTION_SHIPPING_UPDATE,
    ];

    public const STATES = [
        self::STATE_NEW => self::STATE_NEW,
        self::STATE_PENDING => self::STATE_PENDING,
        self::STATE_CONFIRMED => self::STATE_CONFIRMED,
        self::STATE_EDITED => self::STATE_EDITED,
        self::STATE_REFUSED => self::STATE_REFUSED,
        self::STATE_EXPIRED => self::STATE_EXPIRED,
        self::STATE_CANCELED => self::STATE_CANCELED,
        self::STATE_OUTSIDE_TREATED => self::STATE_OUTSIDE_TREATED,
    ];

    public const QUOTE_STATES = [
        self::STATE_NEW => self::STATE_NEW,
        self::STATE_EDITING => self::STATE_EDITING,
        self::STATE_PENDING => self::STATE_PENDING,
        self::STATE_CONFIRMED => self::STATE_CONFIRMED,
        self::STATE_REFUSED => self::STATE_REFUSED,
        self::STATE_EXPIRED => self::STATE_EXPIRED,
        self::STATE_CANCELED => self::STATE_CANCELED,
    ];

    public const BLOCK_NUMBER = 'number';
    public const BLOCK_SELLER_NUMBER = 'seller_number';
    public const BLOCK_DATE = 'date';
    public const BLOCK_SELLER = 'seller';
    public const BLOCK_BUYER = 'buyer';
    public const BLOCK_BUYER_SHIPMENT = 'buyer_shipment';
    public const BLOCK_SHIPMENT = 'shipment';
    public const BLOCK_PAYMENT = 'payment';
    public const BLOCK_ITEMS = 'items';
    public const BLOCK_NOTE = 'note';
    public const BLOCK_TOTAL = 'total';
    public const BLOCK_RECURRING = 'recurring';
    public const BLOCK_UNIT_PRICE = 'unit_price';
    public const BLOCK_QUANTITY = 'quantity';

    public const BLOCKS = [
        self::BLOCK_NUMBER => self::BLOCK_NUMBER,
        self::BLOCK_SELLER_NUMBER => self::BLOCK_SELLER_NUMBER,
        self::BLOCK_DATE => self::BLOCK_DATE,
        self::BLOCK_SELLER => self::BLOCK_SELLER,
        self::BLOCK_BUYER => self::BLOCK_BUYER,
        self::BLOCK_BUYER_SHIPMENT => self::BLOCK_BUYER_SHIPMENT,
        self::BLOCK_SHIPMENT => self::BLOCK_SHIPMENT,
        self::BLOCK_PAYMENT => self::BLOCK_PAYMENT,
        self::BLOCK_ITEMS => self::BLOCK_ITEMS,
        self::BLOCK_NOTE => self::BLOCK_NOTE,
        self::BLOCK_TOTAL => self::BLOCK_TOTAL,
        self::BLOCK_UNIT_PRICE => self::BLOCK_UNIT_PRICE,
        self::BLOCK_QUANTITY => self::BLOCK_QUANTITY,
    ];

    public const PURCHASABLES = [
        self::CART,
        self::QUOTE,
        self::ORDER,
        self::SUB_ORDER,
        self::WISHLIST,
        self::RECURRING,
    ];

    public const REVIEWABLE_TYPE = ReviewableInterface::TYPE_ORDER;
    public const REVIEWABLE_PROPERTY = 'id';

    public const ORDER_TYPE_ORDER = 'order';
    public const ORDER_TYPE_ORDER_FROM_QUOTE = 'order_from_quote';
    public const ORDER_TYPE_QUOTE = 'quote';

    public const ORDER_TYPES = [
        self::ORDER_TYPE_ORDER,
        self::ORDER_TYPE_ORDER_FROM_QUOTE,
        self::ORDER_TYPE_QUOTE,
    ];

    public function getAdditionalTaxes();

    public function isQuote(): bool;

    public function hasQuote(): bool;

    public function hasTenderProposal(): bool;

    public function isOrder(): bool;

    public function isCart(): bool;

    public function isDispute(): bool;

    public function isWishlist(): bool;

    public function isPending(): bool;

    public function isEditing(): bool;

    public function isEdited(): bool;

    public function isNew(): bool;

    public function isDelivered(): bool;

    public function getId();

    public function getOrderRecurrenceFrom(): ?self;

    public function setOrderRecurrenceFrom(self $orderRecurrenceFrom): self;

    public function getQuote(): ?self;

    public function setQuote(?self $quote): self;

    /**
     * Check if type number already exist.
     *
     * @return float
     */
    public function hasNumberByType(string $type);

    /**
     * Get shipping total.
     *
     * @return float
     */
    public function getShippingTotal();

    /**
     * Set payment.
     */
    public function setPayment(?PaymentInterface $payment): self;

    /**
     * Get the payment state.
     *
     * @return string
     */
    public function getPaymentState();

    public function isCartState();

    /**
     * Set currency.
     */
    public function setCurrency(CurrencyInterface $currency): self;

    /**
     * Get the shipping state.
     *
     * @return string
     */
    public function getShippingState();

    /**
     * Set shipping state.
     */
    public function setShippingState(?string $state): self;

    /**
     * Alias for getClickAndCollect.
     *
     * @return bool
     */
    public function isClickAndCollect();

    /**
     * Get order click and collect.
     *
     * @return bool
     */
    public function getClickAndCollect();

    /**
     * Set order click and collect.
     *
     * @param bollean $clickAndCollect
     */
    public function setClickAndCollect($clickAndCollect): self;

    /**
     * Get type.
     */
    public function getType(): ?string;

    public function getState(): ?string;

    /**
     * Get state.
     */
    public function setState(string $state): self;

    /**
     * Check if in state.
     *
     * @return int
     */
    public function inState($state);

    /**
     * Get number of purchase order.
     *
     * @return NumberInterface|mixed|null
     */
    public function getNumberPurchase(): ?NumberInterface;

    /**
     * Get all items.
     *
     * @return Collection<OrderItemInterface>
     */
    public function getItems(): Collection;

    /**
     * Get only not canceled items.
     *
     * @return Collection<OrderItemInterface>
     */
    public function getValidatedItems($onlyItemCheckoutStart = false): Collection;

    /**
     * Get only canceled items.
     *
     * @return Collection<OrderItemInterface>
     */
    public function getItemsCanceled(): Collection;

    /**
     * Get all items filterable with checkoutStart only items.
     *
     * @return Collection<OrderItemInterface>
     */
    public function getItemsWithCanceled($onlyItemCheckoutStart = false): Collection;

    /**
     * Check seller.
     *
     * @return bool
     */
    public function isSeller(CompanyInterface $company = null);

    /**
     * Check buyer.
     *
     * @return bool
     */
    public function isBuyer(CompanyInterface $company = null);

    /**
     * Get CreatebBy type.
     *
     * @return string
     */
    public function getCreatedBy();

    public function getUpdatedBy();

    public function hasItems(): bool;

    public function getBillingAddress(): ?AddressInterface;

    /**
     * Get total before modifications.
     *
     * @return int
     */
    public function getOriginalTotal();

    /**
     * Set total before modifications.
     */
    public function setOriginalTotal($originalTotal): self;

    public function setTotal($total): self;

    public function getCheckoutStart();

    public function setCheckoutStart($checkoutStart): self;

    public function getToken();

    public function countItems();

    public function setCreatedBy(string $createdBy): self;

    public function setType(string $type): self;

    public function setDate(?\DateTime $date): self;

    public function getDate();

    public function getExternalId(): ?string;

    public function setExternalId(?string $externalId): self;

    public function setCompany(CompanyInterface $company): self;

    public function setUser(UserInterface $user): self;

    public function setCompanyReceiver(CompanyInterface $companyReceiver): self;

    public function setUserReceiver(UserInterface $userReceiver): self;

    public function setSellerAddress(?AddressInterface $address): self;

    public function setShippingAddress(?AddressInterface $address): self;

    public function setBillingAddress(?AddressInterface $address): self;

    /**
     * @return ShipmentInterface[]|Collection
     */
    public function getShipments();

    public function hasShipments(): bool;

    public function getNumberInvoice(): ?NumberInterface;

    public function getOther(CompanyInterface $company);

    public function getInvoiceDate(): ?\DateTime;

    public function addNumber(NumberInterface $number);

    public function setProposal(TenderProposalInterface $proposal): self;

    public function setCart(?CartInterface $cart): self;

    public function getCart(): ?CartInterface;

    public function getPromotion(): ?OrderPromotionInterface;

    public function setPromotion(?OrderPromotionInterface $promotion): self;

    /**
     * @return Collection<PromotionCodeInterface>
     */
    public function getPromotionCodes(): Collection;

    /**
     * Add several promotion codes.
     */
    public function addPromotionCodes(iterable $promotionCodes): self;

    /**
     * Add promotionCode.
     */
    public function addPromotionCode(PromotionCodeInterface $promotionCode): self;

    /**
     * Remove promotionCode.
     */
    public function removePromotionCode(PromotionCodeInterface $promotionCode): self;

    /**
     * Check has promotionCode.
     */
    public function hasPromotionCode(PromotionCodeInterface $promotionCode): bool;

    /**
     * Check has promotionCode by apply on.
     */
    public function hasPromotionCodeByApplyOn(string $applyOn): bool;

    /**
     * Check has promotionCodes.
     */
    public function hasPromotionCodes(): bool;

    public function getShippingPromotion(): ?OrderPromotionInterface;

    public function setShippingPromotion(OrderPromotionInterface $shippingPromotion = null): self;

    /**
     * @return CouponInterface
     */
    public function getCoupon();

    public function setCoupon(CouponInterface $coupon = null): self;

    public function calculateItemsTotal();

    public function getItemsTotalExcludingTaxes();

    public function setItemsTotalExcludingTaxes($itemsTotalExcludingTaxes): OrderInterface;

    public function calculateItemsTotalExcludingTaxes(): OrderInterface;

    public function getItemsTotalExcludingPromotionsExcludingTaxes();

    public function getItemsTotalExcludingPromotions();

    public function hasRentRangeItem(): bool;

    public function getItemsTotal();

    public function calculateTotal(): OrderInterface;

    public function getSellerAddress(): ?AddressInterface;

    public function getShippingAddress(): ?AddressInterface;

    public function getShipmentByShippingMethod(?ShippingMethodInterface $shippingMethod, bool $getKey = false, string $gateway = ShippingMethodGatewayInterface::DEFAULT);

    public function getOrderRecurring(): ?OrderRecurringInterface;

    public function setPaymentState(?string $paymentState): self;

    public function setToken($token): self;

    public function addShipment(ShipmentInterface $shipment): self;

    public function getNumberRecurring(): ?NumberInterface;

    public function addItem(OrderItemInterface $item): self;

    public function hasItem(OrderItemInterface $item): bool;

    public function removeItem(OrderItemInterface $item);

    public function getShipmentItemByVariant(VariantInterface $variant);

    public function getOrderCancel();

    public function setOrderCancel(EntityCancelInterface $orderCancel): self;

    public function getCanceledTo();

    /**
     * @return TermsOfServiceInterface|null
     */
    public function getTos();

    /**
     * @param TermsOfServiceInterface|null $tos
     */
    public function setTos($tos): self;

    public function getItemsByProduct();

    public function getItemByTrackingCode(string $trackingCode, string $trackingCodeName): ?OrderItemInterface;

    public function getTaxByRate(TaxRateInterface $taxRate);

    /**
     * @return Collection<TaxInterface>
     */
    public function getTaxes(): Collection;

    public function setTaxes(Collection $taxes): self;

    public function addTax(TaxInterface $tax): self;

    public function removeTax(TaxInterface $tax): self;

    public function getAbortedBy();

    public function setAbortedBy($abortedBy): self;

    public function getFormattedTotal();

    public function getShippedAt(): ?\DateTime;

    public function setShippedAt(?\DateTime $shippedAt): self;

    public function getCompletedAt();

    public function getExpiresAt(): ?\DateTime;

    public function setExpiresAt(?\DateTime $expiresAt): self;

    public function getOrderedAt(): ?\DateTime;

    public function getCartConfirmedAt();

    public function setCartConfirmedAt(?\DateTime $cartConfirmedAt): self;

    public function cartConfirme();

    public function setLastState(string $lastState): self;

    public function complete();

    public function resetShipment();

    public function removeShipment(ShipmentInterface $shipment);

    /**
     * @return OrderItemInterface|int|null
     */
    public function getItemByVariant(VariantInterface $variant, $getKey = false): ShipmentItemInterface|OrderItemInterface|int|null;

    public function getUser();

    public function setOrderedAt(?\DateTime $orderedAt): self;

    public function isSubOrder(): bool;

    public function isReadyToShip(): bool;

    public function getOriginOrder(): ?OrderInterface;

    public function setOriginOrder(?self $originOrder): self;

    /**
     * Sets the given state to the order shipments.
     */
    public function setShippingStateForAllShipments(string $state): self;

    public function getNumberByType(string $type): ?NumberInterface;

    public function setNumberByType(string $type, NumberInterface $number = null): self;

    public function getNumberCart(): ?NumberInterface;

    public function getNumberQuote(): ?NumberInterface;

    public function setNumberCart(?NumberInterface $numberCart): self;

    public function isRefused(): bool;

    public function isCanceled(): bool;

    public function isConfirmed(): bool;

    /**
     * Returns the user account department if available.
     *
     * @return DepartmentInterface|null
     */
    public function getUserAccountDepartment();

    /**
     * Returns all product items categories.
     * Duplicates are removed.
     *
     * @param bool $stringify if true, returns all items final categories (i.e: without children) prefixed by the parent categories with ":"
     *                        e.g. with 3 items: PRODUITS SIGLES;PRODUITS SIGLES:Objet publicitaire;PRODUITS SIGLES:Enseigne et pré enseigne:Enseigne
     *
     * @return array|string
     */
    public function getProductItemsCategories(bool $stringify = false, string $stringSeparator = ',');

    public function getProductItemsCategoriesIds(): array;

    public function hasForcePaymentOutside(): bool;

    public function getSupplierCompany(): ?CompanyInterface;

    public function setSupplierCompany(CompanyInterface $supplierCompany): self;

    public function getSupplierOrder(): ?self;

    public function setSupplierOrder(self $supplierOrder): self;

    public function isDropshipped(): bool;

    /**
     * Get total to check capping.
     *
     * @return int
     */
    public function getCappingBaseTotal();

    /**
     * Set total to check capping.
     */
    public function setCappingBaseTotal($cappingBaseTotal): self;

    public function getTotalExcludingTaxes();

    public function getFilePath(): ?string;

    public function setFilePath(string $filePath = null): self;

    public function getContract(): ?ContractInterface;

    public function setContract(ContractInterface $contract = null): self;

    /**
     * Returns the rental item with the ealier "rent from" date.
     *
     * @return bool|OrderItemInterface
     */
    public function getEarlierRentFromItem(): ?OrderItemInterface;

    /**
     * Returns the rental item with the later "rent to" date.
     *
     * @return bool|OrderItemInterface
     */
    public function getLaterRentToItem(): ?OrderItemInterface;

    public function isCartConfirmed();

    /**
     * Returns the adjustment amount according to the given PromotionCode object.
     */
    public function getAdjustmentAmountByPromotionCode(PromotionCodeInterface $promotionCode, array $elements = [], bool $onlyItemCheckoutStart = false): ?int;

    public function getNumberBuyer(): ?NumberInterface;

    public function setNumberBuyer(NumberInterface $numberBuyer): self;

    public function getApprovalOriginOrder(): ?self;

    public function setApprovalOriginOrder(OrderInterface $approvalOriginOrder): self;

    public function getCompanyNotificationOwner(): ?NotificationOwnerInterface;

    public function isSupplierOrder(): bool;

    public function getTotalsVariableUnitsPerItem(): array;

    public function hasItemWithVariableQuantity(): bool;

    public function getItemsAdjustments(): array;

    public function hasCreatedBySeller(): bool;

    public function hasCreatedByBuyer(): bool;

    public function isExternal(): bool;

    public function setIsExternal(bool $isExternal): self;

    public function getLastState(): ?string;

    public function setExpiredAt(?\DateTime $expiredAt): self;

    public function getOrderType(): ?string;

    public function getOrderApproval(): ?OrderApprovalInterface;

    public function setOrderApproval(OrderApprovalInterface $orderApproval): self;

    /**
     * Check if the order approval is accepted.
     */
    public function isAcceptedByOperator(): bool;

    /**
     * Check if the order is concerned by the order approval process.
     * It doesn't check if a request is possible right now. To perform this check use ShopAuthorizer::canRequestOrderApproval().
     */
    public function isApprovable(): bool;

    public function getPaymentMethod(): ?PaymentMethodInterface;

    public function getCartPaymentMethod(): ?PaymentMethodInterface;

    /**
     * Check if all items are canceled or not.
     *
     * @return bool true if all items are canceled
     */
    public function hasAllItemsCanceled(): bool;

    public function getNote(): ?string;

    public function hasOnlyNonPayableItems(bool $onlyItemCheckoutStart = false): bool;

    public function getExportedAt(): ?\DateTime;

    public function setExportedAt(?\DateTime $exportedAt): self;
}

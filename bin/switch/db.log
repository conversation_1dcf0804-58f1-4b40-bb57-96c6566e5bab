
-------
[<PERSON><PERSON>] mysql -u root -puppler -e 'UPDATE configuration SET cookie_samesite = NULL;' madewis_prod
-------
[CMD] mysql -u root -puppler -e 'UPDATE configuration SET content_security_policy = NULL;' madewis_prod
-------
[<PERSON><PERSON>] mysql -u root -puppler -e 'UPDATE data_exporter_configuration SET status = "locked";' madewis_prod
-------
[CMD] mysql -u root -puppler -e 'UPDATE data_exporter_configuration SET status = "locked";' madewis_prod
-------
[C<PERSON>] /var/www/bin/console fos:user:change-password <EMAIL> test
Changed password <NAME_EMAIL>
-------
[CMD] mysql -u root -puppler -e 'PURGE BINARY LOGS BEFORE NOW()' madewis_prod

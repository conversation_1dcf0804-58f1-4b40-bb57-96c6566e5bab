import UpplerFileUpload from '../modules/fileupload';
import '../../css/frontoffice/shop.scss';

var $twigData = $('#twig-data-shop-config');

$(function () {
    $(document).on('click', '.widget-shop-gallery .remove-picture', function (e) {
        e.preventDefault();

        $(this).parents('.image').remove();
        $.ajax({ url: $(this).attr('href') });
        $('#blueimp-gallery').hide();
    });

    var GalleryUploader = new UpplerFileUpload($('.widget-shop-gallery-container'));
    GalleryUploader.initFileUpload(
        {
            success: function (response, $element) {
                var totalImages = $('.widget-shop-gallery .image').length,
                    html =
                        '<div class="image"><img src="' +
                        response.result.files.name +
                        '" /><a  href="' +
                        $twigData.data('delete-image-path') +
                        '/' +
                        response.result.files.id +
                        '" class="remove-picture"><span class="fa fa-times"></span></a><input type="text" class="image-name" /><input type="text" name="uppler_shop_configuration[images][' +
                        totalImages +
                        '][name]" class="image-name" value="" placeholder="' +
                        $twigData.data('image-placeholder') +
                        '" /><input type="hidden" name="uppler_shop_configuration[images][' +
                        totalImages +
                        '][path]" class="image-path" value="' +
                        response.result.files.path +
                        '"/></div>';
                $('.widget-shop-gallery').prepend(html);
            },
        },
        '#images-input',
        null,
        $('.widget-shop-gallery'),
    );
});

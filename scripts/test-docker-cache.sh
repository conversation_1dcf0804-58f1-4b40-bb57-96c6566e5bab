#!/bin/bash

echo "Testing Symfony cache clear in Docker..."
echo "========================================"

# Test if Docker container is running
if ! docker ps | grep -q "php"; then
    echo "✗ Docker container 'php' is not running"
    echo "Please start your Docker environment first"
    exit 1
fi

echo "✓ Docker container 'php' is running"

# Test cache clear
echo "Clearing Symfony cache..."
docker exec php php bin/console cache:clear --env=dev

if [ $? -eq 0 ]; then
    echo "✓ Cache cleared successfully"
else
    echo "✗ Cache clear failed"
    echo "Trying alternative approach..."
    
    # Alternative: remove cache directory
    docker exec php rm -rf var/cache/dev
    docker exec php php bin/console cache:warmup --env=dev
    
    if [ $? -eq 0 ]; then
        echo "✓ Cache cleared using alternative method"
    else
        echo "✗ Cache clear still failing"
        exit 1
    fi
fi

# Test if we can access the application
echo "Testing application access..."
if curl -s -o /dev/null -w "%{http_code}" http://uppler.local | grep -q "200\|302"; then
    echo "✓ Application is accessible"
else
    echo "⚠ Application may not be fully accessible yet"
fi

echo
echo "✓ Ready to test payment logging!"
echo "Next steps:"
echo "1. Go to http://uppler.local"
echo "2. Monitor logs: docker exec php tail -f var/logs/payment_debug.log"
echo "3. Reproduce the cart-to-order bug"

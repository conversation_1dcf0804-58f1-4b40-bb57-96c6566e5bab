#!/bin/bash

echo "==================================================="
echo "Testing Cart-to-Order Bug Logging in Docker"
echo "==================================================="
echo

# Test 1: Clear cache
echo "1. Clearing Symfony cache..."
docker exec -it php php bin/console cache:clear --env=dev
if [ $? -eq 0 ]; then
    echo "✓ Cache cleared successfully"
else
    echo "✗ Cache clear failed"
    exit 1
fi
echo

# Test 2: Test logging configuration
echo "2. Testing logging configuration..."
docker exec -it php php scripts/test-payment-logging.php
echo

# Test 3: Check if services are properly configured
echo "3. Checking service configuration..."
docker exec -it php php bin/console debug:container | grep -i "payment\|bank\|order" | head -10
echo

# Test 4: Check monolog configuration
echo "4. Checking monolog configuration..."
docker exec -it php php bin/console debug:config monolog | grep -A 10 -B 5 payment
echo

# Test 5: Create test log entry
echo "5. Creating test log entry..."
docker exec -it php bash -c 'echo "[PAYMENT_DEBUG] Docker test entry - $(date)" >> var/logs/payment_debug.log'
echo "✓ Test log entry created"
echo

# Test 6: Show recent log entries
echo "6. Recent log entries:"
echo "====================="
docker exec -it php tail -5 var/logs/payment_debug.log
echo

echo "==================================================="
echo "Logging system ready for bug reproduction!"
echo "==================================================="
echo
echo "To monitor logs in real-time:"
echo "docker exec -it php tail -f var/logs/payment_debug.log | grep '[PAYMENT_DEBUG]'"
echo
echo "To reproduce the bug:"
echo "1. Go to http://uppler.local"
echo "2. Follow the reproduction steps from docs/cart-to-order-transition-bug.md"
echo "3. Monitor the logs for the critical failure point"
echo

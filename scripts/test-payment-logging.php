<?php

/**
 * Test script to verify payment logging is working correctly
 *
 * Usage: docker exec -it php php scripts/test-payment-logging.php
 *
 * This script will:
 * 1. Test that the payment logger is properly configured
 * 2. Write test log entries to verify the log file is created
 * 3. Check that the log format is correct
 */

// Simple test without full Symfony bootstrap
echo "Testing Payment Logging Configuration\n";
echo "=====================================\n\n";

// Check if log directory exists
$logDir = __DIR__ . '/../var/logs';
if (!is_dir($logDir)) {
    echo "✗ Log directory not found: $logDir\n";
    exit(1);
}
echo "✓ Log directory exists: $logDir\n";

// Check if log directory is writable
if (!is_writable($logDir)) {
    echo "✗ Log directory is not writable: $logDir\n";
    exit(1);
}
echo "✓ Log directory is writable\n";

// Test writing to payment debug log
$logFile = $logDir . '/payment_debug.log';
$testMessage = "[PAYMENT_DEBUG] Test log entry - " . date('Y-m-d H:i:s') . " - Payment logging system test\n";

if (file_put_contents($logFile, $testMessage, FILE_APPEND | LOCK_EX) === false) {
    echo "✗ Failed to write to payment debug log: $logFile\n";
    exit(1);
}
echo "✓ Successfully wrote test entry to payment debug log\n";

// Check if log file exists and show recent entries
if (file_exists($logFile)) {
    echo "✓ Payment debug log file exists: $logFile\n";

    // Show last few lines if file has content
    if (filesize($logFile) > 0) {
        $lines = file($logFile);
        $lastLines = array_slice($lines, -3);

        echo "\nLast 3 log entries:\n";
        echo "==================\n";
        foreach ($lastLines as $line) {
            echo $line;
        }
    }
} else {
    echo "ℹ Payment debug log file will be created on first use: $logFile\n";
}

// Check monolog configuration
$configFile = __DIR__ . '/../app/config/config_dev.yml';
if (file_exists($configFile)) {
    $configContent = file_get_contents($configFile);
    if (strpos($configContent, 'payment:') !== false) {
        echo "✓ Payment logger configuration found in config_dev.yml\n";
    } else {
        echo "✗ Payment logger configuration not found in config_dev.yml\n";
    }
} else {
    echo "✗ Configuration file not found: $configFile\n";
}

// Check service configuration
$serviceFile = __DIR__ . '/../app/config/services/provider.yaml';
if (file_exists($serviceFile)) {
    $serviceContent = file_get_contents($serviceFile);
    if (strpos($serviceContent, 'monolog.logger.payment') !== false) {
        echo "✓ Bank Provider service configured with payment logger\n";
    } else {
        echo "✗ Bank Provider service not configured with payment logger\n";
    }
} else {
    echo "✗ Service configuration file not found: $serviceFile\n";
}

echo "\n✓ Payment logging system configuration test completed!\n";
echo "\nNext steps:\n";
echo "1. Run: docker exec -it php php bin/console cache:clear\n";
echo "2. Reproduce the cart-to-order bug while monitoring logs\n";
echo "3. Monitor logs: docker exec -it php tail -f var/logs/payment_debug.log\n";
echo "4. Filter logs: docker exec -it php grep '[PAYMENT_DEBUG]' var/logs/payment_debug.log\n";

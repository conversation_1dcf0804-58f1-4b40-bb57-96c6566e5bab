# Cart-to-Order Transition Bug

## Problem Summary

**Critical Issue**: Orders paid by bank transfer ("Virement") remain stuck in cart status ("Panier") instead of transitioning to order status, preventing order fulfillment and causing revenue loss.

## Impact

- Orders stuck in cart status are not processed by the client (<PERSON><PERSON><PERSON>)
- Orders are never sent to the Sage ERP connector
- Customer experience is degraded
- Revenue is lost as legitimate orders cannot be fulfilled

## Bug Details

### Affected Versions
- v4.101.0
- v4.100.1  
- v4.95.3

This indicates the bug is not a recent regression but has existed across multiple versions.

### Symptoms
- Cart entities show `type='cart'` instead of `type='order'` after payment confirmation
- Some cart entities incorrectly show credit card payment method despite being paid by bank transfer
- Order history shows correct transitions but database entity doesn't update
- `completedAt` and `cartConfirmedAt` timestamps are not set

## Reproduction Steps

1. Create an order and initiate credit card payment
2. On the credit card payment page, navigate back to the previous page
3. Switch to bank transfer payment method
4. On the order management page (`/fr/shop-manage/orders/t/{order_token}`), change payment method from bank transfer back to credit card
5. Submit modifications (order transitions to "uppler.cart.state.pending_explicit")
6. Finalize/confirm the order
7. **Result**: Status becomes "confirmed" but type remains "cart" instead of "order"

## Root Cause Analysis

### Technical Flow
The cart-to-order transition should happen in this sequence:

1. **Cart Confirmation**: `CartProvider::confirmCart()` dispatches `UpplerCartEvents::POST_CONFIRM`
2. **State Resolution**: `OrderStateListener::resolveOrderStateAfterCartConfirmation()` processes the event
3. **Pending State**: Calls `processOrderPending()` → `doResolveOrderStates()` with `STATE_PENDING`
4. **Cart Confirmation**: `OrderStateListener` calls `order->cartConfirme()` when state changes to pending
5. **Type Transition**: `Order::cartConfirme()` method sets `type='order'` and `cartConfirmedAt` timestamp

### Critical Failure Point
The bug occurs during the **Doctrine flush operation** in `src/AppBundle/Provider/Bank/Provider.php` at line 594-597:

```php
public function updatePayment(PaymentInterface $payment): PaymentInterface
{
    // ... entity modifications ...
    $this->entityManager->persist($payment);
    $this->entityManager->flush(); // <- FAILS HERE
    return $payment;
}
```

### Root Cause Hypothesis
**Doctrine Entity State Management Conflict**: The issue appears to be related to concurrent entity modifications during payment processing:

1. **Payment Method Switching**: Multiple payment method changes create inconsistent entity states
2. **Event Listener Chain**: Multiple event listeners modify the same entities simultaneously
3. **Flush Conflicts**: The `updatePayment()` method tries to flush changes but encounters conflicting entity states
4. **Transaction Rollback**: When flush fails, the entire transaction rolls back, leaving orders in cart state

## Key Code Locations

### Cart-to-Order Transition Logic
- **Entity Method**: `src/AppBundle/Entity/Order.php::cartConfirme()` (lines 1046-1056)
- **Event Listener**: `src/AppBundle/EventListener/Shop/OrderStateListener.php::doResolveOrderStates()` (line 61)
- **Cart Provider**: `src/AppBundle/Provider/Shop/CartProvider.php::confirmCart()` (lines 1586-1588)

### Failure Point
- **Bank Provider**: `src/AppBundle/Provider/Bank/Provider.php::updatePayment()` (lines 594-597)
- **Event Configuration**: `app/config/services/event_listener.yaml` (line 624)

## Debugging Implementation

### Enhanced Logging
Comprehensive logging has been added to `src/AppBundle/Provider/Bank/Provider.php::updatePayment()` method:

- **Payment State Tracking**: Logs payment ID, referer ID, state changes
- **Order State Monitoring**: Logs order type, state, timestamps before payment update
- **Entity Change Detection**: Logs Doctrine entity change sets
- **Flush Operation Monitoring**: Detailed logging around the critical flush operation
- **Error Capture**: Full exception details including stack traces and entity manager state

### Log File Location
- **Development**: `var/logs/payment_debug.log`
- **Search Pattern**: `[PAYMENT_DEBUG]` prefix for easy filtering

### Key Log Entries to Monitor
- `Starting updatePayment` - Initial payment state
- `Order state before payment update` - Order entity state
- `CRITICAL: Entity manager flush failed` - The actual failure point
- `Entity manager unit of work state` - Doctrine internal state during failure

## Manual Workaround

For stuck orders, execute this SQL to manually fix the state:

```sql
UPDATE uppler_order 
SET type='order',
    state='confirmed',
    last_state='pending',
    completed_at=updated_at,
    cart_confirmed_at=updated_at,
    shipping_state='preparation'
WHERE id = {order_id};
```

**Note**: This bypasses Doctrine and directly updates the database, confirming the issue is in the ORM layer.

## Investigation Next Steps

1. **Reproduce the Bug**: Follow reproduction steps while monitoring `payment_debug.log`
2. **Analyze Logs**: Look for entity state conflicts and flush failure details
3. **Entity Manager State**: Examine unit of work state during failures
4. **Transaction Boundaries**: Review transaction isolation during payment processing
5. **Event Listener Ordering**: Check for concurrent entity modifications

## Potential Solutions

1. **Entity Refresh**: Refresh entities before critical operations to ensure clean state
2. **Transaction Management**: Implement proper transaction boundaries around payment processing  
3. **Event Listener Priorities**: Adjust event listener priorities to prevent conflicts
4. **State Validation**: Add validation to ensure entities are in expected states before flush
5. **Optimistic Locking**: Implement version-based conflict resolution

## Environment
- **Local Development**: uppler.local (dev mode)
- **Docker Container**: "php" container for code execution
- **Framework**: Symfony-based application with Doctrine ORM

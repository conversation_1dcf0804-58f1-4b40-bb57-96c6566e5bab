# Dépannage du Système de Logging

## Erreurs Courantes et Solutions

### 1. Erreur: "The logging channel 'payment' assigned to the 'payment' handler does not exist"

**Cause**: Le canal de logging "payment" n'est pas déclaré dans la configuration Monolog.

**Solution**: Vérifiez que `app/config/config_dev.yml` contient :
```yaml
monolog:
    channels: ['payment']
    handlers:
        # ... autres handlers
        payment:
            type: stream
            path: '%kernel.logs_dir%/payment_debug.log'
            level: debug
            channels: ['payment']
```

### 2. Erreur: "You have requested a non-existent service 'monolog.logger.payment'"

**Cause**: Le service logger n'est pas correctement configuré ou le cache n'a pas été vidé.

**Solutions**:
1. Vider le cache : `docker exec php php bin/console cache:clear --env=dev`
2. Vérifier la configuration dans `app/config/services/provider.yaml`
3. Redémarrer les conteneurs Docker si nécessaire

### 3. Erreur: "Extension DOM is required"

**Cause**: L'extension PHP DOM n'est pas installée dans l'environnement local.

**Solution**: Exécuter les commandes dans le conteneur Docker :
```bash
# Au lieu de : php bin/console cache:clear
# Utiliser : 
docker exec php php bin/console cache:clear --env=dev
```

### 4. Application inaccessible après modification

**Cause**: Erreur de configuration ou cache corrompu.

**Solutions**:
1. Vider complètement le cache :
   ```bash
   docker exec php rm -rf var/cache/dev
   docker exec php php bin/console cache:warmup --env=dev
   ```

2. Vérifier les logs d'erreur :
   ```bash
   docker exec php tail -f var/logs/dev.log
   ```

3. Redémarrer les conteneurs :
   ```bash
   docker-compose restart
   ```

## Scripts de Test

### Test de Configuration Rapide
```bash
./scripts/test-docker-cache.sh
```

### Test de Logging Complet
```bash
./scripts/docker-test-logging.sh
```

### Test Manuel
```bash
# 1. Vérifier que le conteneur fonctionne
docker ps | grep php

# 2. Tester l'accès au conteneur
docker exec php php --version

# 3. Vérifier la configuration Symfony
docker exec php php bin/console debug:config monolog

# 4. Tester l'écriture de logs
docker exec php bash -c 'echo "[TEST] $(date)" >> var/logs/payment_debug.log'

# 5. Vérifier le fichier de log
docker exec php cat var/logs/payment_debug.log
```

## Vérifications de Configuration

### 1. Fichiers Modifiés
Vérifiez que ces fichiers contiennent les bonnes modifications :

- `app/config/config_dev.yml` - Configuration Monolog
- `app/config/services/provider.yaml` - Injection du logger dans Bank Provider
- `app/config/services/event_listener.yaml` - Injection du logger dans OrderStateListener
- `src/AppBundle/Provider/Bank/Provider.php` - Code de logging ajouté
- `src/AppBundle/EventListener/Shop/OrderStateListener.php` - Code de logging ajouté

### 2. Permissions
```bash
# Vérifier les permissions du répertoire de logs
docker exec php ls -la var/logs/

# Corriger les permissions si nécessaire
docker exec php chmod 755 var/logs/
docker exec php chmod 644 var/logs/*.log
```

### 3. Services
```bash
# Vérifier que les services sont bien configurés
docker exec php php bin/console debug:container | grep -i payment
docker exec php php bin/console debug:container AppBundle\\Provider\\Bank\\Provider
docker exec php php bin/console debug:container AppBundle\\EventListener\\Shop\\OrderStateListener
```

## Restauration en Cas de Problème

Si les modifications causent des problèmes, vous pouvez les annuler :

### 1. Restaurer la Configuration Monolog
```bash
# Supprimer la section payment de app/config/config_dev.yml
# Garder seulement :
monolog:
    handlers:
        main:
            type: stream
            path: '%kernel.logs_dir%/%kernel.environment%.log'
            level: notice
            channels: '!event'
        console:
            type: console
            process_psr_3_messages: false
            channels: ['!event', '!doctrine', '!console']
```

### 2. Restaurer les Services
Supprimer les références à `@monolog.logger.payment` dans :
- `app/config/services/provider.yaml`
- `app/config/services/event_listener.yaml`

### 3. Vider le Cache
```bash
docker exec php php bin/console cache:clear --env=dev
```

## Contact et Support

Si vous rencontrez des problèmes persistants :

1. Vérifiez les logs d'erreur : `docker exec php tail -f var/logs/dev.log`
2. Consultez la documentation Symfony 3.x pour Monolog
3. Vérifiez que tous les conteneurs Docker fonctionnent correctement

## État Actuel

✅ **Configuration corrigée** : Canal "payment" ajouté à la configuration Monolog
✅ **Scripts de test** : Disponibles pour vérifier la configuration
✅ **Documentation** : Guide de dépannage complet

**Prêt pour** : Test de la configuration et reproduction du bug

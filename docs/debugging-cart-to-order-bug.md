# Debugging Cart-to-Order Transition Bug

## Quick Start

### 1. Test Logging Setup
```bash
# Verify logging is working
php scripts/test-payment-logging.php

# Monitor payment logs in real-time
tail -f var/logs/payment_debug.log | grep '[PAYMENT_DEBUG]'
```

### 2. Reproduce the Bug
Follow the reproduction steps from `docs/cart-to-order-transition-bug.md` while monitoring the logs.

### 3. Key Log Patterns to Watch

#### Successful Payment Flow
```
[PAYMENT_DEBUG] Starting updatePayment
[ORDER_STATE_DEBUG] Order transitioning to PENDING state - calling cartConfirme()
[ORDER_STATE_DEBUG] cartConfirme() method called successfully
[PAYMENT_DEBUG] Entity manager flush completed successfully
```

#### Failed Payment Flow (Bug Manifestation)
```
[PAYMENT_DEBUG] Starting updatePayment
[ORDER_STATE_DEBUG] Order transitioning to PENDING state - calling cartConfirme()
[ORDER_STATE_DEBUG] cartConfirme() method called successfully
[PAYMENT_DEBUG] CRITICAL: Entity manager flush failed
```

## Log Analysis

### Critical Log Entries

1. **`Starting updatePayment`** - Initial payment state
   - Check `current_state`, `payment_method`, `referer_id`

2. **`Order state before payment update`** - Order entity state
   - Verify `order_type`, `order_state`, `cart_confirmed_at`

3. **`Order transitioning to PENDING state`** - State transition attempt
   - Monitor `current_type` (should be 'cart'), `target_state` (should be 'pending')

4. **`cartConfirme() method called successfully`** - Type transition
   - Verify `new_type` (should be 'order'), `cart_confirmed_at_after` (should be set)

5. **`CRITICAL: Entity manager flush failed`** - The actual failure
   - Examine `error_message`, `entity_changes`, `stack_trace`

### Debugging Commands

```bash
# Filter payment debug logs only
grep '[PAYMENT_DEBUG]' var/logs/payment_debug.log

# Filter order state debug logs only  
grep '[ORDER_STATE_DEBUG]' var/logs/payment_debug.log

# Find failed flush operations
grep 'CRITICAL: Entity manager flush failed' var/logs/payment_debug.log

# Monitor specific order ID
grep 'order_id.*123' var/logs/payment_debug.log

# Real-time monitoring during bug reproduction
tail -f var/logs/payment_debug.log | grep -E '\[PAYMENT_DEBUG\]|\[ORDER_STATE_DEBUG\]'
```

## Investigation Checklist

### Before Reproducing the Bug
- [ ] Verify logging is working: `php scripts/test-payment-logging.php`
- [ ] Clear existing logs: `> var/logs/payment_debug.log`
- [ ] Start log monitoring: `tail -f var/logs/payment_debug.log`

### During Bug Reproduction
- [ ] Note the order/cart ID being tested
- [ ] Follow exact reproduction steps
- [ ] Monitor logs for the critical failure point
- [ ] Capture the complete log sequence for the affected order

### After Bug Reproduction
- [ ] Check if `cartConfirme()` was called successfully
- [ ] Verify if the flush operation failed
- [ ] Examine entity change sets for conflicts
- [ ] Review unit of work state during failure

## Expected Findings

### Hypothesis 1: Entity State Conflicts
Look for:
- Multiple entities being modified simultaneously
- Conflicting change sets in the entity manager
- Stale entity references

### Hypothesis 2: Transaction Boundary Issues
Look for:
- Nested transactions
- Uncommitted changes from previous operations
- Transaction rollback scenarios

### Hypothesis 3: Event Listener Conflicts
Look for:
- Multiple event listeners modifying the same entities
- Race conditions during event processing
- Incorrect event listener priorities

## Log File Locations

- **Development**: `var/logs/payment_debug.log`
- **Production**: Configure similar logging in production environment
- **Rotation**: Logs are rotated (max 10 files) to prevent disk space issues

## Troubleshooting

### If Logging Doesn't Work
1. Check service configuration: `app/config/services/provider.yaml`
2. Verify monolog configuration: `app/config/config_dev.yml`
3. Ensure log directory is writable: `var/logs/`
4. Clear cache: `php bin/console cache:clear`

### If Services Fail to Load
1. Check constructor dependencies in:
   - `src/AppBundle/Provider/Bank/Provider.php`
   - `src/AppBundle/EventListener/Shop/OrderStateListener.php`
2. Verify service arguments in:
   - `app/config/services/provider.yaml`
   - `app/config/services/event_listener.yaml`

## Next Steps After Diagnosis

Once you've identified the root cause through logging:

1. **Entity State Conflicts**: Implement entity refresh before critical operations
2. **Transaction Issues**: Add proper transaction boundaries
3. **Event Conflicts**: Adjust event listener priorities
4. **Doctrine Issues**: Consider optimistic locking or version-based conflict resolution

## Manual Testing Commands

```bash
# Test specific order ID
docker exec -it php php bin/console debug:container | grep -i payment

# Check event listeners
docker exec -it php php bin/console debug:event-dispatcher uppler.cart.post_confirm

# Verify database state
docker exec -it php php bin/console doctrine:query:sql "SELECT id, type, state, cart_confirmed_at FROM uppler_order WHERE id = YOUR_ORDER_ID"
```
